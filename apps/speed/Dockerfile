# Build stage
FROM node:22-slim AS base

# Set working directory
WORKDIR /app

# Install pnpm
RUN corepack enable && corepack prepare pnpm@latest --activate

# Copy root workspace files
COPY package.json pnpm-lock.yaml pnpm-workspace.yaml ./
COPY eslint.config.mjs .prettierrc.mjs ./
COPY vitest.shared.ts vitest.config.ts ./

# Copy app source
COPY apps/speed ./apps/speed

# Install dependencies
RUN pnpm install --frozen-lockfile --ignore-scripts

# Build stage
FROM base AS build
# Set working directory
WORKDIR /app
# Build the app
RUN pnpm --filter speed run build

# Production stage
FROM node:22-slim AS production

# Set working directory
WORKDIR /app

# Copy build artifacts
COPY --from=build /app/apps/speed/.output ./apps/speed/.output

# Set environment variables
ENV NODE_ENV=production
ENV PORT=3000

# Expose the port the app runs on
EXPOSE 3000

# Command to run the application
WORKDIR /app/apps/speed
CMD ["node", ".output/server/index.mjs"]