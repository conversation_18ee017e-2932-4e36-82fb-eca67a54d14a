# This file extends the root CI configuration with app-specific settings

stages:
  - build
  - test
  - deploy

before_script:
  - export BUILD_TAG="${CI_COMMIT_REF_NAME}_${CI_COMMIT_SHA}"
  - export BUILD_TAG=`echo $BUILD_TAG | sed -e 's/\//-/g'`
  - export SANITIZED_REF=`echo $CI_COMMIT_REF_NAME | sed -e 's/\//-/g'`

.speed-common:
  rules:
    - changes:
      - "**/apps/speed/*"
      when: always

# Build temporary Docker image for tests
speed-build-test:
  stage: build
  image: docker:stable
  services:
    - docker:dind
  variables:
    DOCKER_TLS_CERTDIR: "/certs"
    DOCKER_DRIVER: overlay2
  script:
    - docker login -u $CI_REGISTRY_USER -p $CI_JOB_TOKEN $CI_REGISTRY
    - docker pull $CI_REGISTRY_SPEED_IMAGE/test:$SANITIZED_REF || true
    # Build a test image cached from the recently build production image
    - |
      docker build . -f ./apps/speed/Dockerfile --target base \
        --cache-from $CI_REGISTRY_SPEED_IMAGE/test:$SANITIZED_REF \
        -t $CI_REGISTRY_SPEED_IMAGE/test:$CI_PIPELINE_ID \
        -t $CI_REGISTRY_SPEED_IMAGE/test:$SANITIZED_REF
    - docker push $CI_REGISTRY_SPEED_IMAGE/test:$CI_PIPELINE_ID
    - docker push $CI_REGISTRY_SPEED_IMAGE/test:$SANITIZED_REF
  extends: .speed-common

speed-build:
  stage: build
  image: docker:stable
  services:
    - docker:dind
  variables:
    DOCKER_TLS_CERTDIR: "/certs"
    DOCKER_DRIVER: overlay2
  script:
    - docker login -u $CI_REGISTRY_USER -p $CI_JOB_TOKEN $CI_REGISTRY
    - docker pull $CI_REGISTRY_SPEED_IMAGE:$SANITIZED_REF || true
    - |
      docker build . -f ./apps/speed/Dockerfile --target base \
        --cache-from $CI_REGISTRY_SPEED_IMAGE:$SANITIZED_REF \
        -t $CI_REGISTRY_SPEED_IMAGE:$CI_PIPELINE_ID \
        -t $CI_REGISTRY_SPEED_IMAGE:$SANITIZED_REF
    - docker push $CI_REGISTRY_SPEED_IMAGE:$CI_PIPELINE_ID
    - docker push $CI_REGISTRY_SPEED_IMAGE:$SANITIZED_REF
  extends: .speed-common

# Run linting
speed-lint:
  stage: test
  image: $CI_REGISTRY_SPEED_IMAGE/test:$CI_PIPELINE_ID
  script:
    - cd /app
    - pnpm --filter speed run lint
  extends: .speed-common

# Run tests
speed-unit-test:
  stage: test
  image: $CI_REGISTRY_SPEED_IMAGE/test:$CI_PIPELINE_ID
  script:
    - cd /app
    - pnpm --filter speed run test
  extends: .speed-common
