{"name": "@cardcastle/speed", "private": true, "sideEffects": false, "type": "module", "scripts": {"dev": "vinxi dev", "build": "vinxi build", "lint": "eslint ./", "start": "vinxi start", "test": "vitest run", "test:watch": "vitest"}, "dependencies": {"@tanstack/react-query": "^5.66.0", "@tanstack/react-query-devtools": "^5.66.0", "@tanstack/react-router": "^1.120.5", "@tanstack/react-router-devtools": "^1.120.5", "@tanstack/react-router-with-query": "^1.120.5", "@tanstack/react-start": "^1.120.5", "react": "^19.0.0", "react-dom": "^19.0.0", "redaxios": "^0.5.1", "tailwind-merge": "^2.6.0", "vinxi": "0.5.3"}, "devDependencies": {"@types/node": "^22.5.4", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "autoprefixer": "^10.4.20", "jsdom": "^22.1.0", "postcss": "^8.5.1", "tailwindcss": "^3.4.17", "typescript": "^5.7.2", "vite-plugin-checker": "^0.9.3", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.1.3"}}