/// <reference types="vitest" />
import { defineConfig } from '@tanstack/react-start/config';
import checker from 'vite-plugin-checker';
import tsConfigPaths from 'vite-tsconfig-paths';

export default defineConfig({
  tsr: {
    appDirectory: 'src',
  },
  vite: {
    resolve: {
      alias: {
        '@': '/src',
      },
    },
    plugins: [
      checker({
        eslint: {
          // for example, lint .ts and .tsx
          lintCommand: 'eslint "./**/*.{ts,tsx}"',
        },
        typescript: true,
        overlay: {
          initialIsOpen: false,
          position: 'br',
        },
      }),
      tsConfigPaths({
        projects: ['./tsconfig.json'],
      }),
    ],
  },
  server: {
    preset: 'node-server',
  },
});
