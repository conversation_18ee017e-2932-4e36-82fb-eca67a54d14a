openapi: 3.0.0
info:
  title: CardCastle API v3
  description: API to interact with the CardCastle Platform
  version: '1.0'
servers:
  - url: https://cardcastle.co/api/v3
    variables: {}
security:
  - OAuth2: []
paths:
  /collection/total:
    get:
      tags:
        - Collection
      summary: Get Collection Total
      description: Gets the total card count and value of the collection or current staged cards
      operationId: GetCollectionTotal
      parameters:
        - name: staged
          in: query
          required: true
          schema:
            type: boolean
        - name: game
          in: query
          required: false
          schema:
            type: string
            default: mtg
            enum:
              - mtg
              - poke
      security:
        - OAuth2:
            - user
        - cookieAuth: []
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Get Collection Total response
                required: &ref_143
                  - count
                  - value
                type: object
                properties: &ref_144
                  count:
                    type: integer
                    format: int32
                    example: 1375
                  value:
                    type: integer
                    format: int32
                    example: 500000
                  staged_count:
                    type: integer
                    format: int32
                    example: 2
                  staged_value:
                    type: integer
                    format: int32
                    example: 350
                example: &ref_145
                  count: 1375
                  value: 500000
                  staged_count: 2
                  staged_value: 350
  /collection/lookup:
    post:
      tags:
        - Collection
      summary: Lookup Collection by Name or Json ID
      description: |-
        Expands a group of cards owned by the user, either by printing or by name. Either of the parameters `json_id` or `name` must be present
        + json_ids (optional, object) - array of json_ids
        + names (optional, object) - array of card names
      operationId: LookupCollectionByNameOrJsonId
      deprecated: true
      security:
        - OAuth2:
            - user
        - cookieAuth: []
      requestBody:
        description: ''
        content:
          application/json:
            schema:
              title: Lookup Collection by Name or Json ID request
              required: &ref_146
                - json_ids
              type: object
              properties: &ref_147
                json_ids:
                  type: array
                  items:
                    type: string
                    example:
                      - ffc5a91331acb724f6edb432ac355f0cbccc69c6
                  description: ''
              example: &ref_148
                json_ids:
                  - ffc5a91331acb724f6edb432ac355f0cbccc69c6
        required: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Lookup Collection by Name or Json ID response
                required: &ref_149
                  - card_ids
                type: object
                properties: &ref_150
                  card_ids:
                    type: array
                    items:
                      type: integer
                      format: int32
                      example:
                        - 1
                        - 2
                        - 3
                        - 4
                    description: ''
                example: &ref_151
                  card_ids:
                    - 1
                    - 2
                    - 3
                    - 4
        '400':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: &ref_0
                  error: Error Message
  /collection/search:
    post:
      tags:
        - Collection
      summary: Search Collection
      description: New API with support for searching a collection. Same as `/api/v3/collection` with extra sorting features as well as support for new postgres json syntax
      operationId: SearchCollection1
      security:
        - OAuth2:
            - user
        - cookieAuth: []
      parameters:
        - name: quality
          in: query
          required: false
          schema: &ref_126
            type: string
            enum:
              - Damaged
              - Heavily Played
              - Lightly Played
              - Moderately Played
              - Near Mint
        - name: quantity
          in: query
          required: false
          schema: &ref_55
            type: object
            properties:
              min:
                type: number
              max:
                type: number
        - name: price
          in: query
          required: false
          schema: &ref_127
            type: object
            properties:
              min:
                type: number
              max:
                type: number
        - name: foil
          in: query
          required: false
          schema: &ref_128
            type: boolean
            enum:
              - true
              - false
        - name: tags
          in: query
          required: false
          schema: &ref_129
            type: object
            properties:
              with:
                type: array
                items:
                  type: string
              without:
                type: array
                items:
                  type: string
              exclude_unselected:
                type: boolean
              untagged:
                type: boolean
            description: 'Using `with: []` and `exclude_unselected: true` will exclude all cards which do not have at least one of the selected tags'
        - name: in_deck
          in: query
          required: false
          schema: &ref_130
            type: object
            properties:
              linked:
                type: boolean
              uuid:
                type: string
                description: Deck UUID, Optional
          description: Cards in collection need to be linked/not linked to a deck. If passed an optional uuid param, it will return only cards that are present/not present in a specific deck
        - name: language
          in: query
          required: false
          schema: &ref_131
            type: array
            items:
              type: string
              enum:
                - en
                - de
                - fr
                - it
                - es
                - pt
                - jp
                - cn
                - tw
                - ru
            example: '[''sp'', ''pt'']'
        - name: source
          in: query
          required: false
          schema: &ref_56
            type: string
            enum:
              - cardbot
              - app
              - manual
              - import
              - unknown
        - name: input_session
          in: query
          required: false
          schema: &ref_57
            type: string
            example: 7cfabcf0-438a-48f5-a4af-5b0f89f768a4
        - name: sort_by
          in: query
          required: false
          schema: &ref_58
            type: string
            default: release_date
            enum:
              - name
              - cmc
              - set_name
              - release_date
              - power
              - toughness
              - price
              - count
              - date (deprecated)
              - created_at
              - committed_at
              - collector_number
              - position
              - rarity
              - scan_confidence
            description: 'Note: Cannot `sort_by: ''collector_number''` when `group_by: ''name''`. Cannot `sort_by: position` unless `group_by: none`'
        - name: location
          in: query
          required: false
          schema: &ref_132
            type: string
            example: 7cfabcf0-438a-48f5-a4af-5b0f89f768a4
        - name: confidence
          in: query
          required: false
          schema: &ref_59
            type: object
            properties:
              min:
                type: number
              max:
                type: number
            description: Returns the confidence value of associated scan metadata
        - name: json_id
          in: query
          required: false
          schema: &ref_1
            type: string
            enum:
              - card_json_id
        - name: card_list
          in: query
          required: false
          schema: &ref_2
            type: string
            enum:
              - card_list_uuid
        - name: order
          in: query
          required: false
          schema: &ref_3
            type: string
            default: asc
            enum:
              - asc
              - desc
        - name: group_by
          in: query
          required: false
          schema: &ref_4
            type: string
            default: printing
            enum:
              - printing
              - name
              - none
        - name: query
          in: query
          required: false
          schema: &ref_5
            type: string
        - name: starts_with
          in: query
          required: false
          schema: &ref_6
            type: string
        - name: set_starts_with
          in: query
          required: false
          schema: &ref_7
            type: string
        - name: rules_text_filter
          in: query
          required: false
          schema: &ref_8
            type: string
            example: '''haste and trample'''
          description: Searches for the whole string, so it must wholly exist within the rules text.
        - name: oracle_text_filter
          in: query
          required: false
          schema: &ref_9
            type: string
            example: '''haste and trample'''
          description: Searches for the whole string, so it must wholly exist within the rules text.
        - name: per_page
          in: query
          required: false
          schema: &ref_10
            type: integer
            default: 100
            enum:
              - 100
              - 300
              - 600
        - name: page
          in: query
          required: false
          schema: &ref_11
            type: integer
            default: 1
        - name: colors
          in: query
          required: false
          schema: &ref_12
            type: object
            enum:
              - white: boolean
              - blue: boolean
              - black: boolean
              - red: boolean
              - green: boolean
              - colorless: boolean
              - multicolored: boolean
        - name: color_identity
          in: query
          required: false
          schema: &ref_13
            type: object
            enum:
              - white: boolean
              - blue: boolean
              - black: boolean
              - red: boolean
              - green: boolean
              - colorless: boolean
              - multicolored: boolean
        - name: types
          in: query
          required: false
          schema: &ref_14
            type: object
            properties:
              type:
                type: boolean
              multitype:
                type: boolean
          description: Accepted types can be retrieved from the `/types` route.
        - name: sub_types
          in: query
          required: false
          schema: &ref_15
            type: object
            properties:
              with:
                type: array
                items:
                  type: string
              without:
                type: array
                items:
                  type: string
        - name: super_types
          in: query
          required: false
          schema: &ref_16
            type: object
            properties:
              with:
                type: array
                items:
                  type: string
              without:
                type: array
                items:
                  type: string
        - name: set_names
          in: query
          required: false
          schema: &ref_17
            type: object
            properties:
              with:
                type: array
                items:
                  type: string
              without:
                type: array
                items:
                  type: string
        - name: artists
          in: query
          required: false
          schema: &ref_18
            type: object
            properties:
              with:
                type: array
                items:
                  type: string
              without:
                type: array
                items:
                  type: string
        - name: rarity
          in: query
          required: false
          schema: &ref_19
            type: array
            items:
              type: string
              enum:
                - mythic rare
                - rare
                - uncommon
                - common
                - special
                - basic land
        - name: power
          in: query
          required: false
          schema: &ref_20
            type: object
            properties:
              min:
                type: number
              max:
                type: number
        - name: toughness
          in: query
          required: false
          schema: &ref_21
            type: object
            properties:
              min:
                type: number
              max:
                type: number
        - name: loyalty
          in: query
          required: false
          schema: &ref_22
            type: object
            properties:
              min:
                type: number
              max:
                type: number
          description: 'The loyalty filter will only match planeswalkers with loyalty that is not an integer (eg: ''1d4+1'', ''*'', or ''X'') when min is 0.'
        - name: mana_value (converted mana cost)
          in: query
          required: false
          schema: &ref_23
            type: object
            properties:
              min:
                type: number
              max:
                type: number
        - name: format_legality
          in: query
          required: false
          schema: &ref_24
            type: string
            enum:
              - standard
              - modern
              - legacy
              - vintage
              - commander
              - tiny leaders
              - pauper
              - duel
              - brawl
              - oldschool
              - freeform
              - pioneer
              - premodern
        - name: set_types
          in: query
          required: false
          schema: &ref_25
            type: object
            properties:
              with:
                type: array
                items:
                  type: string
              without:
                type: array
                items:
                  type: string
        - name: is_reserved
          in: query
          required: false
          schema: &ref_26
            type: boolean
        - name: border
          in: query
          required: false
          schema: &ref_27
            type: string
            items:
              type: string
              enum:
                - black
                - white
                - silver
                - gold
                - borderless
            example: '[''black'', ''silver'']'
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: MTG Search Collection response
                type: object
                example: &ref_35
                  page_count: 8
                  page_value: 2300
                  total_groups: 4
                  collection_items:
                    - latest: '2017-01-24T03:21:20.260Z'
                      group_count: 1
                      group_price: 79.745994
                      card_users:
                        - id: 1170349
                          uuid: 64da69b8-c9cf-4cca-bd15-36270242db41
                          card_id: 29448
                          foil: false
                          quality: Near Mint
                          language: en
                          created_at: '2017-01-24T03:21:20.260Z'
                          source: app
                          name: forest
                          search_name: forest
                          set_name: invasion
                          release_date: '2016-11-16'
                          json_id: d8a61c60cad7e2b0510028ead4c61a236
                          set_code: INV
                          collector_number: 707
                          mana_cost: '{X}{B}{R}{G}'
                          types:
                            - Elite
                            - Planeswalker
                          sub_type:
                            - unde
                            - tempora
                            - totam
                          rarity: Uncommon
                          converted_mana_cost: 5
                          power: 10
                          toughness: 2
                          is_reserved: false
                          foreign_name: voluptatum quam
                          price: 2679
                          scanned_image_url: www.image.com.au/123456
                          scan_metadata:
                            confidence: 0.95
                            crop:
                              tl:
                                - 0.1
                                - 0.2
                              tr:
                                - 0.1
                                - 0.2
                              br:
                                - 0.1
                                - 0.2
                              bl:
                                - 0.1
                                - 0.2
                            flipped: true
                            mcr_version: 7.9.1
                            evermind_version: 0.6.2
  /collection/list:
    post:
      tags:
        - Collection
      summary: List Card IDs for Json IDs
      description: |-
        Expands a group of cards owned by the user by printing. This API
        is mainly for apps to use when deleting cards
        + json_ids (object) - array of json_id of the cards
        + foil (boolean, optional) - restrict cards with foil if required
      operationId: ListCardIdsForJsonIds
      security:
        - OAuth2:
            - user
        - cookieAuth: []
      requestBody:
        description: ''
        content:
          application/json:
            schema:
              title: List Card IDs for Json IDs request
              required: &ref_152
                - json_ids
                - foil
              type: object
              properties: &ref_153
                json_ids:
                  type: array
                  items:
                    type: string
                    example:
                      - 83c6088bc36d3ccd3b64e03a4383ac20e537349d
                      - ffc5a91331acb724f6edb432ac355f0cbccc69c6
                  description: ''
                foil:
                  type: boolean
                  example: true
              example: &ref_154
                json_ids:
                  - 83c6088bc36d3ccd3b64e03a4383ac20e537349d
                  - ffc5a91331acb724f6edb432ac355f0cbccc69c6
                foil: true
        required: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: List Card IDs for Json IDs response
                required: &ref_155
                  - card_maps
                type: object
                properties: &ref_156
                  card_maps:
                    type: array
                    items:
                      type: object
                      example:
                        - card_ids:
                            - 1
                            - 2
                            - 4
                          json_id: 83c6088bc36d3ccd3b64e03a4383ac20e537349d
                        - card_ids:
                            - 5
                            - 6
                            - 8
                          json_id: ffc5a91331acb724f6edb432ac355f0cbccc69c6
                    description: ''
                example: &ref_157
                  card_maps:
                    - card_ids:
                        - 1
                        - 2
                        - 4
                      json_id: 83c6088bc36d3ccd3b64e03a4383ac20e537349d
                    - card_ids:
                        - 5
                        - 6
                        - 8
                      json_id: ffc5a91331acb724f6edb432ac355f0cbccc69c6
        '400':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
  /collection/count:
    post:
      tags:
        - Collection
      summary: Count CardUsers for Name
      description: |-
        Shows number of CardUsers owned for a given list of names. This API
        is mainly for apps to use when scanning in Search Mode.
        + names (object) - array of names of the cards to match (must be exact)
        + foil (boolean, optional) - restrict cards with foil if required
      operationId: CountCardusersForName
      deprecated: true
      requestBody:
        description: ''
        content:
          application/json:
            schema:
              title: Count CardUsers for Name request
              required: &ref_158
                - names
                - foil
              type: object
              properties: &ref_159
                names:
                  type: array
                  items:
                    type: string
                    example:
                      - Card Name
                      - Test
                  description: ''
                foil:
                  type: boolean
                  example: true
              example: &ref_160
                names:
                  - Card Name
                  - Test
                foil: true
        required: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Count CardUsers for Name response
                required: &ref_161
                  - count_maps
                type: object
                properties: &ref_162
                  count_maps:
                    type: object
                    example:
                      Card Name: 1
                      Test: 0
                example: &ref_163
                  count_maps:
                    Card Name: 1
                    Test: 0
        '400':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
  /collection/count_by:
    post:
      tags:
        - Collection
      summary: Count CardUsers by printing, card_set, or name
      description: Shows number of CardUsers owned by a given category. Note that the keys in the response will change depending on the 'count_by' param provided
      operationId: CollectionCountBy
      parameters:
        - name: json_ids
          in: query
          required: true
          schema:
            type: array
            items:
              type: string
        - name: count_by
          in: query
          required: false
          schema:
            type: string
            default: printing
            enum:
              - printing
              - card_set
              - name
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Count CardUsers by given category response
                required: &ref_164
                  - count_by
                type: object
                properties: &ref_165
                  count_by:
                    type: object
                    example:
                      64da69b8-c9cf-4cca-bd15-36270242db41: 2
                      51811f2a-7002-4ba7-98d8-5b09d887975c: 3
                      7c5a3c09-5656-4975-ba03-2d809903ed180: 3
        '400':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
  /cards:
    get:
      tags:
        - Card
      description: Search all cards. This API is both public and private, if the user is logged in an owned count is returned, if they are not\nlogged in the owned count fields are not present.
      summary: Search all cards
      operationId: GetAllCards
      parameters:
        - name: json_id
          in: query
          required: false
          schema: *ref_1
        - name: card_list
          in: query
          required: false
          schema: *ref_2
        - name: sort_by
          in: query
          required: false
          schema: &ref_33
            type: string
            default: release_date
            enum:
              - name
              - cmc
              - set_name
              - release_date
              - power
              - toughness
              - collector_number
              - rarity
              - json_id
            description: 'Note: Cannot `sort_by: ''collector_number''` when `group_by: ''name''`'
        - name: order
          in: query
          required: false
          schema: *ref_3
        - name: group_by
          in: query
          required: false
          schema: *ref_4
        - name: query
          in: query
          required: false
          schema: *ref_5
        - name: starts_with
          in: query
          required: false
          schema: *ref_6
        - name: set_starts_with
          in: query
          required: false
          schema: *ref_7
        - name: rules_text_filter
          in: query
          required: false
          schema: *ref_8
          description: Searches for the whole string, so it must wholly exist within the rules text.
        - name: oracle_text_filter
          in: query
          required: false
          schema: *ref_9
          description: Searches for the whole string, so it must wholly exist within the rules text.
        - name: per_page
          in: query
          required: false
          schema: *ref_10
        - name: page
          in: query
          required: false
          schema: *ref_11
        - name: colors
          in: query
          required: false
          schema: *ref_12
        - name: color_identity
          in: query
          required: false
          schema: *ref_13
        - name: types
          in: query
          required: false
          schema: *ref_14
          description: Accepted types can be retrieved from the `/types` route.
        - name: sub_types
          in: query
          required: false
          schema: *ref_15
        - name: super_types
          in: query
          required: false
          schema: *ref_16
        - name: set_names
          in: query
          required: false
          schema: *ref_17
        - name: artists
          in: query
          required: false
          schema: *ref_18
        - name: rarity
          in: query
          required: false
          schema: *ref_19
        - name: price
          in: query
          required: false
          schema: &ref_34
            type: object
            properties:
              source:
                type: string
                default: tcg_player
                enum:
                  - tcg_player
                  - tcg_player_low
                  - tcg_player_mid
                  - tcg_player_high
                  - tcg_player_direct_low
                  - card_kingdom
                  - card_market
              min:
                type: number
              max:
                type: number
        - name: power
          in: query
          required: false
          schema: *ref_20
        - name: toughness
          in: query
          required: false
          schema: *ref_21
        - name: loyalty
          in: query
          required: false
          schema: *ref_22
          description: 'The loyalty filter will only match planeswalkers with loyalty that is not an integer (eg: ''1d4+1'', ''*'', or ''X'') when min is 0.'
        - name: mana_value (converted mana cost)
          in: query
          required: false
          schema: *ref_23
        - name: format_legality
          in: query
          required: false
          schema: *ref_24
        - name: set_types
          in: query
          required: false
          schema: *ref_25
        - name: is_reserved
          in: query
          required: false
          schema: *ref_26
        - name: border
          in: query
          required: false
          schema: *ref_27
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Get All Cards response
                required: &ref_166
                  - cards
                type: object
                properties: &ref_167
                  cards:
                    type: array
                    items:
                      type: object
                      example:
                        - name: Aethergeode Miner
                          json_id: 0c67391b0056fd6ec898ac4a0b6db596eabb2362
                          card_set_name: Aether Revolt
                          card_set_code: AER
                          rarity: Rare
                          legalities: {}
                          number: 4
                          multiverse_id: 423671
                          primary_card_json_id: null
                          secondary_card_json_id: null
                          layout: normal
                          ownable: true
                          owned_count: 0
                        - name: Aethersphere Harvester
                          json_id: 23ee8664f20563c48d8377afea9f4f7aac3349c4
                          card_set_name: Aether Revolt
                          card_set_code: AER
                          rarity: Rare
                          legalities: {}
                          number: 142
                          multiverse_id: 423809
                          primary_card_json_id: null
                          secondary_card_json_id: null
                          layout: normal
                          ownable: true
                          owned_count: 0
                    description: ''
                example: &ref_168
                  cards:
                    - name: Aethergeode Miner
                      description: Sed numquam sint sapiente.
                      original_description: Sed numquam sint sapiente.
                      json_id: 0c67391b0056fd6ec898ac4a0b6db596eabb2362
                      card_set_name: Aether Revolt
                      card_set_code: AER
                      rarity: Rare
                      collector_number: 4
                      multiverse_id: 423671
                      primary_card_json_id: null
                      secondary_card_json_id: null
                      converted_mana_cost: 1
                      layout: normal
                      ownable: true
                      types:
                        - Planeswalker
                        - Instant
                      super_types:
                        - ducimus
                        - dolor
                        - ut
                      sub_types:
                        - soldier
                      image_url: www.mtg.com/something
                      power: 7
                      toughness: 2
                      loyalty: 4
                      white_count: 1
                      color_white: true
                      blue_count: 0
                      color_blue: true
                      black_count: 0
                      color_black: true
                      red_count: 1
                      color_red: false
                      green_count: 1
                      color_green: true
                      color_multi: false
                      is_reserved: false
                      legalities:
                        standard: Legal
                        modern: Banned
                        commander: Legal
                      foreign_data:
                        en:
                          language_name: Esperanto
                          name: optio sunt
                          description: Et reiciendis ad doloremque.
                          flavor: Qui commodi quia hic.
                          multiverse_id: 1882
                          card_type: nam placeat
                      price: 100
                      price_foil: 120
                      ck_price: 90
                      ck_price_foil: 100
                      cm_price: 51
                      cm_price_foil: 65
                      owned_count: 0
                      user_price: 0
                    - name: Aethersphere Harvester
                      description: Sed numquam sint sapiente.
                      original_description: Sed numquam sint sapiente.
                      json_id: 23ee8664f20563c48d8377afea9f4f7aac3349c4
                      card_set_name: Aether Revolt
                      card_set_code: AER
                      rarity: Rare
                      collector_number: 142
                      multiverse_id: 423809
                      primary_card_json_id: null
                      secondary_card_json_id: null
                      converted_mana_cost: 1
                      layout: normal
                      ownable: true
                      types:
                        - Planeswalker
                        - Instant
                      super_types:
                        - ducimus
                        - dolor
                        - ut
                      sub_types:
                        - soldier
                      image_url: www.mtg.com/something
                      power: 7
                      toughness: 2
                      loyalty: 4
                      white_count: 1
                      color_white: true
                      blue_count: 0
                      color_blue: true
                      black_count: 0
                      color_black: true
                      red_count: 1
                      color_red: false
                      green_count: 1
                      color_green: true
                      color_multi: false
                      is_reserved: false
                      legalities:
                        standard: Legal
                        modern: Banned
                        commander: Legal
                      foreign_data:
                        en:
                          language_name: Esperanto
                          name: optio sunt
                          description: Et reiciendis ad doloremque.
                          flavor: Qui commodi quia hic.
                          multiverse_id: 1882
                          card_type: nam placeat
                      price: 100
                      price_foil: 120
                      ck_price: 90
                      ck_price_foil: 100
                      cm_price: 51
                      cm_price_foil: 65
                      owned_count: 1
                      user_price: 100
  /cards/printings:
    post:
      tags:
        - Card
      summary: Get printing details for cards by JSON ids
      description: |-
        + Duplicate JSON ids are ignored 
        + Response is sorted by release date 
        + Response is empty when there are no shared printings 
        + Invalid ids respond with 'not found'
      operationId: GetCardsPrintings
      requestBody:
        description: ''
        content:
          application/json:
            schema:
              title: Get printings for cards by JSON ids
              required: &ref_28
                - json_ids
              type: object
              properties: &ref_29
                json_ids:
                  type: object
                  example:
                    - 83c6088bc36d3ccd3b64e03a4383ac20e537349d
                    - ffc5a91331acb724f6edb432ac355f0cbccc69c6
                  description: ''
                query:
                  type: string
                  example:
                    - Urza's Saga
                  description: Filters response to only those from a specific set
                ownable:
                  type: boolean
                  default: true
                  description: Filters response to only those ownable/unownable
                per_page:
                  type: integer
                  description: ''
                page:
                  type: integer
                  description: ''
        required: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Get All Cards printings response
                required: &ref_169
                  - printings
                type: object
                properties: &ref_170
                  printings:
                    type: object
                    example:
                      - name: Aethergeode Miner
                        description: Sed numquam sint sapiente.
                        original_description: Sed numquam sint sapiente.
                        json_id: 0c67391b0056fd6ec898ac4a0b6db596eabb2362
                        card_set_name: Aether Revolt
                        card_set_code: AER
                        rarity: Rare
                        collector_number: 4
                        multiverse_id: 423671
                        primary_card_json_id: null
                        secondary_card_json_id: null
                        converted_mana_cost: 1
                        layout: normal
                        ownable: true
                        types:
                          - Planeswalker
                          - Instant
                        super_types:
                          - ducimus
                          - dolor
                          - ut
                        sub_types:
                          - soldier
                        image_url: www.mtg.com/something
                        power: 7
                        toughness: 2
                        loyalty: 4
                        white_count: 1
                        color_white: true
                        blue_count: 0
                        color_blue: true
                        black_count: 0
                        color_black: true
                        red_count: 1
                        color_red: false
                        green_count: 1
                        color_green: true
                        color_multi: false
                        is_reserved: false
                        languages:
                          - en
                          - de
                          - fr
                          - it
                          - es
                          - pt
                          - jp
                          - cn
                          - tw
                          - ru
                          - ko
                        legalities:
                          standard: Legal
                          modern: Banned
                          commander: Legal
                        foreign_data:
                          en:
                            language_name: Esperanto
                            name: optio sunt
                            description: Et reiciendis ad doloremque.
                            flavor: Qui commodi quia hic.
                            multiverse_id: 1882
                            card_type: nam placeat
                        price: 100
                        price_foil: 120
                        ck_price: 90
                        ck_price_foil: 100
                        cm_price: 51
                        cm_price_foil: 65
        '400':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
  /cards/languages:
    post:
      tags:
        - Card
      summary: Get language details for cards by JSON ids
      description: + Invalid ids respond with 'not found'
      operationId: GetCardsLanguages
      requestBody:
        description: ''
        content:
          application/json:
            schema:
              title: Get printings for cards by JSON ids
              required: *ref_28
              type: object
              properties: *ref_29
        required: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Get All Cards language response
                required: &ref_171
                  - languages
                type: object
                properties: &ref_172
                  languages:
                    type: object
                    example:
                      - 83c6088bc36d3ccd3b64e03a4383ac20e537349d:
                          - en
                          - de
                          - fr
                          - it
                          - es
                          - pt
                          - jp
                          - cn
                          - tw
                          - ru
                          - ko
                      - ffc5a91331acb724f6edb432ac355f0cbccc69c6:
                          - en
                          - de
                          - fr
                          - it
                          - es
                          - pt
                          - jp
                          - cn
                          - tw
                          - ru
                          - ko
        '400':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
  /cards/{id}:
    get:
      tags:
        - Card
      summary: Get Details for Card
      description: + id (string) - json_id of the card
      operationId: GetDetailsForCard
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            example: 7cfabcf0-438a-48f5-a4af-5b0f89f768a4
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Get Details for Card response
                required: &ref_30
                  - id
                  - name
                  - description
                  - power
                  - toughness
                  - created_at
                  - updated_at
                  - search_name
                  - mana_cost
                  - converted_mana_cost
                  - card_set_name
                  - card_type
                  - sub_type
                  - loyalty
                  - rarity
                  - card_set_id
                  - multiverse_id
                  - card_set_code
                  - legalities
                  - super_type
                  - types
                  - colors
                  - flavor
                  - artist
                  - number
                  - ozguild_id
                  - price
                  - json_id
                  - has_image
                type: object
                properties: &ref_31
                  id:
                    type: integer
                    format: int32
                    example: 17165
                  name:
                    type: string
                    example: Harvest Gwyllion
                  description:
                    type: string
                    example: Wither (This deals damage to creatures in the form of -1/-1 counters.)
                  power:
                    type: string
                    example: 2
                  toughness:
                    type: string
                    example: 4
                  created_at:
                    type: string
                    example: 10/30/2015 6:59:59 AM
                  updated_at:
                    type: string
                    example: 11/26/2015 1:58:24 AM
                  search_name:
                    type: string
                    example: Harvest Gwyllion
                  mana_cost:
                    type: string
                    example: '{2}{W/B}{W/B}'
                  converted_mana_cost:
                    type: string
                    example: 4
                  card_set_name:
                    type: string
                    example: Eventide
                  card_type:
                    type: string
                    example: Creature — Hag
                  sub_type:
                    type: string
                    example: '["Hag"]'
                  loyalty:
                    type: string
                    nullable: true
                  rarity:
                    type: string
                    example: Common
                  card_set_id:
                    type: string
                    example: 106
                  multiverse_id:
                    type: integer
                    format: int32
                    example: 152089
                  card_set_code:
                    type: string
                    example: EVE
                  legalities:
                    type: string
                    example: '[{"format"=>"Commander", "legality"=>"Legal"}, {"format"=>"Freeform", "legality"=>"Legal"}, {"format"=>"Legacy", "legality"=>"Legal"}, {"format"=>"Lorwyn-Shadowmoor Block", "legality"=>"Legal"}, {"format"=>"Modern", "legality"=>"Legal"}, {"format"=>"Prismatic", "legality"=>"Legal"}, {"format"=>"Singleton 100", "legality"=>"Legal"}, {"format"=>"Tribal Wars Legacy", "legality"=>"Legal"}, {"format"=>"Vintage", "legality"=>"Legal"}]'
                  super_type:
                    type: string
                    example: '[]'
                  types:
                    type: string
                    example: '["Creature"]'
                  colors:
                    type: string
                    example: '["White", "Black"]'
                  flavor:
                    type: string
                    example: She speaks only in vile epithets, spitting out curses and obscenities that harm others just by the hearing.
                  artist:
                    type: string
                    example: Nils Hamm
                  number:
                    type: string
                    example: 90
                  ozguild_id:
                    type: string
                    example: 7279144a9b82f2f47ce8de0700bff13c
                  price:
                    type: integer
                    format: int32
                    example: 5991
                  json_id:
                    type: string
                    example: 960e9cc1ed20802d30effa0e26c06a623f4d792c
                  has_image:
                    type: boolean
                    example: false
                example: &ref_32
                  id: 17165
                  name: Harvest Gwyllion
                  description: Wither (This deals damage to creatures in the form of -1/-1 counters.)
                  power: 2
                  toughness: 4
                  created_at: '2015-10-30T06:59:59.519Z'
                  updated_at: '2015-11-26T01:58:24.416Z'
                  search_name: Harvest Gwyllion
                  mana_cost: '{2}{W/B}{W/B}'
                  converted_mana_cost: 4
                  card_set_name: Eventide
                  card_type: Creature — Hag
                  sub_type: '["Hag"]'
                  loyalty: null
                  rarity: Common
                  card_set_id: 106
                  multiverse_id: 152089
                  card_set_code: EVE
                  legalities: '[{"format"=>"Commander", "legality"=>"Legal"}, {"format"=>"Freeform", "legality"=>"Legal"}, {"format"=>"Legacy", "legality"=>"Legal"}, {"format"=>"Lorwyn-Shadowmoor Block", "legality"=>"Legal"}, {"format"=>"Modern", "legality"=>"Legal"}, {"format"=>"Prismatic", "legality"=>"Legal"}, {"format"=>"Singleton 100", "legality"=>"Legal"}, {"format"=>"Tribal Wars Legacy", "legality"=>"Legal"}, {"format"=>"Vintage", "legality"=>"Legal"}]'
                  super_type: '[]'
                  types: '["Creature"]'
                  colors: '["White", "Black"]'
                  flavor: She speaks only in vile epithets, spitting out curses and obscenities that harm others just by the hearing.
                  artist: Nils Hamm
                  number: 90
                  ozguild_id: 7279144a9b82f2f47ce8de0700bff13c
                  price: 5991
                  json_id: 960e9cc1ed20802d30effa0e26c06a623f4d792c
                  has_image: false
        '400':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
  /cards/expand:
    get:
      tags:
        - Card
      summary: Get Details for Card By JSON ID
      description: ''
      operationId: GetDetailsForCardJsonId
      requestBody:
        description: ''
        content:
          application/json:
            schema:
              title: Get Card By Json Id Request
              required: &ref_173
                - json_id
              type: array
              items: &ref_174
                type: string
                example:
                  - json_id1
                  - json_id2
                description: ''
              example: &ref_175
                - json_id1
                - json_id2
        required: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Get Details for Card response
                required: *ref_30
                type: object
                properties: *ref_31
                example: *ref_32
        '400':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
  /cards/search:
    post:
      tags:
        - Card
      summary: Search cards
      description: Searching cards
      operationId: SearchCards
      security:
        - OAuth2:
            - user
        - cookieAuth: []
      parameters:
        - name: json_id
          in: query
          required: false
          schema: *ref_1
        - name: card_list
          in: query
          required: false
          schema: *ref_2
        - name: sort_by
          in: query
          required: false
          schema: *ref_33
        - name: order
          in: query
          required: false
          schema: *ref_3
        - name: group_by
          in: query
          required: false
          schema: *ref_4
        - name: query
          in: query
          required: false
          schema: *ref_5
        - name: starts_with
          in: query
          required: false
          schema: *ref_6
        - name: set_starts_with
          in: query
          required: false
          schema: *ref_7
        - name: rules_text_filter
          in: query
          required: false
          schema: *ref_8
          description: Searches for the whole string, so it must wholly exist within the rules text.
        - name: oracle_text_filter
          in: query
          required: false
          schema: *ref_9
          description: Searches for the whole string, so it must wholly exist within the rules text.
        - name: per_page
          in: query
          required: false
          schema: *ref_10
        - name: page
          in: query
          required: false
          schema: *ref_11
        - name: colors
          in: query
          required: false
          schema: *ref_12
        - name: color_identity
          in: query
          required: false
          schema: *ref_13
        - name: types
          in: query
          required: false
          schema: *ref_14
          description: Accepted types can be retrieved from the `/types` route.
        - name: sub_types
          in: query
          required: false
          schema: *ref_15
        - name: super_types
          in: query
          required: false
          schema: *ref_16
        - name: set_names
          in: query
          required: false
          schema: *ref_17
        - name: artists
          in: query
          required: false
          schema: *ref_18
        - name: rarity
          in: query
          required: false
          schema: *ref_19
        - name: price
          in: query
          required: false
          schema: *ref_34
        - name: power
          in: query
          required: false
          schema: *ref_20
        - name: toughness
          in: query
          required: false
          schema: *ref_21
        - name: loyalty
          in: query
          required: false
          schema: *ref_22
          description: 'The loyalty filter will only match planeswalkers with loyalty that is not an integer (eg: ''1d4+1'', ''*'', or ''X'') when min is 0.'
        - name: mana_value (converted mana cost)
          in: query
          required: false
          schema: *ref_23
        - name: format_legality
          in: query
          required: false
          schema: *ref_24
        - name: set_types
          in: query
          required: false
          schema: *ref_25
        - name: is_reserved
          in: query
          required: false
          schema: *ref_26
        - name: border
          in: query
          required: false
          schema: *ref_27
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: MTG Search Collection response
                type: object
                example: *ref_35
  /cards/artists:
    get:
      tags:
        - Card
      summary: Get all artists
      description: Get all artists
      operationId: GetArtists
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Get all card artists
                required: &ref_176
                  - artists
                type: object
                properties: &ref_177
                  artists:
                    type: string
                    example:
                      - Jason Chan
                      - Johannes Voss
                      - Seb McKinnon
                    description: ''
  /cards/super_types:
    get:
      tags:
        - Card
      summary: Get all super_types
      description: Get all super_types
      operationId: GetSuperTypes
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Get all card super_types
                required: &ref_178
                  - super_types
                type: object
                properties: &ref_179
                  super_types:
                    type: array
                    example:
                      - Basic
                      - Legendary
                      - Snow
                      - ...
                    description: ''
  /cards/sub_types:
    get:
      tags:
        - Card
      summary: Get all sub_types
      description: Get all sub_types
      operationId: GetSubTypes
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Get all card sub_types
                required: &ref_180
                  - sub_types
                type: object
                properties: &ref_181
                  sub_types:
                    type: array
                    example:
                      - Goblin
                      - Giant
                      - Saga
                      - Desert
                      - Gate
                      - ...
                    description: ''
  /cards/types:
    get:
      tags:
        - Card
      summary: Get all types
      description: Get all types
      operationId: GetTypes
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Get all card types
                required: &ref_182
                  - types
                type: object
                properties: &ref_183
                  types:
                    type: array
                    example:
                      - Land
                      - Creature
                      - Enchantment
                      - Instant
                      - Sorcery
                      - ...
                    description: ''
  /card_users:
    post:
      tags:
        - Card Users
      summary: Create Cards For User
      description: |-
        Bulk creates cards for the currently authenticated user. On request
        success all the cards are created for the user. On failure the request is rollbacked. If no input_source param is passed, a legacy input_source with type 'unknown' is created
      operationId: CreateCardsForUser
      parameters:
        - name: card_users
          in: query
          required: true
          schema:
            type: array
            example:
              - json_id: b6c7eae12675518633ac20c01060892
                quality: Near Mint
                foil: false
              - json_id: 15d18d585a2e08b311c5bbaed1d6382
                quality: Near Mint
                foil: false
        - name: input_source
          in: query
          required: false
          schema:
            type: string
            example: 7cfabcf0-438a-48f5-a4af-5b0f89f768a4
      requestBody:
        description: ''
        content:
          application/json:
            schema:
              title: Create Cards For User request
              required: &ref_39
                - card_users
              type: object
              properties: &ref_40
                card_users:
                  type: array
                  items:
                    type: object
                    example:
                      - json_id: id
                        quality: played
                        foil: true
                      - json_id: id2
                        quality: mint
                        foil: false
                  description: ''
              example: &ref_41
                card_users:
                  - json_id: id
                    quality: played
                    foil: true
                  - json_id: id2
                    quality: mint
                    foil: false
        required: true
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                title: Create Cards For User response
                required: &ref_36
                  - card_users
                type: object
                properties: &ref_37
                  card_users:
                    type: array
                    items:
                      type: object
                    description: ''
                example: &ref_38
                  card_users:
                    - id: 1
                      json_id: id
                      foil: true
                      quality: played
                      created_at: 2015-10-30 18:19:26 +1100
                      updated_at: 2015-10-30 18:19:26 +1100
                      name: Argus
                      foreign_name: El Argus
                      language: English
                      mana_cost: '["blue"]'
                      price: 50
                      price_foil: 100
                      rarity: common
                      set_code: ABC
                      set_name: Any Basic Card
                      sub_type:
                        - shaman
                      types:
                        - creature
                      collector_number: '59'
                    - id: 2
                      json_id: id2
                      foil: true
                      quality: played
                      created_at: 2015-10-30 18:19:26 +1100
                      updated_at: 2015-10-30 18:19:26 +1100
                      name: Argus Jr
                      foreign_name: El Argus Jr
                      language: English
                      mana_cost: '[''blue'']'
                      price: 50
                      price_foil: 100
                      rarity: common
                      set_code: ABC
                      set_name: Any Basic Card
                      sub_type:
                        - shaman
                      types:
                        - creature
                      collector_number: '60'
        '400':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
        '404':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
    patch:
      tags:
        - Card Users
      summary: Update Card Users
      description: Bulk updates card users
      operationId: PatchCardUsers
      parameters:
        - name: card_ids
          in: query
          required: true
          schema:
            type: array
            items:
              type: integer
              example:
                - 33
                - 62
                - 16
          description: card_user ids
        - name: foil
          in: query
          required: false
          schema:
            type: boolean
        - name: quality
          in: query
          required: false
          schema:
            type: string
            enum:
              - Near Mint
              - Lightly Played
              - Moderately Played
              - Heavily Played
              - Damaged
        - name: language
          in: query
          required: false
          schema:
            type: string
            enum:
              - en
              - de
              - fr
              - it
              - es
              - pt
              - jp
              - cn
              - tw
              - ru
              - ko
        - name: printing
          in: query
          required: false
          schema:
            type: string
            example: b6c7eae12675518633ac20c01060892
          description: Card's json_id
        - name: annotate_scan
          in: query
          required: false
          schema:
            type: boolean
          description: Create or update scan metadata with updated card info
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                title: Create Cards For User response
                required: *ref_36
                type: object
                properties: *ref_37
                example: *ref_38
        '400':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
        '404':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
    delete:
      tags:
        - Card Users
      summary: Delete Card
      description: |-
        Bulk deletes card for the currently authenticated user. On request
        success all the cards are deleted from the user. On failure the request is rollbacked.
        + card_ids (object) - array of ids of card users to be deleted
      operationId: DeleteCard
      requestBody:
        description: ''
        content:
          application/json:
            schema:
              title: Delete Card request
              required: &ref_184
                - card_ids
              type: object
              properties: &ref_185
                card_ids:
                  type: array
                  items:
                    type: integer
                    format: int32
                    example:
                      - 1
                      - 2
                      - 3
                      - 4
                  description: ''
              example: &ref_186
                card_ids:
                  - 1
                  - 2
                  - 3
                  - 4
        required: true
      responses:
        '200':
          description: ''
        '400':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
        '404':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
  /card_users/add:
    post:
      tags:
        - Card Users
      summary: Create Cards For User
      description: |-
        Bulk creates cards for the currently authenticated user. On request
        success all the cards are created for the user. On failure the request is rollbacked. If no input_source param is passed, a legacy input_source with type 'unknown' is created
      operationId: AddCardsForUser
      parameters:
        - name: card_users
          in: query
          required: true
          schema:
            type: array
            example:
              - json_id: b6c7eae12675518633ac20c01060892
                quality: Near Mint
                foil: false
              - json_id: 15d18d585a2e08b311c5bbaed1d6382
                quality: Near Mint
                foil: false
        - name: input_source
          in: query
          required: false
          schema:
            type: string
            example: 7cfabcf0-438a-48f5-a4af-5b0f89f768a4
      requestBody:
        description: ''
        content:
          application/json:
            schema:
              title: Create Cards For User request
              required: *ref_39
              type: object
              properties: *ref_40
              example: *ref_41
        required: true
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                title: Create Cards For User response
                required: *ref_36
                type: object
                properties: *ref_37
                example: *ref_38
        '400':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
  /card_users/export:
    get:
      tags:
        - Card Users
      summary: Export Collection
      description: Queues a CSV export job. A link to the resulting CSV is emailed to the user
      operationId: ExportCollection
      parameters:
        - name: type
          in: query
          required: false
          schema:
            type: string
            default: cardcastle
            enum:
              - cardcastle
              - simple
              - deckbox
              - echomtg
              - binderpos
              - crystalcommerce
              - tcgplayer_kiosk
              - tcgplayer_online
              - tcg_vault
        - name: kind
          in: query
          required: false
          schema:
            type: string
            default: csv
            enum:
              - csv
              - txt
            description: Only `echomtg` support txt
        - name: staged
          in: query
          required: false
          schema:
            type: boolean
            default: false
          description: when true, exports all staged card users accross all sessions. Otherwise exports all owned card users
        - name: source
          in: query
          required: false
          schema:
            type: string
            enum:
              - cardbot
              - app
              - manual
              - import
              - unknown
        - name: input_session
          in: query
          required: false
          schema:
            type: string
            example: 7cfabcf0-438a-48f5-a4af-5b0f89f768a4
      responses:
        '202':
          description: ''
  /card_users/import:
    post:
      tags:
        - Card Users
      summary: Import to Collection
      description: Upload the CSV to the server. A job is queued to asynchronously add the cards to the users collection. A resulting email will be sent to the user on completion.
      operationId: ImportToCollection
      parameters:
        - name: csv
          in: query
          required: true
          schema:
            type: object
        - name: kind
          in: query
          required: false
          schema:
            type: string
            default: csv
            enum:
              - csv
              - txt
            description: Only echomtg support txt
        - name: type
          in: query
          required: false
          schema:
            type: string
            default: cardcastle
            enum:
              - cardcastle
              - simple
              - deckbox
              - echomtg
      responses:
        '202':
          description: ''
  /card_users/foil:
    patch:
      tags:
        - Card Users
      summary: Update Foil on Card
      description: Updates the foil attribute on the card
      operationId: UpdateFoilOnCard
      requestBody:
        description: ''
        content:
          application/json:
            schema:
              title: Update Foil on Card request
              required: &ref_187
                - card_ids
                - foil
              type: object
              properties: &ref_188
                card_ids:
                  type: array
                  items:
                    type: integer
                    format: int32
                    example:
                      - 1
                      - 2
                      - 3
                      - 4
                  description: Array of ids of card users to be updated
                foil:
                  type: boolean
                  example: true
              example: &ref_189
                card_ids:
                  - 1
                  - 2
                  - 3
                  - 4
                foil: true
        required: true
      responses:
        '200':
          description: ''
          content:
            text/plain:
              schema:
                type: object
        '400':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
  /card_users/quality:
    patch:
      tags:
        - Card Users
      summary: Update Quality on Card
      description: Updates the quality attribute on the card, the default quality is an empty string.
      operationId: UpdateQualityOnCard
      requestBody:
        description: ''
        content:
          application/json:
            schema:
              title: Update Quality on Card request
              required: &ref_190
                - card_ids
                - quality
              type: object
              properties: &ref_191
                card_ids:
                  type: array
                  items:
                    type: integer
                    format: int
                    example:
                      - 1
                      - 2
                      - 3
                      - 4
                  description: ''
                quality:
                  type: string
                  enum:
                    - Near Mint
                    - Lightly Played
                    - Moderately Played
                    - Heavily Played
                    - Damaged
                  example: Near Mint
              example: &ref_192
                card_ids:
                  - 1
                  - 2
                  - 3
                  - 4
                quality: Near Mint
        required: true
      responses:
        '200':
          description: ''
          content:
            text/plain:
              schema:
                type: object
        '400':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
  /card_users/language:
    patch:
      tags:
        - Card Users
      summary: Update Language On Cards
      description: Updates the language attribute on the card
      operationId: UpdateLanguageOnCard
      requestBody:
        description: ''
        content:
          application/json:
            schema:
              title: Update Language on Card request
              required: &ref_193
                - card_ids
                - language
              type: object
              properties: &ref_194
                card_ids:
                  type: array
                  items:
                    type: integer
                    format: int32
                    example:
                      - 1
                      - 2
                      - 3
                      - 4
                  description: ''
                language:
                  type: object
                  example:
                    en: true
                    fr: false
        required: true
      responses:
        '200':
          description: ''
          content:
            text/plain:
              schema:
                type: object
        '400':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
  /card_users/commit:
    post:
      tags:
        - Card Users
      summary: Commit Staged Cards
      description: Commit the cards that have been staged on the build page, and finalize the valus. Returns the ids of all the cards added
      operationId: CommitStagedCards
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Commit Staged Cards response
                required: &ref_195
                  - card_ids
                type: object
                properties: &ref_196
                  card_ids:
                    type: array
                    items:
                      type: integer
                      format: int32
                      example:
                        - 1
                        - 2
                        - 3
                        - 4
                    description: ''
                example: &ref_197
                  card_ids:
                    - 1
                    - 2
                    - 3
                    - 4
        '500':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                title: 404 Error
                required: &ref_198
                  - error
                type: object
                properties: &ref_199
                  error:
                    type: string
                    example: Error Message
                example: &ref_200
                  error: Error Message
  /card_users/reset:
    delete:
      tags:
        - Card Users
      summary: Reset Staged Cards
      description: Reset all the cards in the staged area. Does a hard delete of all staged cards
      operationId: ResetStagedCards
      responses:
        '200':
          description: ''
  /card_users/clear:
    delete:
      tags:
        - Card Users
      summary: Clear Collection
      description: Removes all cards in the users collection for all games. This is a soft delete
      operationId: ClearCollection
      responses:
        '204':
          description: ''
  /card_users/resources:
    post:
      tags:
        - Card Users
      summary: Get Linked Resources
      description: Gets the linked resources for a card user
      operationId: GetCardUserResources
      requestBody:
        content:
          application/json:
            schema:
              title: Card User Resource request
              required: &ref_201
                - card_ids
              type: object
              properties: &ref_202
                card_ids:
                  type: array
                  items:
                    type: integer
                    format: int32
                    example:
                      - 1
                      - 2
                      - 3
                      - 4
                  description: ''
              example: &ref_203
                card_ids:
                  - 1
                  - 2
                  - 3
                  - 4
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Get a Resource response
                required: &ref_204
                  - resources
                type: object
                properties: &ref_205
                  resources:
                    type: object
                    example:
                      01c3854a-a139-42ae-9aae-4db53661fc4f:
                        deck:
                          id: 2,
                          name: Merrill Kuhn
                          legality: modern
                          image: http://erdman.info/asuncion_kshlerin
                          user_id: 2
                          created_at: '2023-06-09T00:53:56.60152'
                          updated_at: '2023-06-09T00:53:56.766147'
                          deleted_at: nil
                          uuid: 9d11884b-e21e-4620-9a68-e7659ceefbdf
                          color_white: true
                          color_blue: true
                          color_black: true
                          color_green: true
                          color_red: true
                          color_colorless: true
                          public: true
                        card_deck:
                          id: 12
                          card_id: 32
                          deck_id: 2
                          deck_board_id: 3
                          card_type: nil
                          created_at: '2023-06-09T00:53:56.621093'
                          updated_at: '2023-06-09T00:53:56.621093'
                          card_user_id: 32
                        stacked_card:
                          id: 12
                          uuid: edd33fde-4adb-461e-ae05-fb1e605a83f0
                          position: 9
                          card_user_id: 32
                          location_id: 2
                          location_uuid: e4654973-1674-4ee0-a923-5fb844bd1622
                          created_at: '2023-06-09T00:53:56.629094'
                          updated_at: '2023-06-09T00:53:56.773523'
                        location:
                          id: 2
                          name: autin
                          uuid: e4654973-1674-4ee0-a923-5fb844bd1622
                          parent_id: nil
                          user_id: 2
                          created_at: '2023-06-09T00:53:56.613768'
                          updated_at: '2023-06-09T00:53:56.616593'
        '400':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
  /card_users/card:
    patch:
      tags:
        - Card Users
      summary: Update card
      description: Update card for card users
      operationId: PatchCardUsersCard
      parameters:
        - name: json_id
          in: query
          required: true
          schema:
            type: string
        - name: card_user_uuids
          in: query
          required: true
          schema:
            type: array
            items:
              type: string
        - name: annotate_scan
          in: query
          required: false
          schema:
            type: boolean
          description: Create or update scan metadata with updated card info
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Patch card users card response
                required: &ref_206
                  - card_users
                type: object
                properties: &ref_207
                  card_users:
                    type: array
                    items:
                      type: object
                    example:
                      - id: 6
                        card_id: 12
                        language: en
                        input_session_id: 6
                        scanned_image:
                          url: nil
                        foil: true
                        quality: Damaged
                        created_at: '2024-07-29T04:46:55.869Z'
                        updated_at: '2024-07-29T04:46:56.237Z'
                        deleted_at: nil
                        uuid: 391005ce-93fe-4845-afa2-5104bed38b3a
                        json_id: 3477135a42c627927dd81b475d163e12
                        set_name: quia et
                        set_code: RPF
                        collector_number: 422D
                        rarity: Basic Land
                        foreign_name: vero atque
                        price: 1199
                        name: Misfit
        '400':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
  /card_users/{id}/scanned_image:
    post:
      tags:
        - Card Users
      summary: Upload a scanned image
      description: Uploads a scanned image and attaches it to an existing card user
      operationId: PostCardUsersScannedImage
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        content:
          image/*:
            schema:
              type: string
              format: binary
      responses:
        '201':
          description: The image was successfully uploaded.
          content:
            application/json:
              schema:
                title: Scanned images response
                required: &ref_208
                  - scanned_image
                type: object
                properties: &ref_209
                  scanned_image:
                    type: string
                example: &ref_210
                  scanned_image: /uploads/card_user/scanned_image/large_9ab2efab-840e-4c9d-8d7e-d428325d69d0.jpg
        '400':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
        '404':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
  /card_lists:
    get:
      tags:
        - Card Lists
      summary: List Card Lists
      description: Lists all card lists owned by the current user
      operationId: GetCardLists
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Get Card List Response
                type: object
                properties: &ref_211
                  card_lists:
                    type: array
                    items:
                      type: object
                      properties:
                        name:
                          type: string
                        id:
                          type: integer
                          format: int32
                        created_at:
                          type: string
                          format: date-time
                example: &ref_212
                  card_lists:
                    - name: My Awesome List
                      created_at: '2015-12-16T02:55:34.086Z'
                    - name: My More Awesomer List
                      created_at: '2015-12-16T02:55:34.086Z'
        '400':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
    post:
      tags:
        - Card Lists
      summary: Create Card List
      description: Creates a new Card List
      operationId: CreateCardList
      requestBody:
        content:
          application/json:
            schema:
              title: Create Card List Request
              type: object
              required: &ref_213
                - name
              properties: &ref_214
                name:
                  type: string
                  maximum: 100
                description:
                  type: string
              example: &ref_215
                name: My Awesome List
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                title: Create Card List Response
                type: object
                properties: &ref_216
                  card_list:
                    type: object
                    properties:
                      name:
                        type: string
                      id:
                        type: integer
                        format: int32
                      count:
                        type: integer
                        format: int32
                      created_at:
                        type: string
                        format: date-time
                example: &ref_217
                  card_lists:
                    id: 2
                    name: My Awesome List
                    count: 666
                    created_at: '2015-12-16T02:55:34.086Z'
        '400':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
  /card_lists/{uuid}:
    get:
      tags:
        - Card Lists
      summary: Show Card List
      description: Gets the details of a Card List.
      operationId: ShowCardList
      parameters:
        - name: uuid
          in: path
          required: true
          schema:
            type: string
            example: 7cfabcf0-438a-48f5-a4af-5b0f89f768a4
        - name: query
          in: query
          required: false
          schema:
            type: string
        - name: sort_by
          in: query
          required: false
          schema:
            type: string
            enum:
              - cmc
              - set_name
              - release_date
              - power
              - toughness
              - rarity
        - name: order
          in: query
          required: false
          schema:
            type: string
            enum:
              - asc
              - desc
        - name: per_page
          in: query
          required: false
          schema:
            type: number
            format: int32
        - name: page
          in: query
          required: false
          schema:
            type: number
            format: int32
      responses:
        '200':
          description: ''
          headers:
            X-Next-Page:
              description: The url for the next page of results.
              schema:
                type: string
            X-Prev-Page:
              description: The url for the previous page of results.
              schema:
                type: string
            X-Total:
              description: The total number of listed cards.
              schema:
                type: number
                format: int32
            X-Total-Pages:
              description: The total number of pages.
              schema:
                type: number
                format: int32
          content:
            application/json:
              schema:
                title: Show Card List Response
                type: object
                properties: &ref_218
                  card_list:
                    type: object
                    properties:
                      name:
                        type: string
                      id:
                        type: integer
                        format: int32
                      count:
                        type: integer
                        format: int32
                      created_at:
                        type: string
                        format: date-time
                      listed_cards:
                        type: array
                        items:
                          type: object
                          properties:
                            json_id:
                              type: string
                            foil:
                              type: boolean
                            language:
                              type: string
                            created_at:
                              type: string
                              format: date-time
                      cards:
                        type: object
                        properties:
                          json_id:
                            type: string
                          name:
                            type: string
                example: &ref_219
                  card_list:
                    id: 2
                    name: My Awesome List
                    count: 123
                    created_at: '2015-12-16T02:55:34.086Z'
                    listed_cards:
                      - json_id: abcdef
                        foil: false
                        language: en
                        created_at: '2015-12-16T02:55:34.086Z'
                      - json_id: '123456'
                        foil: true
                        language: pt
                        created_at: '2015-12-16T02:55:34.086Z'
                    cards:
                      '123456':
                        json_id: '123456'
                        name: Technical Debt
                      abcdef:
                        json_id: abcdef
                        name: Foobar The Great
        '400':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
        '404':
          description: Card List not found. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
    delete:
      tags:
        - Card Lists
      summary: Remove Card List
      description: Removes a Card List
      operationId: RemoveCardList
      parameters:
        - name: uuid
          in: path
          required: true
          schema:
            type: string
            example: 7cfabcf0-438a-48f5-a4af-5b0f89f768a4
      responses:
        '200':
          description: ''
        '400':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
        '404':
          description: Card List not found. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
    patch:
      tags:
        - Card Lists
      summary: Update Card List
      description: Updates a Card List
      operationId: UpdateCardList
      parameters:
        - name: uuid
          in: path
          required: true
          schema:
            type: string
            example: 7cfabcf0-438a-48f5-a4af-5b0f89f768a4
      requestBody:
        content:
          application/json:
            schema:
              title: Update Card List Request
              type: object
              required: &ref_220
                - name
              properties: &ref_221
                name:
                  type: string
                  maximum: 100
                description:
                  type: string
              example: &ref_222
                name: My new name
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Update Card List Response
                type: object
                properties: &ref_223
                  card_list:
                    type: object
                    properties:
                      name:
                        type: string
                      id:
                        type: integer
                        format: int32
                      count:
                        type: integer
                        format: int32
                      created_at:
                        type: string
                        format: date-time
                      listed_cards:
                        type: array
                        items:
                          type: object
                          properties:
                            json_id:
                              type: string
                            foil:
                              type: boolean
                            language:
                              type: string
                            created_at:
                              type: string
                              format: date-time
                      cards:
                        type: object
                        properties:
                          json_id:
                            type: string
                          name:
                            type: string
                example: &ref_224
                  card_list:
                    id: 2
                    name: My New Name
                    count: 123
                    created_at: '2015-12-16T02:55:34.086Z'
                    listed_cards:
                      - json_id: abcdef
                        foil: false
                        language: en
                        created_at: '2015-12-16T02:55:34.086Z'
                      - json_id: '123456'
                        foil: true
                        language: pt
                        created_at: '2015-12-16T02:55:34.086Z'
                    cards:
                      '123456':
                        json_id: '123456'
                        name: Technical Debt
                      abcdef:
                        json_id: abcdef
                        name: Foobar The Great
        '400':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
        '404':
          description: Card List not found. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
  /card_lists/{uuid}/add:
    post:
      tags:
        - Card Lists
      summary: Add Listed Card
      description: Add a Listed Card to a Card List
      operationId: AddListedCard
      parameters:
        - name: uuid
          in: path
          required: true
          schema:
            type: string
            example: 6da368f5-48f8-4a5c-8411-d00e1bb93bab
      requestBody:
        content:
          application/json:
            schema:
              title: Add Listed Cards to a Card List Request
              type: object
              required: &ref_225
                - json_id
                - foil
                - language
              properties: &ref_226
                json_id:
                  type: string
                foil:
                  type: boolean
                language:
                  type: string
              example: &ref_227
                json_id: abcdef
                foil: true
                language: fr
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                title: Add a Listed Cards to a Card List Response
                type: object
                properties: &ref_228
                  listed_card:
                    type: object
                    properties:
                      json_id:
                        type: string
                      foil:
                        type: boolean
                      language:
                        type: string
                      created_at:
                        type: string
                  card:
                    type: object
                example: &ref_229
                  listed_card:
                    json_id: abcdef
                    foil: false
                    language: en
                    created_at: '2015-12-16T02:55:34.086Z'
                  card: {}
        '400':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
        '404':
          description: Card List or Card not found. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
  /card_lists/{uuid}/add_all_printings:
    post:
      tags:
        - Card Lists
      summary: Add All Printings
      description: Add all printings for cards that match a name
      operationId: AddAllPrintings
      parameters:
        - name: uuid
          in: path
          required: true
          schema:
            type: string
            example: 6da368f5-48f8-4a5c-8411-d00e1bb93bab
      requestBody:
        content:
          application/json:
            schema:
              title: Add All Printings to a Card List Request
              type: object
              required: &ref_230
                - name
                - foil
                - language
              properties: &ref_231
                name:
                  type: string
                foil:
                  type: boolean
                language:
                  type: string
              example: &ref_232
                name: Voidwalker
                foil: true
                language: fr
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                title: Add All Printings to a Card List Request
                type: object
                properties: &ref_233
                  listed_cards:
                    type: array
                    properties:
                      json_id:
                        type: string
                      foil:
                        type: boolean
                      language:
                        type: string
                      created_at:
                        type: string
                  cards:
                    type: object
                example: &ref_234
                  listed_cards:
                    - json_id: abcdef
                      foil: false
                      language: en
                      created_at: '2023-11-16T02:55:34.086Z'
                    - json_id: dftgch
                      foil: false
                      language: en
                      created_at: '2023-12-16T02:55:34.086Z'
                  cards:
                    6da368f5-48f8-4a5c-8411-d00e1bb93bab:
                      id: 1
                      name: Voidwalker
                      search_name: Voidwalker
                      description: A creature
                      created_at: '2023-12-16T02:55:34.086Z'
        '400':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
        '404':
          description: Card List or Card not found. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
  /card_lists/{uuid}/remove:
    delete:
      tags:
        - Card Lists
      summary: Remove Listed Card
      description: Remove a Listed Card from a Card List
      operationId: RemoveListedCard
      parameters:
        - name: uuid
          in: path
          required: true
          schema:
            type: string
            example: 6da368f5-48f8-4a5c-8411-d00e1bb93bab
      requestBody:
        content:
          application/json:
            schema:
              title: Remove a Listed Card to a Card List Request
              type: object
              required: &ref_235
                - json_id
                - foil
                - language
              properties: &ref_236
                json_id:
                  type: string
                foil:
                  type: boolean
                language:
                  type: string
              example: &ref_237
                json_id: abcdef
                foil: true
                language: fr
      responses:
        '200':
          description: ''
        '400':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
        '404':
          description: Listed Card not found. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
  /card_lists/{uuid}/selection:
    post:
      tags:
        - Card Lists
      summary: Add Card User Selection
      description: Creates Listed Cards based on selection of Card Users
      operationId: CardListSelection
      parameters:
        - name: uuid
          in: path
          required: true
          schema:
            type: string
            items:
              type: string
            example: 6da368f5-48f8-4a5c-8411-d00e1bb93bab
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
                - card_users
              properties:
                card_users:
                  type: array
                  items:
                    type: number
              example:
                card_users:
                  - 1
                  - 2
                  - 3
        required: true
      responses:
        '200':
          description: ''
        '400':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
        '404':
          description: Listed Card not found. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
  /card_lists/{uuid}/commit:
    post:
      tags:
        - Card Lists
      summary: Commit to CardList
      description: Creates ListedCards for all staged Card Users
      operationId: CardListCommit
      parameters:
        - name: uuid
          in: path
          required: true
          schema:
            type: string
            example: 6da368f5-48f8-4a5c-8411-d00e1bb93bab
        - name: input_session
          in: query
          required: true
          schema:
            type: string
            example: 7cfabcf0-438a-48f5-a4af-5b0f89f768a4
      responses:
        '200':
          description: ''
        '400':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
  /card_sets:
    get:
      tags:
        - Card Set
      summary: Get All Card Sets
      description: Get all sets
      operationId: GetAllCardSets
      parameters:
        - name: query
          in: query
          required: false
          schema:
            type: string
            items:
              type: string
            example: Invasion
          description: string to fuzzy match against the set name
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Get all card sets
                required: &ref_238
                  - card_sets
                type: object
                properties: &ref_239
                  card_sets:
                    type: array
                    example:
                      - id: 1
                        name: invasion
                        set_type: expansion
                        price_api_name: card_kingdom
                        block_name: Urza's Saga
                        set_code: INV
                        gatherer_set_code: INV
                        release_date: '2004-10-27'
                    description: ''
  /card_sets/type:
    get:
      tags:
        - Card Set
      summary: Get All Card Set Types
      description: Get all set types
      operationId: GetAllCardSetTypes
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Get all card set types
                required: &ref_240
                  - types
                type: object
                properties: &ref_241
                  types:
                    type: array
                    example:
                      - masterpiece
                      - draft_innovation
                      - expansion
                    description: ''
  /poke_cards/search:
    get:
      tags:
        - Poke Card
      description: Search all `poke cards`. This API is both public and private, if the user is logged in an owned count is returned, if they are not logged in, the owned count fields are not present.
      summary: Search all cards
      operationId: SearchPokeCards
      parameters:
        - name: order
          in: query
          required: false
          schema: &ref_42
            type: string
            default: asc
            enum:
              - asc
              - desc
        - name: group_by
          in: query
          required: false
          schema: &ref_43
            type: string
            default: printing
            enum:
              - printing
              - name
              - none
        - name: per_page
          in: query
          required: false
          schema: &ref_46
            type: integer
            default: 100
            enum:
              - 100
              - 300
              - 600
        - name: page
          in: query
          required: false
          schema: &ref_47
            type: integer
            default: 1
        - name: name
          in: query
          required: false
          schema: &ref_44
            type: string
        - name: query
          in: query
          required: false
          schema: &ref_45
            type: string
        - name: set_names
          in: query
          required: false
          schema: &ref_51
            type: object
            properties:
              with:
                type: array
                items:
                  type: string
              without:
                type: array
                items:
                  type: string
        - name: starts_with
          in: query
          required: false
          schema: &ref_52
            type: string
          description: matches the first letter (case insensitive) of a poke card's name
        - name: set_starts_with
          in: query
          required: false
          schema: &ref_53
            type: string
          description: matches the first letter (case insensitive) of a poke card's set name
        - name: types
          in: query
          required: false
          schema: &ref_48
            type: object
            properties:
              with:
                type: array
                items:
                  type: string
              without:
                type: array
                items:
                  type: string
              multi:
                type: boolean
          description: Accepted types can be retrieved from the `/types` route.
        - name: sub_types
          in: query
          required: false
          schema: &ref_49
            type: object
            properties:
              with:
                type: array
                items:
                  type: string
              without:
                type: array
                items:
                  type: string
              multi:
                type: boolean
        - name: super_type
          in: query
          required: false
          schema: &ref_50
            type: object
            properties:
              with:
                type: array
                items:
                  type: string
              without:
                type: array
                items:
                  type: string
            description: As `poke_cards` only have 1 `super_type`, all values are filtered using OR logic
        - name: artist
          in: query
          required: false
          schema: &ref_62
            type: object
            properties:
              with:
                type: array
                items:
                  type: string
              without:
                type: array
                items:
                  type: string
            description: As `poke_cards` only have 1 `artist`, all values are filtered using OR logic
        - name: rarity_filter
          in: query
          required: false
          schema: &ref_65
            type: array
            items:
              type: string
              enum:
                - Amazing Rare
                - Classic Collection
                - Common
                - Double Rare
                - Hyper Rare
                - Illustration Rare
                - LEGEND
                - Promo
                - Radiant Rare
                - Rare
                - Rare ACE
                - Rare BREAK
                - Rare Holo
                - Rare Holo EX
                - Rare Holo GX
                - Rare Holo LV.X
                - Rare Holo Star
                - Rare Holo V
                - Rare Holo VMAX
                - Rare Holo VSTAR
                - Rare Prime
                - Rare Prism Star
                - Rare Rainbow
                - Rare Secret
                - Rare Shining
                - Rare Shiny
                - Rare Shiny GX
                - Rare Ultra
                - Special Illustration Rare
                - Trainer Gallery Rare Holo
                - Ultra Rare
                - Uncommon
        - name: level
          in: query
          required: false
          schema: &ref_63
            type: object
            properties:
              min:
                type: integer
              max:
                type: integer
        - name: hp
          in: query
          required: false
          schema: &ref_64
            type: object
            properties:
              min:
                type: integer
              max:
                type: integer
        - name: rules_text
          in: query
          required: false
          schema: &ref_60
            type: string
        - name: flavor_text
          in: query
          required: false
          schema: &ref_61
            type: string
        - name: fields
          in: query
          required: false
          schema:
            type: array
            items: &ref_54
              type: string
              enum:
                - name
                - super_type
                - collector_number
                - rarity
                - level
                - hp
                - evolves_from
                - evolves_to
                - artist
                - flavor_text
                - regulation_mark
                - types
                - sub_types
                - rules
                - set_uuid
                - set_pokemontcgio_id
                - set_name
                - set_release_date
                - set_series
          description: Request specific fields to be included in the response. All fields are returned by default, but it's recommended that you use this parameter to specify only the fields you need to reduce the size of the response.
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Search Poke Cards response
                required: &ref_242
                  - cards
                type: object
                properties: &ref_243
                  cards:
                    type: array
                    items:
                      type: object
                      example:
                        - uuid: 80a8c7f6-e2b9-4d69-9e30-710e264e575d
                          name: Parasect
                          super_type: Nobis
                          collector_number: '317'
                          rarity: Common
                          level: Twelve
                          hp: 20
                          evolves_from: Paras
                          evolves_to:
                            - Parasectest
                          artist: Maurita William
                          flavor_text: Facere dolor quam iure.
                          regulation_mark: autem
                          types:
                            - odit
                            - alias
                            - ut
                          sub_types:
                            - ipsa
                            - ut
                          rules: null
                          set_uuid: 1094a640-54eb-41d7-a749-26a2a3cdccd6
                          set_pokemontcgio_id: velit-et
                          set_name: consequatur impedit
                          set_release_date: '2019-04-20'
                          set_series: illum
                        - uuid: 80a8c7f6-e2b9-4d69-9e30-710e264e575d
                          name: Parasect
                          super_type: Nobis
                          collector_number: '317'
                          rarity: Common
                          level: Twelve
                          hp: 20
                          evolves_from: Paras
                          evolves_to:
                            - Parasectest
                          artist: Maurita William
                          flavor_text: Facere dolor quam iure.
                          regulation_mark: autem
                          types:
                            - odit
                            - alias
                            - ut
                          sub_types:
                            - ipsa
                            - ut
                          rules: null
                          set_uuid: 1094a640-54eb-41d7-a749-26a2a3cdccd6
                          set_pokemontcgio_id: velit-et
                          set_name: consequatur impedit
                          set_release_date: '2019-04-20'
                          set_series: illum
                    description: ''
                example: &ref_244
                  cards:
                    - uuid: 80a8c7f6-e2b9-4d69-9e30-710e264e575d
                      name: Parasect
                      super_type: Nobis
                      collector_number: '317'
                      rarity: Common
                      level: Twelve
                      hp: 20
                      evolves_from: Paras
                      evolves_to:
                        - Parasectest
                      artist: Maurita William
                      flavor_text: Facere dolor quam iure.
                      regulation_mark: autem
                      types:
                        - odit
                        - alias
                        - ut
                      sub_types:
                        - ipsa
                        - ut
                      rules: null
                      set_uuid: 1094a640-54eb-41d7-a749-26a2a3cdccd6
                      set_pokemontcgio_id: velit-et
                      set_name: consequatur impedit
                      set_release_date: '2019-04-20'
                      set_series: illum
                    - uuid: 80a8c7f6-e2b9-4d69-9e30-710e264e575d
                      name: Parasect
                      super_type: Nobis
                      collector_number: '317'
                      rarity: Common
                      level: Twelve
                      hp: 20
                      evolves_from: Paras
                      evolves_to:
                        - Parasectest
                      artist: Maurita William
                      flavor_text: Facere dolor quam iure.
                      regulation_mark: autem
                      types:
                        - odit
                        - alias
                        - ut
                      sub_types:
                        - ipsa
                        - ut
                      rules: null
                      set_uuid: 1094a640-54eb-41d7-a749-26a2a3cdccd6
                      set_pokemontcgio_id: velit-et
                      set_name: consequatur impedit
                      set_release_date: '2019-04-20'
                      set_series: illum
  /poke_cards/search/compact:
    get:
      tags:
        - Poke Card
      description: Search all poke_cards. Unlike `/search`, the response is not paginated, and instead of an object containing card properties, only the cards' UUIDs are returned.
      summary: Search all cards
      operationId: SearchCompactPokeCards
      parameters:
        - name: order
          in: query
          required: false
          schema: *ref_42
        - name: group_by
          in: query
          required: false
          schema: *ref_43
        - name: name
          in: query
          required: false
          schema: *ref_44
        - name: query
          in: query
          required: false
          schema: *ref_45
        - name: per_page
          in: query
          required: false
          schema: *ref_46
        - name: page
          in: query
          required: false
          schema: *ref_47
        - name: types
          in: query
          required: false
          schema: *ref_48
          description: Accepted types can be retrieved from the `/types` route.
        - name: sub_types
          in: query
          required: false
          schema: *ref_49
        - name: super_type
          in: query
          required: false
          schema: *ref_50
        - name: set_names
          in: query
          required: false
          schema: *ref_51
        - name: starts_with
          in: query
          required: false
          schema: *ref_52
          description: matches the first letter (case insensitive) of a poke card's name
        - name: set_starts_with
          in: query
          required: false
          schema: *ref_53
          description: matches the first letter (case insensitive) of a poke card's set name
        - name: fields
          in: query
          required: false
          schema:
            type: array
            items: *ref_54
          description: Request specific fields to be included in the response. All fields are returned by default, but it's recommended that you use this parameter to specify only the fields you need to reduce the size of the response.
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Search Poke Cards compact response
                required: &ref_245
                  - cards
                type: object
                properties: &ref_246
                  cards:
                    type: array
                    items:
                      type: string
                example: &ref_247
                  - 215482b8-e8e8-4a31-b0ab-8fbf472a4c32
                  - 41ade325-ac4c-4e3b-ac9b-546ee624ec9b
                  - 31fe09cf-8110-4b82-a165-43c683b6ce4a
  /poke_cards/printings:
    post:
      tags:
        - Poke Card
      summary: Get printing details for poke cards by UUIDs
      description: |-
        + Duplicate UUIDs are ignored 
        + Response is sorted by release date 
        + Response is empty when there are no shared printings 
        + Invalid ids respond with 'not found'
      operationId: GetPokeCardsPrintings
      requestBody:
        description: ''
        content:
          application/json:
            schema:
              title: Get printings for poke cards by UUIDs
              required: &ref_248
                - uuids
              type: object
              properties: &ref_249
                uuids:
                  type: object
                  example:
                    - 225cc55f-bdab-47e9-9056-8bbcdedc8504
                    - 3b980296-ca3b-44e7-96d8-f940426cd96b
                  description: ''
        required: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Get all poke cards printings response
                required: &ref_250
                  - printings
                type: object
                properties: &ref_251
                  printings:
                    type: object
                    example:
                      - uuid: 80a8c7f6-e2b9-4d69-9e30-710e264e575d
                        name: Parasect
                        super_type: Nobis
                        collector_number: '317'
                        rarity: Common
                        level: Twelve
                        hp: 20
                        evolves_from: Paras
                        evolves_to:
                          - Parasectest
                        artist: Maurita William
                        flavor_text: Facere dolor quam iure.
                        regulation_mark: autem
                        types:
                          - odit
                          - alias
                          - ut
                        sub_types:
                          - ipsa
                          - ut
                        rules: null
                        set_uuid: 1094a640-54eb-41d7-a749-26a2a3cdccd6
                        set_pokemontcgio_id: velit-et
                        set_name: consequatur impedit
                        set_release_date: '2019-04-20'
                        set_series: illum
                        price_tcgplayer: 2.5
                      - uuid: 80a8c7f6-e2b9-4d69-9e30-710e264e575d
                        name: Parasect
                        super_type: Nobis
                        collector_number: '317'
                        rarity: Common
                        level: Twelve
                        hp: 20
                        evolves_from: Paras
                        evolves_to:
                          - Parasectest
                        artist: Maurita William
                        flavor_text: Facere dolor quam iure.
                        regulation_mark: autem
                        types:
                          - odit
                          - alias
                          - ut
                        sub_types:
                          - ipsa
                          - ut
                        rules: null
                        set_uuid: 1094a640-54eb-41d7-a749-26a2a3cdccd6
                        set_pokemontcgio_id: velit-et
                        set_name: consequatur impedit
                        set_release_date: '2019-04-20'
                        set_series: illum
                        price_tcgplayer: 6.3
        '400':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
  /poke_cards/super_types:
    get:
      tags:
        - Poke Card
      summary: Get all super_types
      description: Get all super_types
      operationId: GetPokeSuperTypes
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Get all card super_types
                required: &ref_252
                  - super_types
                type: object
                properties: &ref_253
                  super_types:
                    type: array
                    example:
                      - Energy
                      - Pokemon
                      - Trainer
                      - ...
                    description: ''
  /poke_cards/sub_types:
    get:
      tags:
        - Poke Card
      summary: Get all sub_types
      description: Get all sub_types
      operationId: GetPokeSubTypes
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Get all card sub_types
                required: &ref_254
                  - sub_types
                type: object
                properties: &ref_255
                  sub_types:
                    type: array
                    example:
                      - BREAK
                      - Baby
                      - Basic
                      - LEGEND
                      - Special
                      - Stadium
                      - ...
                    description: ''
  /poke_cards/types:
    get:
      tags:
        - Poke Card
      summary: Get all types
      description: Get all types
      operationId: GetPokeTypes
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Get all card types
                required: &ref_256
                  - types
                type: object
                properties: &ref_257
                  types:
                    type: array
                    example:
                      - Grass
                      - Water
                      - Fighting
                      - Dragon
                      - Fire
                      - Lightning
                      - ...
                    description: ''
  /poke_cards/rarities:
    get:
      tags:
        - Poke Card
      summary: Get all rarities
      description: Get all rarities
      operationId: GetPokeRarities
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Get all card rarities
                required: &ref_258
                  - rarities
                type: object
                properties: &ref_259
                  rarities:
                    type: array
                    example:
                      - Rare Secret
                      - Rare Shining
                      - Rare Shiny
                      - Rare Shiny GX
                      - Rare Ultra
                      - Ultra Rare
                      - Rare
                      - Uncommon
                      - Common
                      - ...
                    description: ''
  /poke_cards/finishes:
    get:
      tags:
        - Poke Card
      summary: Get all finishes
      description: Get all finishes
      operationId: GetPokeFinishes
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Get all card finishes
                required: &ref_260
                  - finishes
                type: object
                properties: &ref_261
                  finishes:
                    type: array
                    example:
                      - name: Holo
                        uuid: 80a8c7f6-e2b9-4d69-9e30-710e264e575d
                        fallback_order: 1
                      - name: Reverse Holo
                        uuid: 225cc55f-bdab-47e9-9056-8bbcdedc8504
                        fallback_order: 2
                      - name: Full Art Holo
                        uuid: 3b980296-ca3b-44e7-96d8-f940426cd96b
                        fallback_order: 3
                      - ...
                    description: ''
  /poke_cards/version:
    get:
      tags:
        - Poke Card
      summary: Get current version
      description: Get current version
      operationId: GetPokeVersion
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Get version
                required: &ref_262
                  - version
                type: object
                properties: &ref_263
                  version:
                    type: string
                    example: 80a8c7f6-e2b9-4d69-9e30-710e264e575d
                    description: ''
  /poke_cards/update_version:
    get:
      tags:
        - Poke Card
      summary: Update current version
      description: Update current version
      operationId: UpdatePokeVersion
      parameters:
        - name: new_version
          in: query
          required: true
          schema:
            type: string
            example: 7cfabcf0-438a-48f5-a4af-5b0f89f768a4
      responses:
        '200':
          description: ''
  /poke_card_users/add:
    post:
      tags:
        - Poke Card Users
      summary: Add a poke card user
      description: Add a poke card user
      operationId: AddPokeCardUser
      parameters:
        - name: uuid
          in: query
          required: true
          schema:
            type: string
        - name: source
          in: query
          required: false
          schema:
            type: string
            enum:
              - cardbot
              - app
              - manual
              - import
              - unknown
        - name: condition
          in: query
          required: false
          schema:
            type: string
            default: Near Mint
            enum:
              - Near Mint
              - Lightly Played
              - Moderately Played
              - Heavily Played
              - Damaged
        - name: finish_uuid
          in: query
          required: false
          schema:
            type: string
            example: 7cfabcf0-438a-48f5-a4af-5b0f89f768a4
      responses:
        '201':
          description: The poke card user was successfully added
          content:
            application/json:
              schema:
                title: Add Poke Card User response
                required: &ref_264
                  - poke_card_user
                type: object
                properties: &ref_265
                  poke_card_user:
                    type: object
                example: &ref_266
                  poke_card_user:
                    id: 1
                    uuid: e4654973-1674-4ee0-a923-5fb844bd1622
                    poke_card_id: 4
                    user_id: 3
                    scanned_image:
                      url: null
                    scan_metadata: null
                    poke_input_session_id: 3
                    condition: Near Mint
                    finish: Normal
                    created_at: 2015-10-30 18:19:26 +1100
                    updated_at: 2015-10-30 18:19:26 +1100
                    deleted_at: nil
        '400':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
        '404':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
  /poke_card_users:
    patch:
      tags:
        - Poke Card Users
      summary: Update a poke card user
      description: Updates a poke card user's attributes
      operationId: UpdatePokeCardUser
      parameters:
        - name: uuids
          in: query
          required: true
          schema:
            type: array
            description: array of UUIDs
        - name: card_ids
          in: query
          required: true
          schema:
            items:
              type: string
              example: '[''7cfabcf0-438a-48f5-a4af-5b0f89f768a4'', ''6da368f5-48f8-4a5c-8411-d00e1bb93bab'']'
        - name: condition
          in: query
          required: false
          schema:
            type: string
            enum:
              - Near Mint
              - Lightly Played
              - Moderately Played
              - Heavily Played
              - Damaged
        - name: annotate_scan
          in: query
          required: false
          schema:
            type: boolean
          description: Create or update scan metadata with updated card info
        - name: finish_uuid
          in: query
          required: false
          schema:
            type: string
            example: 7cfabcf0-438a-48f5-a4af-5b0f89f768a4
        - name: printing
          in: query
          required: false
          schema:
            type: string
            example: 7cfabcf0-438a-48f5-a4af-5b0f89f768a4
          description: Update printing of all poke card users. The new printing must be valid for all or the request will fail
      responses:
        '200':
          description: The poke card user was successfully updated
          content:
            application/json:
              schema:
                title: Update Poke Card User response
                required: &ref_267
                  - card_users
                type: object
                properties: &ref_268
                  card_users:
                    type: array
                  cards:
                    type: object
                    items:
                      type: object
                example: &ref_269
                  card_users:
                    - uuid: 9c3e7a2e-5750-4e68-b2f6-3bf6758c3ada
                      poke_card_uuid: c33c55e5-252f-4bb8-9c39-ef2b181588c6
                      input_session_uuid: 63feaaae-743f-49bb-b787-9d5a6ca30ac4
                      condition: Near Mint
                      finish: Normal
                      scanned_image_url: nil
                      price: 2244
                      created_at: '2024-01-29T02:21:20.304Z'
                      updated_at: '2024-01-29T02:21:20.476Z'
                      source_type: cardbot
                      scan_metadata: nil
                  cards:
                    e4654973-1674-4ee0-a923-5fb844bd1622:
                      uuid: e4654973-1674-4ee0-a923-5fb844bd1622
                      pokemontcgio_id: ADV-2
                      name: Beedrill
                      collector_number: 23
                      rarity: Rare Holo LV.X
                      super_type: Sint
                      level: tenetur
                      hp: 40
                      evolves_from: Nidoran
                      evolves_to:
                        - Shannon Williamson
                      artist: Hermina Boyle
                      flavor_text: Officia dolores voluptas aliquid.
                      regulation_mark: quaerat
                      types:
                        - maiores
                        - neque
                        - ut
                      sub_types: []
                      rules: null
                      set_uuid: 741ca319-7524-4738-b108-163c90aa1512
                      set_pokemontcgio_id: et-nostrum
                      set_name: incidunt ut
                      set_release_date: '2007-11-11'
                      set_series: voluptatem
        '400':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
        '404':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
    delete:
      tags:
        - Poke Card Users
      summary: Delete Poke Card Users
      description: Bulk deletes poke cards for the currently authenticated user. On request success all the poke cards are deleted from the user. On failure the request is rollbacked
      operationId: DeletePokeCard
      parameters:
        - name: uuids
          in: query
          required: true
          schema:
            type: array
            items:
              type: string
              example: '[''7cfabcf0-438a-48f5-a4af-5b0f89f768a4'', ''6da368f5-48f8-4a5c-8411-d00e1bb93bab'']'
      responses:
        '200':
          description: ''
        '400':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
        '404':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
  /poke_card_users/poke_card:
    patch:
      tags:
        - Poke Card Users
      summary: Update poke_card
      description: Update poke_card for poke_card_users
      operationId: PatchPokeCardUsersPokeCard
      parameters:
        - name: uuid
          in: query
          required: true
          schema:
            type: string
        - name: poke_card_user_uuids
          in: query
          required: true
          schema:
            type: array
            items:
              type: string
        - name: annotate_scan
          in: query
          required: false
          schema:
            type: boolean
          description: Create or update scan metadata with updated card info
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Patch poke card users poke card response
                required: &ref_270
                  - card_users
                  - cards
                type: object
                properties: &ref_271
                  card_users:
                    type: array
                    items:
                      type: object
                    example:
                      card_users:
                        - uuid: 9c3e7a2e-5750-4e68-b2f6-3bf6758c3ada
                          poke_card_uuid: c33c55e5-252f-4bb8-9c39-ef2b181588c6
                          input_session_uuid: 63feaaae-743f-49bb-b787-9d5a6ca30ac4
                          condition: Near Mint
                          finish: Foil
                          scanned_image_url: nil
                          price: 2244
                          created_at: '2024-01-29T02:21:20.304Z'
                          updated_at: '2024-01-29T02:21:20.476Z'
                          source_type: cardbot
                          scan_metadata: nil
                        - uuid: a5a31a17-d0ee-4e34-85f3-284feddff404
                          poke_card_uuid: 9c3e7a2e-5750-4e68-b2f6-3bf6758c3ada
                          input_session_uuid: 63feaaae-743f-49bb-b787-9d5a6ca30ac4
                          condition: Damaged
                          finish: Normal
                          scanned_image_url: nil
                          price: 560
                          created_at: '2024-01-29T02:21:20.304Z'
                          updated_at: '2024-01-29T02:21:20.476Z'
                          source_type: import
                          scan_metadata: nil
                      cards:
                        e4654973-1674-4ee0-a923-5fb844bd1622:
                          uuid: e4654973-1674-4ee0-a923-5fb844bd1622
                          pokemontcgio_id: ADV-2
                          name: Beedrill
                          collector_number: 23
                          rarity: Rare Holo LV.X
                          super_type: Sint
                          level: tenetur
                          hp: 40
                          evolves_from: Nidoran
                          evolves_to:
                            - Shannon Williamson
                          artist: Hermina Boyle
                          flavor_text: Officia dolores voluptas aliquid.
                          regulation_mark: quaerat
                          types:
                            - maiores
                            - neque
                            - ut
                          sub_types: []
                          rules: null
                          set_uuid: 741ca319-7524-4738-b108-163c90aa1512
                          set_pokemontcgio_id: et-nostrum
                          set_name: incidunt ut
                          set_release_date: '2007-11-11'
                          set_series: voluptatem
        '400':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
  /poke_card_users/{uuid}/scanned_image:
    post:
      tags:
        - Poke Card Users
      summary: Upload a scanned image
      description: Uploads a scanned image and attaches it to an existing poke card user
      operationId: PostPokeCardUsersScannedImage
      parameters:
        - name: uuid
          in: path
          required: true
          schema:
            type: integer
        - name: scanned_image
          in: query
          required: true
          schema:
            type: string
      responses:
        '201':
          description: The image was successfully uploaded.
          content:
            application/json:
              schema:
                title: Scanned images response
                required: &ref_272
                  - scanned_image
                type: object
                properties: &ref_273
                  scanned_image:
                    type: string
                example: &ref_274
                  scanned_image: /uploads/poke_card_user/scanned_image/large_9ab2efab-840e-4c9d-8d7e-d428325d69d0.jpg
        '400':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              example: *ref_0
        '404':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
  /poke_card_sets:
    get:
      tags:
        - Poke Card Sets
      summary: Get All Poke Card Sets
      description: Get all Poke Card Sets
      operationId: GetAllPokeCardSets
      parameters:
        - name: query
          in: query
          required: false
          schema:
            type: string
            items:
              type: string
            example: Scarlet
          description: string to fuzzy match against the poke card set name
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Get all Poke card sets
                required: &ref_275
                  - poke_card_sets
                type: object
                properties: &ref_276
                  card_sets:
                    type: array
                    example:
                      - id: 1
                        uuid: b6c75bf3-d1c2-4967-97c7-d63e9e1fdcb1
                        name: Scarlet & Violet
                        pokemontcgio_id: Scarlet & Violet
                        release_date: '2004-10-27'
                        series: svi
                        search_name: Scarlet & Violet
                        created_at: '2023-02-16T22:47:02.794Z'
                        updated_at: '2023-02-16T22:47:02.794Z'
                    description: ''
  /collection/pokemon/search:
    post:
      tags:
        - Collection
      summary: Search Pokemon Collection
      description: Search the collection for pokemon cards. Similar to search, but with different parameters and response payload.
      operationId: SearchPokemonCollection
      security:
        - OAuth2:
            - user
        - cookieAuth: []
      parameters:
        - name: quantity
          in: query
          required: false
          schema: *ref_55
        - name: source
          in: query
          required: false
          schema: *ref_56
        - name: input_session
          in: query
          required: false
          schema: *ref_57
        - name: condition
          in: query
          required: false
          schema: &ref_82
            type: array
            items:
              type: string
              enum:
                - Near Mint
                - Lightly Played
                - Moderately Played
                - Heavily Played
                - Damaged
            example: '[''Near Mint'', ''Damaged'']'
        - name: sort_by
          in: query
          required: false
          schema: *ref_58
        - name: confidence
          in: query
          required: false
          schema: *ref_59
        - name: group_by
          in: query
          required: false
          schema: *ref_43
        - name: order
          in: query
          required: false
          schema: *ref_42
        - name: name
          in: query
          required: false
          schema: *ref_44
        - name: query
          in: query
          required: false
          schema: *ref_45
        - name: rules_text
          in: query
          required: false
          schema: *ref_60
        - name: flavor_text
          in: query
          required: false
          schema: *ref_61
        - name: per_page
          in: query
          required: false
          schema: *ref_46
        - name: page
          in: query
          required: false
          schema: *ref_47
        - name: types
          in: query
          required: false
          schema: *ref_48
          description: Accepted types can be retrieved from the `/types` route.
        - name: sub_types
          in: query
          required: false
          schema: *ref_49
        - name: super_type
          in: query
          required: false
          schema: *ref_50
        - name: artist
          in: query
          required: false
          schema: *ref_62
        - name: set_names
          in: query
          required: false
          schema: *ref_51
        - name: starts_with
          in: query
          required: false
          schema: *ref_52
          description: matches the first letter (case insensitive) of a poke card's name
        - name: set_starts_with
          in: query
          required: false
          schema: *ref_53
          description: matches the first letter (case insensitive) of a poke card's set name
        - name: level
          in: query
          required: false
          schema: *ref_63
        - name: hp
          in: query
          required: false
          schema: *ref_64
        - name: rarity_filter
          in: query
          required: false
          schema: *ref_65
        - name: fields
          in: query
          required: false
          schema:
            type: object
            properties:
              summary:
                type: array
                items: &ref_83
                  type: string
                  enum:
                    - page_value
                    - total_count
                    - total_value
                    - total_groups
              group:
                type: array
                items: &ref_84
                  type: string
                  enum:
                    - latest_created
                    - latest_committed
                    - group_price
                    - max_price
              card:
                type: array
                items: *ref_54
              element:
                type: array
                items: &ref_277
                  type: string
                  enum:
                    - source_type
                    - scanned_image_url
                    - price
                    - created_at
                    - committed_at
          description: Request specific fields to be included in the response. All fields are returned by default, but it's recommended that you use this parameter to specify only the fields you need to reduce the size of the response.
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Pokemon Search Collection response
                type: object
                example: &ref_278
                  page_count: 8
                  page_value: 2300
                  total_groups: 4
                  total_count: 8
                  total_value: 1234
                  collection_items:
                    - latest_created: '2017-01-24T03:21:20.260Z'
                      latest_committed: '2017-01-24T03:21:20.260Z'
                      group_count: 1
                      group_price: 79.745994
                      group_elements:
                        - uuid: 64da69b8-c9cf-4cca-bd15-36270242db41
                          poke_card_uuid: 51811f2a-7002-4ba7-98d8-5b09d887975c
                          input_session_uuid: 51811f2a-7002-4ba7-98d8-5b09d887975c
                          committed_at: '2017-01-24T03:21:20.260Z'
                          created_at: '2017-01-24T03:21:20.260Z'
                          source_type: app
                          price: 10
                          scanned_image_url: www.image.com.au/123456
                          scan_metadata:
                            confidence: 0.95
                            crop:
                              tl:
                                - 0.1
                                - 0.2
                              tr:
                                - 0.1
                                - 0.2
                              br:
                                - 0.1
                                - 0.2
                              bl:
                                - 0.1
                                - 0.2
                            flipped: true
                            mcr_version: 7.9.1
                            evermind_version: 0.6.2
                  cards:
                    51811f2a-7002-4ba7-98d8-5b09d887975c:
                      uuid: 51811f2a-7002-4ba7-98d8-5b09d887975c
                      name: Pikachu
                      super_type: Pokemon
                      collector_number: 1
                      rarity: Rare
                      level: Basic
                      hp: 50
                      evolves_from: null
                      evolves_to:
                        - Raichu
                      artist: John Doe
                      flavor_text: Pika pikachu
                      regulation_mark: null
                      types:
                        - Electric
                      sub_types: []
                      rules: null
                      set_uuid: 51811f2a-7002-4ba7-98d8-5b09d887975c
                      set_pokemontcgio_id: foo
                      set_name: Foo and Bar
                      set_release_date: '2017-01-24T03:21:20.260Z'
                      set_series: Foo
  /poke_card_identifiers:
    get:
      tags:
        - Poke Card Identifiers
      summary: Get all poke card identifiers
      description: Get all poke card identifiers
      operationId: GetAllPokeCardIdentifiers
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Get all poke card identifiers
                required: &ref_279
                  - poke_card_identifiers
                type: object
                properties: &ref_280
                  card_identifiers:
                    type: array
                    example:
                      - card_uuid: c3b70b5b-b04b-49dd-9ad7-480708bac61f
                        service: tcgplayer
                        external_id: 34268
                        name: Golbat (348)
                        collector_number: '348'
                        match_info: null
                        set_name: null
                    description: ''
  /poke_skus:
    get:
      tags:
        - Poke Skus
      summary: Get all poke skus
      description: Get all poke skus
      operationId: GetAllPokeSkus
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Get all poke skus
                required: &ref_281
                  - poke_skus
                type: object
                properties: &ref_282
                  skus:
                    type: array
                    example:
                      - card_uuid: 88065402-6b90-4dc2-8fec-3a3e6cc124c7
                        code: 2836104638
                        service: tcgplayer
                        condition: Near Mint
                        finish: normal
                        language: en
                    description: ''
  /poke_finishes:
    get:
      tags:
        - Poke Finishes
      summary: Get all poke finishes
      description: Get all poke finishes
      operationId: GetAllPokeFinishes
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Get all poke finishes
                required: &ref_283
                  - poke_finishes
                type: object
                properties: &ref_284
                  finishes:
                    type: array
                    example:
                      - uuid: 88065402-6b90-4dc2-8fec-3a3e6cc124c7
                        name: Normal
                        fallback_order: 1
                      - uuid: 4825dfdb-21bd-44a9-8f04-53abe4be659b
                        name: Holo Foil
                        fallback_order: 2
                      - uuid: c0f30e1b-01b7-43ec-8f7b-bf1609cc6d15
                        name: Reverse Holo Foil
                        fallback_order: 3
                      - ...
                    description: ''
  /poke_rarities:
    get:
      tags:
        - Poke Rarities
      summary: Get all poke rarities
      description: Get all poke rarities
      operationId: GetAllPokeRarities
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Get all poke rarities
                required: &ref_285
                  - poke_rarities
                type: object
                properties: &ref_286
                  rarities:
                    type: array
                    example:
                      - uuid: 88065402-6b90-4dc2-8fec-3a3e6cc124c7
                        name: Common
                        rarity_order: 1
                      - uuid: 4825dfdb-21bd-44a9-8f04-53abe4be659b
                        name: Uncommon
                        rarity_order: 2
                      - uuid: c0f30e1b-01b7-43ec-8f7b-bf1609cc6d15
                        name: Rare
                        rarity_order: 3
                      - ...
                    description: ''
  /yugi_cards:
    get:
      tags:
        - Yugi Card
      description: Get all yugi cards
      summary: Get all yugi cards
      operationId: IndexYugiCards
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Get all yugi cards response
                required: &ref_287
                  - cards
                type: object
                properties: &ref_288
                  cards:
                    type: array
                    items:
                      type: object
                      example:
                        uuid: 80a8c7f6-e2b9-4d69-9e30-710e264e575d
                        name: Eagle-Masked Anbu Member
                        ygoprodeck_id: 8740
                        type: Nobis
                        frame_type: Culpa
                        description: Perferendis
                        attack: 18
                        defense: 12
                        level: 10
                        race: Lizard
                        archetype: Sit
  /yugi_cards/search:
    post:
      tags:
        - Yugi Card
      description: Search all `yugi cards`. This API is both public and private, if the user is logged in an owned count is returned, if they are not logged in, the owned count fields are not present.
      summary: Search all yugi cards
      operationId: SearchYugiCards
      parameters:
        - name: order
          in: query
          required: false
          schema: &ref_66
            type: string
            default: asc
            enum:
              - asc
              - desc
        - name: group_by
          in: query
          required: false
          schema: &ref_67
            type: string
            default: printing
            enum:
              - printing
              - name
              - none
            description: printing groups by yugi card set
        - name: name
          in: query
          required: false
          schema: &ref_68
            type: string
        - name: per_page
          in: query
          required: false
          schema: &ref_70
            type: integer
            default: 100
            enum:
              - 100
              - 300
              - 600
        - name: query
          in: query
          required: false
          schema: &ref_69
            type: string
        - name: page
          in: query
          required: false
          schema: &ref_71
            type: integer
            default: 1
        - name: set_names
          in: query
          required: false
          schema: &ref_72
            type: object
            properties:
              with:
                type: array
                items:
                  type: string
              without:
                type: array
                items:
                  type: string
          description: Payload can include `with`, `without`, or both
        - name: price
          in: query
          required: false
          schema: &ref_73
            type: object
            properties:
              source:
                type: string
                enum:
                  - tcg_player
                  - tcg_player_low
                  - tcg_player_mid
                  - tcg_player_high
                  - tcg_player_direct_low
                  - card_market
              min:
                type: number
              max:
                type: number
          description: when no source is passed, price source defaults to user preference.
        - name: printing_uuid
          in: query
          required: false
          schema: &ref_74
            type: string
        - name: race
          in: query
          required: false
          schema: &ref_75
            type: string
        - name: archetype
          in: query
          required: false
          schema: &ref_76
            type: string
        - name: starts_with
          in: query
          required: false
          schema: &ref_77
            type: string
          description: matches the first letter (case insensitive) of a yugi card's name
        - name: fields
          in: query
          required: false
          schema:
            type: array
            items: &ref_78
              type: string
              enum:
                - name
                - ygoprodeck_id
                - card_type
                - frame_type
                - description
                - attack
                - defense
                - level
                - race
                - archetype
                - printings
                - card_sets
                - min_price
                - avg_price
                - max_price
          description: Request specific fields to be included in the response. All fields are returned by default, but it's recommended that you use this parameter to specify only the fields you need to reduce the size of the response.
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Search Yugi Cards response
                required: &ref_289
                  - cards
                  - sets
                type: object
                properties: &ref_290
                  cards:
                    type: array
                    items:
                      type: object
                      example:
                        - uuid: 143196b9-e99b-4c7e-afa4-65e9e301ef9b
                        - archetype: Aut
                        - attack: 19
                        - card_sets:
                            84c79b22-5d3e-4fa1-8868-16f4c14b39e8:
                              code: ENU-uwd110
                              uuid: 84c79b22-5d3e-4fa1-8868-16f4c14b39e8
                              set_uuid: cc02833e-9654-4677-bb66-35a947c7a199
                              rarity_uuid: 5a00ce5b-4c5f-4861-ae0a-8de39eb8553f
                        - card_type: Sint
                        - defense: 23
                        - description: Quam
                        - frame_type: Sit
                        - level: 5
                        - max_price: 3500
                        - avg_price: 2560
                        - median_price: 2000
                        - min-price: 1500
                        - name: Agari
                        - Printings: {}
                        - race: Demon
                        - ygoprodeck_id: 58605000113
                  sets:
                    type: object
                    example:
                      - 45d8bcd3-b291-4f21-974e-821224e264ec:
                          name: id natus labore facilis animi
                          release_date: '2015-08-05'
                          set_code: CXE
                          uuid: 45d8bcd3-b291-4f21-974e-821224e264ec
                    description: ''
                example: &ref_291
                  cards:
                    - uuid: 4d9c2bcf-393d-47bc-920e-bf703ad961d9
                      archetype: Voluptatum
                      attack: 3,
                      avg_price: nil
                      card_sets:
                        f95afe0c-68bc-40fa-af4e-ab195f066f89:
                          code: MHC-kqavw107
                          uuid: f95afe0c-68bc-40fa-af4e-ab195f066f89
                          set_uuid: 6d476ab4-8aef-45cb-ba64-99bf37ab1320
                          rarity_uuid: 9a896714-a1e4-4035-bc59-b1f01657cf7f
                      card_type: Aspernatur
                      defense: 18
                      description: Quisquam
                      frame_type: Est
                      level: 2
                      max_price: nil
                      median_price: nil
                      min_price: nil
                      name: Benten
                      printings: {}
                      race: Minus
                      ygoprodeck_id: 84497000110
                  sets:
                    a55a0ed5-778a-4472-93b6-7341beddb21d:
                      name: pariatur harum quaerat vitae fuga
                      release_date: '2008-09-19'
                      set_code: QJV
                      uuid: a55a0ed5-778a-4472-93b6-7341beddb21d
  /yugi_cards/search/compact:
    get:
      tags:
        - Yugi Card
      description: Search all yugi_cards. Unlike `/search`, the response is not paginated, and instead of an object containing card properties, only the cards' UUIDs are returned.
      summary: Search all yugi cards
      operationId: SearchCompactYugiCards
      parameters:
        - name: order
          in: query
          required: false
          schema: *ref_66
        - name: group_by
          in: query
          required: false
          schema: *ref_67
        - name: name
          in: query
          required: false
          schema: *ref_68
        - name: query
          in: query
          required: false
          schema: *ref_69
        - name: per_page
          in: query
          required: false
          schema: *ref_70
        - name: page
          in: query
          required: false
          schema: *ref_71
        - name: set_names
          in: query
          required: false
          schema: *ref_72
          description: Payload can include `with`, `without`, or both
        - name: price
          in: query
          required: false
          schema: *ref_73
          description: when no source is passed, price source defaults to user preference.
        - name: printing_uuid
          in: query
          required: false
          schema: *ref_74
        - name: race
          in: query
          required: false
          schema: *ref_75
        - name: archetype
          in: query
          required: false
          schema: *ref_76
        - name: starts_with
          in: query
          required: false
          schema: *ref_77
          description: matches the first letter (case insensitive) of a yugi card's name
        - name: fields
          in: query
          required: false
          schema:
            type: array
            items: *ref_78
          description: Request specific fields to be included in the response. All fields are returned by default, but it's recommended that you use this parameter to specify only the fields you need to reduce the size of the response.
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Search Yugi Cards compact response
                required: &ref_292
                  - cards
                type: object
                properties: &ref_293
                  cards:
                    type: array
                    items:
                      type: string
                example: &ref_294
                  - 215482b8-e8e8-4a31-b0ab-8fbf472a4c32
                  - 41ade325-ac4c-4e3b-ac9b-546ee624ec9b
                  - 31fe09cf-8110-4b82-a165-43c683b6ce4a
  /yugi_cards/types:
    get:
      tags:
        - Yugi Card
      summary: Get all types
      description: Get all types
      operationId: GetYugiTypes
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Get all card types
                required: &ref_295
                  - types
                type: object
                properties: &ref_296
                  types:
                    type: array
                    example:
                      - Beast
                      - Insect
                      - Divine
                      - ...
                    description: ''
  /yugi_cards/frame_types:
    get:
      tags:
        - Yugi Card
      summary: Get all frame types
      description: Get all frame types
      operationId: GetYugiFrameTypes
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Get all card frame types
                required: &ref_297
                  - frame_types
                type: object
                properties: &ref_298
                  frame_types:
                    type: array
                    example:
                      - Orange
                      - Yellow
                      - Green
                      - ...
                    description: ''
  /yugi_cards/archetypes:
    get:
      tags:
        - Yugi Card
      summary: Get all archetypes
      description: Get all archetypes
      operationId: GetYugiArchetypes
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Get all card archetypes
                required: &ref_299
                  - archetypes
                type: object
                properties: &ref_300
                  archetypes:
                    type: array
                    example:
                      - Altergeist
                      - Batteryman
                      - Elementsaber
                      - ...
                    description: ''
  /yugi_cards/races:
    get:
      tags:
        - Yugi Card
      summary: Get all races
      description: Get all races
      operationId: GetYugiRaces
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Get all card races
                required: &ref_301
                  - races
                type: object
                properties: &ref_302
                  races:
                    type: array
                    example:
                      - Beast-Warrior
                      - Creator God
                      - Cyberse
                      - ...
                    description: ''
  /yugi_cards/version:
    get:
      tags:
        - Yugi Card
      summary: Get current version
      description: Get current version
      operationId: GetYugiVersion
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Get version
                required: &ref_303
                  - version
                type: object
                properties: &ref_304
                  version:
                    type: string
                    example: 80a8c7f6-e2b9-4d69-9e30-710e264e575d
                    description: ''
  /yugi_card_instances:
    patch:
      tags:
        - Yugi Card Instances
      summary: Update yugi card instances
      description: Updates yugi card instances' attributes
      operationId: UpdateYugiCardInstances
      parameters:
        - name: uuids
          in: query
          required: true
          schema:
            type: array
            description: array of UUIDs
        - name: condition
          in: query
          required: false
          schema:
            type: string
            enum:
              - Near Mint
              - Lightly Played
              - Moderately Played
              - Heavily Played
              - Damaged
        - name: printing
          in: query
          required: false
          schema:
            type: string
            description: UUID
        - name: card_set
          in: query
          required: false
          schema:
            type: string
            description: UUID
      responses:
        '200':
          description: The yugi card instance was successfully updated
          content:
            application/json:
              schema:
                title: Update Yugi Card Instances response
                required: &ref_79
                  - card_instances
                type: object
                properties: &ref_80
                  card_instances:
                    type: array
                  cards:
                    type: object
                    items:
                      type: object
                  sets:
                    type: object
                    items:
                      type: object
                example: &ref_81
                  card_instances:
                    - id: 1
                      uuid: e4654973-1674-4ee0-a923-5fb844bd1622
                      user_id: 3
                      yugi_card_uuid: 4
                      yugi_card_printing_uuid: c33c55e5-252f-4bb8-9c39-ef2b181588c6
                      yugi_input_session_uuid: 63feaaae-743f-49bb-b787-9d5a6ca30ac4
                      yugi_card_set_id: null
                      scanned_image: null
                      scan_metadata: null
                      condition: Near Mint
                      created_at: 2015-10-30 18:19:26 +1100
                      updated_at: 2015-10-30 18:19:26 +1100
                      deleted_at: nil
                  cards:
                    e4d92769-874c-42b3-9892-3fed1eec83cf:
                      uuid: e4d92769-874c-42b3-9892-3fed1eec83cf
                      ygoprodeck_id: 4444
                      name: Ensui Nara
                      type: Officiis
                      frame_type: Officia
                      description: Quo
                      attack: 4
                      defense: 10
                      level: 8
                      race: null
                      archetype: Eum
                  sets:
                    9dd0a8c2-50aa-466d-aff6-3a746a8bf175:
                      uuid: 9dd0a8c2-50aa-466d-aff6-3a746a8bf175
                      name: Labyrinth of Nightmare
                      set_code: LON
                      release_date: '2003-05-17'
        '400':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
        '404':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
    delete:
      tags:
        - Yugi Card Instances
      summary: Delete yugi card instances
      description: Bulk deletes yugi card instances for the currently authenticated user. On request success all the yugi card instances are deleted from the user. On failure the request is rollbacked
      operationId: DeleteYugiCardInstance
      parameters:
        - name: uuids
          in: query
          required: true
          schema:
            type: array
            items:
              type: string
              example: '[''7cfabcf0-438a-48f5-a4af-5b0f89f768a4'', ''6da368f5-48f8-4a5c-8411-d00e1bb93bab'']'
      responses:
        '200':
          description: ''
        '400':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
        '404':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
  /yugi_card_instances/add:
    post:
      tags:
        - Yugi Card Instances
      summary: Add a yugi card instance
      description: Creates a yugi card instance
      operationId: AddYugiCardInstance
      parameters:
        - name: card_uuid
          in: query
          required: true
          schema:
            type: string
        - name: printing_uuid
          in: query
          required: false
          schema:
            type: string
        - name: card_set_uuid
          in: query
          required: false
          schema:
            type: string
        - name: source
          in: query
          required: false
          schema:
            type: string
            enum:
              - cardbot
              - app
              - manual
              - import
              - unknown
        - name: condition
          in: query
          required: false
          schema:
            type: string
            default: Near Mint
            enum:
              - Near Mint
              - Lightly Played
              - Moderately Played
              - Heavily Played
              - Damaged
      responses:
        '201':
          description: The yugi card instance was successfully added
          content:
            application/json:
              schema:
                title: Add Yugi Card Instance response
                required: &ref_305
                  - yugi_card_instance
                type: object
                properties: &ref_306
                  yugi_card_instance:
                    type: object
                example: &ref_307
                  yugi_card_instance:
                    id: 1
                    uuid: e4654973-1674-4ee0-a923-5fb844bd1622
                    user_id: 3
                    yugi_card_uuid: 4
                    yugi_card_printing_uuid: c33c55e5-252f-4bb8-9c39-ef2b181588c6
                    yugi_input_session_uuid: 63feaaae-743f-49bb-b787-9d5a6ca30ac4
                    yugi_card_set_id: null
                    scanned_image: null
                    scan_metadata: null
                    condition: Near Mint
                    created_at: 2015-10-30 18:19:26 +1100
                    updated_at: 2015-10-30 18:19:26 +1100
                    deleted_at: nil
        '400':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
        '404':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
  /yugi_card_instances/yugi_card_printing:
    patch:
      tags:
        - Yugi Card Instances
      summary: Update yugi card printing
      description: Update yugi card printing for yugi_card_instances
      operationId: PatchYugiCardPrintingYugiCardInstances
      parameters:
        - name: card_printing_uuid
          in: query
          required: true
          schema:
            type: string
        - name: card_instance_uuids
          in: query
          required: true
          schema:
            type: array
            items:
              type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Update Yugi Card Instances response
                required: *ref_79
                type: object
                properties: *ref_80
                example: *ref_81
        '400':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
  /yugi_card_instances/yugi_card:
    patch:
      tags:
        - Yugi Card Instances
      summary: Update yugi_card
      description: Update yugi_card for yugi_card_instances
      operationId: PatchYugiCardInstancesYugiCard
      parameters:
        - name: card_set_uuid
          in: query
          required: true
          schema:
            type: string
          description: To avoid mismatched records, the card to update will be infered from a `yugi_card_set` record
        - name: card_instance_uuids
          in: query
          required: true
          schema:
            type: array
            items:
              type: string
        - name: annotate_scan
          in: query
          required: false
          schema:
            type: boolean
          description: Create or update scan metadata with updated card info
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Patch yugi card instances yugi card response
                required: &ref_308
                  - card_instances
                  - cards
                  - sets
                type: object
                properties: &ref_309
                  card_instances:
                    type: array
                  cards:
                    type: object
                    items:
                      type: object
                  sets:
                    type: object
                    items:
                      type: object
                example: &ref_310
                  card_instances:
                    - id: 1
                      uuid: e4654973-1674-4ee0-a923-5fb844bd1622
                      user_id: 3
                      yugi_card_uuid: e4d92769-874c-42b3-9892-3fed1eec83cf
                      yugi_card_printing_uuid: c33c55e5-252f-4bb8-9c39-ef2b181588c6
                      yugi_input_session_uuid: 63feaaae-743f-49bb-b787-9d5a6ca30ac4
                      yugi_card_set_id: c33c55e5-252f-4bb8-9c39-ef2b181588c6
                      scanned_image: null
                      scan_metadata: null
                      condition: Near Mint
                      created_at: 2015-10-30 18:19:26 +1100
                      updated_at: 2015-10-30 18:19:26 +1100
                      deleted_at: nil
                  cards:
                    e4d92769-874c-42b3-9892-3fed1eec83cf:
                      uuid: e4d92769-874c-42b3-9892-3fed1eec83cf
                      ygoprodeck_id: 4444
                      name: Ensui Nara
                      type: Officiis
                      frame_type: Officia
                      description: Quo
                      attack: 4
                      defense: 10
                      level: 8
                      race: null
                      archetype: Eum
                  sets:
                    9dd0a8c2-50aa-466d-aff6-3a746a8bf175:
                      uuid: 9dd0a8c2-50aa-466d-aff6-3a746a8bf175
                      name: Labyrinth of Nightmare
                      set_code: LON
                      release_date: '2003-05-17'
        '400':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
  /yugi_card_instances/{uuid}/scanned_image:
    post:
      tags:
        - Yugi Card Instances
      summary: Upload a scanned image
      description: Uploads a scanned image and attaches it to an existing yugi card instance
      operationId: PostYugiCardInstanceScannedImage
      parameters:
        - name: uuid
          in: path
          required: true
          schema:
            type: integer
        - name: scanned_image
          in: query
          required: true
          schema:
            type: string
      responses:
        '201':
          description: The image was successfully uploaded.
          content:
            application/json:
              schema:
                title: Scanned images response
                required: &ref_311
                  - scanned_image
                type: object
                properties: &ref_312
                  scanned_image:
                    type: string
                example: &ref_313
                  scanned_image: /uploads/yugi_card_instance/scanned_image/large_9ab2efab-840e-4c9d-8d7e-d428325d69d0.jpg
        '400':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              example: *ref_0
        '404':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
  /collection/yugioh/search:
    post:
      tags:
        - Collection
      summary: Search Yugioh Collection
      description: Search the collection for yugioh cards. Similar to search, but with different parameters and response payload.
      operationId: SearchYugiohCollection
      security:
        - OAuth2:
            - user
        - cookieAuth: []
      parameters:
        - name: sort_by
          in: query
          required: false
          schema: *ref_58
        - name: quantity
          in: query
          required: false
          schema: *ref_55
        - name: input_session
          in: query
          required: false
          schema: *ref_57
        - name: condition
          in: query
          required: false
          schema: *ref_82
        - name: confidence
          in: query
          required: false
          schema: *ref_59
        - name: order
          in: query
          required: false
          schema: *ref_66
        - name: price
          in: query
          required: false
          schema: *ref_73
          description: when no source is passed, price source defaults to user preference.
        - name: name
          in: query
          required: false
          schema: *ref_68
        - name: query
          in: query
          required: false
          schema: *ref_69
        - name: group_by
          in: query
          required: false
          schema: *ref_67
        - name: per_page
          in: query
          required: false
          schema: *ref_70
        - name: page
          in: query
          required: false
          schema: *ref_71
        - name: fields
          in: query
          required: false
          schema:
            type: object
            properties:
              summary:
                type: array
                items: *ref_83
              group:
                type: array
                items: *ref_84
              card:
                type: array
                items: *ref_78
              element:
                type: array
                items: &ref_314
                  type: string
                  enum:
                    - source_type
                    - input_session_uuid
                    - scanned_image_url
                    - scan_metadata
                    - price
                    - condition
                    - created_at
                    - committed_at
          description: Request specific fields to be included in the response. All fields are returned by default, but it's recommended that you use this parameter to specify only the fields you need to reduce the size of the response.
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Yugioh Search Collection response
                type: object
                example: &ref_315
                  page_count: 8
                  page_value: 2300
                  total_groups: 4
                  total_count: 8
                  total_value: 1234
                  collection_items:
                    - group_count: 1
                      group_value: 50
                      latest_committed: '2024-08-05T06:44:15.86546'
                      latest_created: '2024-08-05T06:44:15.867998'
                      group_elements:
                        - uuid: 64da69b8-c9cf-4cca-bd15-36270242db41
                          card_uuid: 51811f2a-7002-4ba7-98d8-5b09d887975c
                          input_session_uuid: 51811f2a-7002-4ba7-98d8-5b09d887975c
                          source_type: app
                          committed_at: '2017-01-24T03:21:20.260Z'
                          created_at: '2017-01-24T03:21:20.260Z'
                          price: 10
                          scanned_image_url: www.image.com.au/123456
                          scan_metadata:
                            confidence: 0.95
                            crop:
                              tl:
                                - 0.1
                                - 0.2
                              tr:
                                - 0.1
                                - 0.2
                              br:
                                - 0.1
                                - 0.2
                              bl:
                                - 0.1
                                - 0.2
                            flipped: true
                            mcr_version: 7.9.1
                            evermind_version: 0.6.2
                  cards:
                    51811f2a-7002-4ba7-98d8-5b09d887975c:
                      - archetype: Reiciendis"
                        attack: 8
                        avg_price: 4626
                        card_sets:
                          - d4d63f8c-f187-4c2c-9720-77571afe8487:
                              - code: MNY-ckmqz5
                                uuid: d4d63f8c-f187-4c2c-9720-77571afe8487
                                set_uuid: 62fc5601-03ca-42b5-93d0-e83af8ebd128
                                rarity_uuid: fa30a88d-d0c3-4e5e-8653-8a3cf92e0b22
                        card_type: Quo
                        defense: 14
                        description: Molestias"
                        frame_type: Omnis"
                        level: 6
                        max_price: 4626
                        median_price: 4626
                        min_price: 4626
                        name: Chichiatsu
                        printings:
                          - 8adf162e-0cd7-4d78-a0e8-246fc504c919:
                              - uuid: 8adf162e-0cd7-4d78-a0e8-246fc504c919
                                printing_idx: 10
                                ygoprodeck_id: 6000010
                          - b4a8807e-37cc-402c-a01d-94aca280bd29:
                              - uuid: b4a8807e-37cc-402c-a01d-94aca280bd29
                                printing_idx: 9
                                ygoprodeck_id: 8000009
                        race: nil,
                        ygoprodeck_id: 51537000005
                  sets:
                    - 7ccc9cc2-6ee4-48cf-bdb7-d57dd3a84f6b:
                        - name: assumenda consequatur eveniet quae sed
                          release_date: '2024-03-10'
                          set_code: VID
                          uuid: 7ccc9cc2-6ee4-48cf-bdb7-d57dd3a84f6b
  /yugi_sets:
    get:
      tags:
        - Yugi Sets
      summary: Get all yugi sets
      description: Get all yugi sets
      operationId: GetAllYugiSets
      parameters:
        - name: query
          in: query
          required: false
          schema:
            type: string
            items:
              type: string
            example: Ancient Prophecy
          description: string to fuzzy match against the yugi set name or set code
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Get all yugi sets
                required: &ref_316
                  - yugi_sets
                type: object
                properties: &ref_317
                  sets:
                    type: array
                    example:
                      - uuid: b6c75bf3-d1c2-4967-97c7-d63e9e1fdcb1
                        name: Ancient Prophecy
                        release_date: '2004-10-27'
                        set_code: APY
                        search_name: Ancient Prophecy
                    description: ''
  /yugi_card_sets:
    get:
      tags:
        - Yugi Card Sets
      summary: Get all yugi card sets
      description: Get all yugi card sets
      operationId: GetAllYugiCardSets
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Get all yugi card sets
                required: &ref_318
                  - yugi_card_sets
                type: object
                properties: &ref_319
                  yugi_card_sets:
                    type: array
                    example:
                      - rarity: optio
                        yugi_card_uuid: a966b111-4951-45c6-bf08-3ed511a80237
                        yugi_set_uuid: 2bfea5c0-ddac-42be-b27c-941a674cde11
                    description: ''
  /yugi_card_sets/search:
    post:
      tags:
        - Yugi Card Sets
      description: Search all `yugi cards` and/or `yugi sets` through associated `yugi card sets`. This API is both public and private, if the user is logged in an owned count is returned, if they are not logged in, the owned count fields are not present.
      summary: Search all yugi cards
      operationId: SearchYugiCardSets
      parameters:
        - name: name
          in: query
          required: false
          schema: *ref_68
        - name: set_names
          in: query
          required: false
          schema: *ref_72
          description: Payload can include `with`, `without`, or both
        - name: query
          in: query
          required: false
          schema: *ref_69
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Search Yugi Card Sets response
                required: &ref_320
                  - cards
                  - sets
                  - card_sets
                type: object
                example: &ref_321
                  card_sets:
                    - uuid: 350c88fe-4b64-48d1-a896-b47776e7ec5e
                      card_uuid: fd7bb0a5-68a8-4403-a5fe-e5dd331aeac3
                      code: JVN-hrb48
                      price: 3645
                      set_uuid: 2721408f-b418-4c62-8397-ff2fe623eafe
                  cards:
                    fd7bb0a5-68a8-4403-a5fe-e5dd331aeac3:
                      uuid: fd7bb0a5-68a8-4403-a5fe-e5dd331aeac3
                      archetype: Nihil
                      attack: 7
                      card_type: Non
                      defense: 27
                      description: Nisi
                      frame_type: Odit
                      level: 13
                      name: En no Gyōja
                      race: Et
                      ygoprodeck_id: 13679000048,
                  sets:
                    2721408f-b418-4c62-8397-ff2fe623eafe:
                      uuid: 2721408f-b418-4c62-8397-ff2fe623eafe
                      name: vero molestiae repudiandae voluptatem eaque
                      release_date: '2018-10-28'
                      set_code: JVN
  /yugi_card_identifiers:
    get:
      tags:
        - Yugi Card Identifiers
      summary: Get all yugi card identifiers
      description: Get all yugi card identifiers
      operationId: GetAllYugiCardIdentifiers
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Get all yugi card identifiers
                required: &ref_322
                  - yugi_card_identifiers
                type: object
                properties: &ref_323
                  card_identifiers:
                    type: array
                    example:
                      - service: tcgplayer
                        external_id: 63827
                        match_info: {}
                        name: null
                        set_name: null
                        collector_number: null
                        yugi_card_printing_uuid: f1232d79-2a0b-45c3-95c9-d9317aac6ede
                        yugi_set_uuid: 618da86c-56ca-434c-b4c4-be43bf355212
                        yugi_card_uuid: 27e083e7-b250-42f3-a61b-************
                    description: ''
  /yugi_skus:
    get:
      tags:
        - Yugi Skus
      summary: Get all yugi skus
      description: Get all yugi skus
      operationId: GetAllYugiSkus
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Get all yugi skus
                required: &ref_324
                  - yugi_skus
                type: object
                properties: &ref_325
                  skus:
                    type: array
                    example:
                      - card_uuid: 88065402-6b90-4dc2-8fec-3a3e6cc124c7
                        code: 2836104638
                        service: tcgplayer
                        condition: Near Mint
                        finish: normal
                        language: en
                    description: ''
  /yugi_rarities:
    get:
      tags:
        - Yugi Rarities
      summary: Get all yugioh rarities
      description: Get all yugioh rarities
      operationId: GetAllYugiRarities
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Get all yugioh rarities
                required: &ref_326
                  - yugi_rarities
                type: object
                properties: &ref_327
                  rarities:
                    type: array
                    example:
                      - uuid: 88065402-6b90-4dc2-8fec-3a3e6cc124c7
                        name: Normal
                        rarity_order: 1
                      - uuid: 4825dfdb-21bd-44a9-8f04-53abe4be659b
                        name: Rare
                        rarity_order: 2
                      - uuid: c0f30e1b-01b7-43ec-8f7b-bf1609cc6d15
                        name: Super Rare
                        rarity_order: 3
                      - ...
                    description: ''
  /lorcana_cards:
    get:
      tags:
        - Lorcana Card
      summary: Get All Lorcana Cards
      description: Get all lorcana cards
      operationId: GetAllLorcanaCard
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Get all lorcana cards
                required: &ref_328
                  - cards
                type: object
                properties: &ref_329
                  cards:
                    type: array
                    example:
                      - uuid: a23abe78-94a6-4c09-a730-c9f175f79dff
                        lorcast_id: crd_ly27u9if1y9o5bfrew95zvh751u59086
                        name: Ruffnut
                        collector_number: 9de
                        rules_text:
                          - 'optio minus: molestias dolores voluptatum dolores ut'
                        flavor_text: Ut eum exercitationem quis
                        ink: Amber
                        inkwell: true
                        version: Non exercitationem doloremque omnis.
                        cost: 2
                        move_cost: nil
                        strength: 7
                        willpower: nil
                        lore: nil
                        layout: landscape
                        types:
                          - enim
                          - sit
                        classifications:
                          - rerum
                          - quia
                          - laboriosam
                        artist:
                          - Rusty Gulgowski
                        set_uuid: 900ae9f1-72bc-4e43-8716-00d949aa6ae3
                        set_lorcast_id: set_f7m3n79bynyq6q3gaaxse9u9lw9nfaio
                        set_name: rerum corrupti
                        set_release_date: '2023-05-07'
                        set_code: BSB
                        rarity_uuid: 235e09f1-c2c8-4dca-8013-322b5a33466f
                        rarity: enchanted
                    description: ''
  /lorcana_cards/search:
    get:
      tags:
        - Lorcana Card
      description: Search all `lorcana cards`. This API is both public and private, if the user is logged in an owned count is returned, if they are not logged in, the owned count fields are not present.
      summary: Search all cards
      operationId: SearchLorcanaCards
      parameters:
        - name: sort_by
          in: query
          required: false
          schema: &ref_545
            type: string
            default: release_date
            enum:
              - name
              - set_name
              - release_date
              - collector_number
              - rarity
              - scan_confidence
            description: 'Note: Cannot `sort_by: ''collector_number''` when `group_by: ''name''`'
        - name: group_by
          in: query
          required: false
          schema: &ref_85
            type: string
            default: printing
            enum:
              - printing
              - name
              - none
        - name: order
          in: query
          required: false
          schema: &ref_86
            type: string
            default: asc
            enum:
              - asc
              - desc
        - name: name
          in: query
          required: false
          schema: &ref_87
            type: string
        - name: query
          in: query
          required: false
          schema: &ref_88
            type: string
        - name: rules_text
          in: query
          required: false
          schema: &ref_89
            type: string
        - name: flavor_text
          in: query
          required: false
          schema: &ref_90
            type: string
        - name: layout
          in: query
          required: false
          schema: &ref_91
            type: string
        - name: version
          in: query
          required: false
          schema: &ref_92
            type: string
        - name: ink
          in: query
          required: false
          schema: &ref_93
            type: string
        - name: inkwell
          in: query
          required: false
          schema: &ref_94
            type: string
        - name: collector_number
          in: query
          required: false
          schema: &ref_95
            type: string
        - name: per_page
          in: query
          required: false
          schema: &ref_96
            type: integer
            default: 100
            enum:
              - 100
              - 300
              - 600
        - name: page
          in: query
          required: false
          schema: &ref_97
            type: integer
            default: 1
        - name: types
          in: query
          required: false
          schema: &ref_98
            type: object
            properties:
              with:
                type: array
                items:
                  type: string
              without:
                type: array
                items:
                  type: string
              multi:
                type: boolean
          description: Accepted types can be retrieved from the `/types` route.
        - name: classifications
          in: query
          required: false
          schema: &ref_99
            type: object
            properties:
              with:
                type: array
                items:
                  type: string
              without:
                type: array
                items:
                  type: string
              multi:
                type: boolean
        - name: artist
          in: query
          required: false
          schema: &ref_100
            type: object
            properties:
              with:
                type: array
                items:
                  type: string
              without:
                type: array
                items:
                  type: string
              multi:
                type: boolean
        - name: set_names
          in: query
          required: false
          schema: &ref_101
            type: object
            properties:
              with:
                type: array
                items:
                  type: string
              without:
                type: array
                items:
                  type: string
        - name: starts_with
          in: query
          required: false
          schema: &ref_102
            type: string
          description: matches the first letter (case insensitive) of a lorcana card's name
        - name: set_starts_with
          in: query
          required: false
          schema: &ref_103
            type: string
          description: matches the first letter (case insensitive) of a lorcana card's set name
        - name: strength
          in: query
          required: false
          schema: &ref_104
            type: object
            properties:
              min:
                type: integer
              max:
                type: integer
        - name: lore
          in: query
          required: false
          schema: &ref_105
            type: object
            properties:
              min:
                type: integer
              max:
                type: integer
        - name: willpower
          in: query
          required: false
          schema: &ref_106
            type: object
            properties:
              min:
                type: integer
              max:
                type: integer
        - name: move_cost
          in: query
          required: false
          schema: &ref_107
            type: object
            properties:
              min:
                type: integer
              max:
                type: integer
        - name: cost
          in: query
          required: false
          schema: &ref_108
            type: object
            properties:
              min:
                type: integer
              max:
                type: integer
        - name: price
          in: query
          required: false
          schema: &ref_109
            type: object
            properties:
              source:
                type: string
                enum:
                  - tcg_player
                  - card_kingdom
                  - card_market
              min:
                type: number
              max:
                type: number
          description: when no source is passed, price source defaults to user preference.
        - name: rarity_filter
          in: query
          required: false
          schema: &ref_110
            type: array
            items:
              type: string
              enum:
                - Common
                - Uncommon
                - Rare
                - Super_rare
                - Legendary
                - Enchanted
                - Promo
        - name: fields
          in: query
          required: false
          schema:
            type: array
            items: &ref_111
              type: string
              enum:
                - name
                - lorcast_id
                - types
                - collector_number
                - layout
                - cost
                - version
                - ink
                - inkwell
                - move_cost
                - strength
                - willpower
                - lore
                - artist
                - flavor_text
                - rules_text
                - classifications
                - set_uuid
                - set_lorcast_id
                - set_name
                - release_date
                - set_code
                - rarity_uuid
                - rarity
          description: Request specific fields to be included in the response. All fields are returned by default, but it's recommended that you use this parameter to specify only the fields you need to reduce the size of the response.
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Search Lorcana Cards response
                required: &ref_330
                  - cards
                type: object
                properties: &ref_331
                  cards:
                    type: array
                    items:
                      type: object
                      example:
                        - uuid: 448d4024-bd63-43ac-ba7d-1b1bf04f544f
                          lorcast_id: crd_lvs5d6m9lhojk0rfrunyz51qptf5hp4z
                          name: Jens Henderson
                          collector_number: 561
                          rules_text:
                            - 'sint quis: consequuntur et sunt possimus officia'
                          flavor_text: Ducimus voluptatem qui cumque.
                          ink: nil
                          inkwell: false
                          version: Quod atque cupiditate voluptas.
                          cost: 2
                          move_cost: nil
                          strength: nil
                          willpower: 9
                          lore: nil
                          layout: landscape
                          types:
                            - reprehenderit
                          classifications:
                            - voluptatem
                          artist:
                            - Mrs. Edyth Hintz
                          set_uuid: 7a687e6f-516b-45df-9f5e-5534a442d604
                          set_lorcast_id: set_rdezui4szzsdr65gev5zxb7twxdp02kp
                          set_name: Into the Inklands
                          release_date: '2021-07-27'
                          set_code: NPF
                          rarity_uuid: 7766d709-1c64-4521-bcf6-7a0756a3d2c0
                          rarity: Super_rare
                          price_tcgplayer: 24
                        - uuid: 6c991884-12e8-4ece-8f47-5d6dc5c42351
                          lorcast_id: crd_4ql7p9t44cdf5xmvdmrbuw5afdu2plyo
                          name: Jens Henderson
                          collector_number: 9000000
                          rules_text:
                            - 'voluptatem quidem: rerum consequatur aut modi magnam'
                          flavor_text: Perspiciatis commodi placeat numquam.
                          ink: nil
                          inkwell: false
                          version: Et qui ratione et.
                          cost: 10
                          move_cost: 10
                          strength: nil
                          willpower: 8
                          lore: nil
                          layout: normal
                          types:
                            - et
                            - eum"
                          classifications:
                            - corrupti
                            - nostrum
                            - est
                          artist:
                            - Julianne Steuber DDS
                          set_uuid: 8deeb414-8899-4fed-86d6-02828475cbd9
                          set_lorcast_id: set_ngfzkdponmjl8y8lryql3jursyv51x1y
                          set_name: Into the Inklands
                          release_date: '2016-10-02'
                          set_code: LQP
                          rarity_uuid: e44fcbee-5c0a-4982-83ff-1ade1b4babfa
                          rarity: Enchanted
                          price_tcgplayer: 24
                    description: ''
  /lorcana_cards/printings:
    post:
      tags:
        - Lorcana Card
      summary: Get printing details for lorcana cards by UUIDs
      description: |-
        + Duplicate UUIDs are ignored 
        + Response is sorted by release date 
        + Response is empty when there are no shared printings 
        + Invalid ids respond with 'not found'
      operationId: GetLorcanaCardsPrintings
      requestBody:
        description: ''
        content:
          application/json:
            schema:
              title: Get printings for lorcana cards by UUIDs
              required: &ref_332
                - uuids
              type: object
              properties: &ref_333
                uuids:
                  type: object
                  example:
                    - 83c6088bc36d3ccd3b64e03a4383ac20e537349d
                    - ffc5a91331acb724f6edb432ac355f0cbccc69c6
                  description: ''
                query:
                  type: string
                  example:
                    - Into the Inklands
                  description: Filters response to only those from a specific set
                per_page:
                  type: integer
                  default: 600
                  enum:
                    - 100
                    - 300
                    - 600
                  description: ''
                page:
                  type: integer
                  description: ''
        required: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Get all lorcana cards printings response
                required: &ref_334
                  - printings
                type: object
                properties: &ref_335
                  printings:
                    type: object
                    example:
                      - uuid: 448d4024-bd63-43ac-ba7d-1b1bf04f544f
                        lorcast_id: crd_lvs5d6m9lhojk0rfrunyz51qptf5hp4z
                        name: Jens Henderson
                        collector_number: 561
                        rules_text:
                          - 'sint quis: consequuntur et sunt possimus officia'
                        flavor_text: Ducimus voluptatem qui cumque.
                        ink: nil
                        inkwell: false
                        version: Quod atque cupiditate voluptas.
                        cost: 2
                        move_cost: nil
                        strength: nil
                        willpower: 9
                        lore: nil
                        layout: landscape
                        types:
                          - reprehenderit
                        classifications:
                          - voluptatem
                        artist:
                          - Mrs. Edyth Hintz
                        set_uuid: 7a687e6f-516b-45df-9f5e-5534a442d604
                        set_lorcast_id: set_rdezui4szzsdr65gev5zxb7twxdp02kp
                        set_name: Into the Inklands
                        release_date: '2021-07-27'
                        set_code: NPF
                        rarity_uuid: 7766d709-1c64-4521-bcf6-7a0756a3d2c0
                        rarity: Super_rare
                        price_tcgplayer: 24
                      - uuid: 6c991884-12e8-4ece-8f47-5d6dc5c42351
                        lorcast_id: crd_4ql7p9t44cdf5xmvdmrbuw5afdu2plyo
                        name: Jens Henderson
                        collector_number: 9000000
                        rules_text:
                          - 'voluptatem quidem: rerum consequatur aut modi magnam'
                        flavor_text: Perspiciatis commodi placeat numquam.
                        ink: nil
                        inkwell: false
                        version: Et qui ratione et.
                        cost: 10
                        move_cost: 10
                        strength: nil
                        willpower: 8
                        lore: nil
                        layout: normal
                        types:
                          - et
                          - eum"
                        classifications:
                          - corrupti
                          - nostrum
                          - est
                        artist:
                          - Julianne Steuber DDS
                        set_uuid: 8deeb414-8899-4fed-86d6-02828475cbd9
                        set_lorcast_id: set_ngfzkdponmjl8y8lryql3jursyv51x1y
                        set_name: Into the Inklands
                        release_date: '2016-10-02'
                        set_code: LQP
                        rarity_uuid: e44fcbee-5c0a-4982-83ff-1ade1b4babfa
                        rarity: Enchanted
                        price_tcgplayer: 24
        '400':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
  /lorcana_cards/version:
    get:
      tags:
        - Lorcana Card
      summary: Get current version
      description: Get current version
      operationId: GetLorcanaVersion
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Get version
                required: &ref_336
                  - version
                type: object
                properties: &ref_337
                  version:
                    type: string
                    example: 80a8c7f6-e2b9-4d69-9e30-710e264e575d
                    description: ''
  /lorcana_cards/update_version:
    get:
      tags:
        - Lorcana Card
      summary: Update current version
      description: Update current version
      operationId: UpdateLorcanaVersion
      parameters:
        - name: new_version
          in: query
          required: true
          schema:
            type: string
            example: 7cfabcf0-438a-48f5-a4af-5b0f89f768a4
      responses:
        '200':
          description: ''
  /lorcana_card_instances/add:
    post:
      tags:
        - Lorcana Card Instances
      summary: Add a lorcana card instance
      description: Add a lorcana card instance
      operationId: AddLorcanaCardInstance
      parameters:
        - name: uuid
          in: query
          required: true
          schema:
            type: string
        - name: source
          in: query
          required: false
          schema:
            type: string
            enum:
              - cardbot
              - app
              - manual
              - import
              - unknown
        - name: condition
          in: query
          required: false
          schema:
            type: string
            default: Near Mint
            enum:
              - Near Mint
              - Lightly Played
              - Moderately Played
              - Heavily Played
              - Damaged
        - name: finish_uuid
          in: query
          required: false
          schema:
            type: string
            example: 7cfabcf0-438a-48f5-a4af-5b0f89f768a4
      responses:
        '201':
          description: The lorcana card instance was successfully added
          content:
            application/json:
              schema:
                title: Add Lorcana Card Instance response
                required: &ref_338
                  - lorcana_card_instances
                type: object
                properties: &ref_339
                  lorcana_card_instances:
                    type: object
                example: &ref_340
                  lorcana_card_instances:
                    uuid: 515cc42e-57d9-402c-a1d8-520a4a6d49d7
                    created_at: '2024-09-16T05:29:41.821Z'
                    condition: Near Mint
                    scanned_image_url: nil
                    finish: Normal
                    lorcana_card_uuid: 594f8ed8-8e85-4412-b5cd-1be51bedd03b
                    price: 0
                    input_session_uuid: 6588afc2-de77-4343-a78e-a2197b01536f
                    committed_at: nil
                    source_type: unknown
                    scan_metadata: nil
        '400':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
        '404':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
  /lorcana_card_instances:
    patch:
      tags:
        - Lorcana Card Instances
      summary: Update a lorcana card instance
      description: Updates a lorcana card instance's attributes
      operationId: UpdateLorcanaCardInstance
      parameters:
        - name: uuids
          in: query
          required: true
          schema:
            type: array
            description: array of UUIDs
        - name: condition
          in: query
          required: false
          schema:
            type: string
            enum:
              - Near Mint
              - Lightly Played
              - Moderately Played
              - Heavily Played
              - Damaged
        - name: annotate_scan
          in: query
          required: false
          schema:
            type: boolean
          description: Create or update scan metadata with updated card info
        - name: finish_uuid
          in: query
          required: false
          schema:
            type: string
            example: 7cfabcf0-438a-48f5-a4af-5b0f89f768a4
        - name: printing
          in: query
          required: false
          schema:
            type: string
            example: 7cfabcf0-438a-48f5-a4af-5b0f89f768a4
          description: Update printing of all Lorcana card Instances. The new printing must be valid for all or the request will fail
      responses:
        '200':
          description: The lorcana card instance was successfully updated
          content:
            application/json:
              schema:
                title: Update Lorcana Card Instance response
                required: &ref_341
                  - card_instances
                  - cards
                type: object
                properties: &ref_342
                  card_instances:
                    type: array
                  cards:
                    type: object
                    items:
                      type: object
                example: &ref_343
                  card_instances:
                    - uuid: f0bbccf5-2cb8-44f3-81a7-6d01f7c26b53
                      created_at: '2024-09-16T05:33:45.226Z'
                      condition: Damaged
                      scanned_image_url: nil
                      finish:
                        - \"Normal\"
                        - \"Foil\"
                        - \"Holofoil\"
                      lorcana_card_uuid: fd71195e-a1e2-4a08-b776-e169b2627652
                      price: 0
                      input_session_uuid: 8664f717-aff1-4288-86e6-882481295eb3
                      committed_at: '2024-09-16T05:33:45.225Z'
                      source_type: trade
                      scan_metadata: nil
                  cards:
                    e4654973-1674-4ee0-a923-5fb844bd1622:
                      lorcast_id: crd_ztnc122mxw4p5beqqi0wy5ng2zugwtfw
                      name: Snow White
                      collector_number: 902
                      rules_text:
                        - 'ut excepturi: inventore debitis et culpa aut'
                      flavor_text: A voluptatem et est
                      ink: nil
                      inkwell: false
                      version: Impedit fugiat repudiandae consequatur
                      cost: 7
                      move_cost: nil
                      strength: nil
                      willpower: nil
                      lore: 9
                      layout: landscape
                      types:
                        - facere
                      classifications:
                        - cupiditate
                        - voluptas
                      artist:
                        - Elbert Connelly
                      set_uuid: abc99958-0afd-44d5-82b9-03864a5d99da
                      set_lorcast_id: set_b2b7hxa30m3owl7qd45zksf2h5nsayya
                      set_name: numquam labore
                      set_release_date: '2002-04-01'
                      set_code: BFE
                      rarity_uuid: d5635139-2d60-4e64-bd1a-012db53cb4ce
                      rarity: rare
        '400':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
        '404':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
    delete:
      tags:
        - Lorcana Card Instances
      summary: Delete Lorcana Card Instances
      description: Bulk deletes lorcana card instances for the currently authenticated user. On request success all the lorcana cards are deleted from the user. On failure the request is rolled back
      operationId: DeleteLorcanaCardInstance
      parameters:
        - name: uuids
          in: query
          required: true
          schema:
            type: array
            items:
              type: string
              example: '[''7cfabcf0-438a-48f5-a4af-5b0f89f768a4'', ''6da368f5-48f8-4a5c-8411-d00e1bb93bab'']'
      responses:
        '200':
          description: ''
        '400':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
        '404':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
  /lorcana_card_instances/lorcana_card:
    patch:
      tags:
        - Lorcana Card Instances
      summary: Update lorcana_card
      description: Update lorcana_card for lorcana_card_instances
      operationId: PatchLorcanaCardUsersLorcanaCard
      parameters:
        - name: uuid
          in: query
          required: true
          schema:
            type: string
        - name: lorcana_card_user_uuids
          in: query
          required: true
          schema:
            type: array
            items:
              type: string
        - name: annotate_scan
          in: query
          required: false
          schema:
            type: boolean
          description: Create or update scan metadata with updated card info
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Patch Lorcana Card Instance Lorcana Card Response
                required: &ref_344
                  - card_instances
                  - cards
                type: object
                properties: &ref_345
                  card_instances:
                    type: array
                    items:
                      type: object
                    example:
                      card_instances:
                        - uuid: f0bbccf5-2cb8-44f3-81a7-6d01f7c26b53
                          created_at: '2024-09-16T05:33:45.226Z'
                          condition: Damaged
                          scanned_image_url: nil
                          finish:
                            - \"Normal\"
                            - \"Foil\"
                            - \"Holofoil\"
                          lorcana_card_uuid: fd71195e-a1e2-4a08-b776-e169b2627652
                          price: 0
                          input_session_uuid: 8664f717-aff1-4288-86e6-882481295eb3
                          committed_at: '2024-09-16T05:33:45.225Z'
                          source_type: trade
                          scan_metadata: nil
                      cards:
                        e4654973-1674-4ee0-a923-5fb844bd1622:
                          lorcast_id: crd_ztnc122mxw4p5beqqi0wy5ng2zugwtfw
                          name: Snow White
                          collector_number: 902
                          rules_text:
                            - 'ut excepturi: inventore debitis et culpa aut'
                          flavor_text: A voluptatem et est
                          ink: nil
                          inkwell: false
                          version: Impedit fugiat repudiandae consequatur
                          cost: 7
                          move_cost: nil
                          strength: nil
                          willpower: nil
                          lore: 9
                          layout: landscape
                          types:
                            - facere
                          classifications:
                            - cupiditate
                            - voluptas
                          artist:
                            - Elbert Connelly
                          set_uuid: abc99958-0afd-44d5-82b9-03864a5d99da
                          set_lorcast_id: set_b2b7hxa30m3owl7qd45zksf2h5nsayya
                          set_name: numquam labore
                          set_release_date: '2002-04-01'
                          set_code: BFE
                          rarity_uuid: d5635139-2d60-4e64-bd1a-012db53cb4ce
                          rarity: rare
        '400':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
  /lorcana_card_instances/{uuid}/scanned_image:
    post:
      tags:
        - Lorcana Card Instances
      summary: Upload a scanned image
      description: Uploads a scanned image and attaches it to an existing lorcana card instance
      operationId: PostLorcanaCardInstancesScannedImage
      parameters:
        - name: uuid
          in: path
          required: true
          schema:
            type: integer
        - name: scanned_image
          in: query
          required: true
          schema:
            type: string
      responses:
        '201':
          description: The image was successfully uploaded.
          content:
            application/json:
              schema:
                title: Scanned images response
                required: &ref_346
                  - scanned_image
                type: object
                properties: &ref_347
                  scanned_image:
                    type: string
                example: &ref_348
                  scanned_image: /uploads/lorcana_card_instance/scanned_image/large_9ab2efab-840e-4c9d-8d7e-d428325d69d0instance
        '400':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              example: *ref_0
        '404':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
  /collection/lorcana/search:
    post:
      tags:
        - Collection
      summary: Search Lorcana Collection
      description: Search the collection for Lorcana cards. Similar to search, but with different parameters and response payload.
      operationId: SearchLorcanaCollection
      security:
        - OAuth2:
            - user
        - cookieAuth: []
      parameters:
        - name: quantity
          in: query
          required: false
          schema: *ref_55
        - name: source
          in: query
          required: false
          schema: *ref_56
        - name: input_session
          in: query
          required: false
          schema: *ref_57
        - name: condition
          in: query
          required: false
          schema: *ref_82
        - name: sort_by
          in: query
          required: false
          schema: *ref_58
        - name: confidence
          in: query
          required: false
          schema: *ref_59
        - name: group_by
          in: query
          required: false
          schema: *ref_85
        - name: order
          in: query
          required: false
          schema: *ref_86
        - name: name
          in: query
          required: false
          schema: *ref_87
        - name: query
          in: query
          required: false
          schema: *ref_88
        - name: rules_text
          in: query
          required: false
          schema: *ref_89
        - name: flavor_text
          in: query
          required: false
          schema: *ref_90
        - name: layout
          in: query
          required: false
          schema: *ref_91
        - name: version
          in: query
          required: false
          schema: *ref_92
        - name: ink
          in: query
          required: false
          schema: *ref_93
        - name: inkwell
          in: query
          required: false
          schema: *ref_94
        - name: collector_number
          in: query
          required: false
          schema: *ref_95
        - name: per_page
          in: query
          required: false
          schema: *ref_96
        - name: page
          in: query
          required: false
          schema: *ref_97
        - name: types
          in: query
          required: false
          schema: *ref_98
          description: Accepted types can be retrieved from the `/types` route.
        - name: classifications
          in: query
          required: false
          schema: *ref_99
        - name: artist
          in: query
          required: false
          schema: *ref_100
        - name: set_names
          in: query
          required: false
          schema: *ref_101
        - name: starts_with
          in: query
          required: false
          schema: *ref_102
          description: matches the first letter (case insensitive) of a lorcana card's name
        - name: set_starts_with
          in: query
          required: false
          schema: *ref_103
          description: matches the first letter (case insensitive) of a lorcana card's set name
        - name: strength
          in: query
          required: false
          schema: *ref_104
        - name: lore
          in: query
          required: false
          schema: *ref_105
        - name: willpower
          in: query
          required: false
          schema: *ref_106
        - name: move_cost
          in: query
          required: false
          schema: *ref_107
        - name: cost
          in: query
          required: false
          schema: *ref_108
        - name: price
          in: query
          required: false
          schema: *ref_109
          description: when no source is passed, price source defaults to user preference.
        - name: rarity_filter
          in: query
          required: false
          schema: *ref_110
        - name: fields
          in: query
          required: false
          schema:
            type: object
            properties:
              summary:
                type: array
                items: *ref_83
              group:
                type: array
                items: *ref_84
              card:
                type: array
                items: *ref_111
              element:
                type: array
                items: &ref_349
                  type: string
                  enum:
                    - source_type
                    - scanned_image_url
                    - price
                    - created_at
                    - committed_at
          description: Request specific fields to be included in the response. All fields are returned by default, but it's recommended that you use this parameter to specify only the fields you need to reduce the size of the response.
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Lorcana Search Collection response
                type: object
                example: &ref_350
                  page_count: 8
                  page_value: 2300
                  total_groups: 4
                  total_count: 8
                  total_value: 1234
                  collection_items:
                    - latest_created: '2017-01-24T03:21:20.260Z'
                      latest_committed: '2017-01-24T03:21:20.260Z'
                      group_count: 1
                      group_price: 79.745994
                      group_elements:
                        - uuid: 64da69b8-c9cf-4cca-bd15-36270242db41
                          lorcana_card_uuid: 6c991884-12e8-4ece-8f47-5d6dc5c42351
                          input_session_uuid: 8adf162e-0cd7-4d78-a0e8-246fc504c919
                          committed_at: '2017-01-24T03:21:20.260Z'
                          created_at: '2017-01-24T03:21:20.260Z'
                          source_type: app
                          price: 10
                          scanned_image_url: www.image.com.au/123456
                          scan_metadata:
                            confidence: 0.95
                            crop:
                              tl:
                                - 0.1
                                - 0.2
                              tr:
                                - 0.1
                                - 0.2
                              br:
                                - 0.1
                                - 0.2
                              bl:
                                - 0.1
                                - 0.2
                            flipped: true
                            mcr_version: 7.9.1
                            evermind_version: 0.6.2
                  cards:
                    6c991884-12e8-4ece-8f47-5d6dc5c42351:
                      - uuid: 6c991884-12e8-4ece-8f47-5d6dc5c42351
                        lorcast_id: crd_4ql7p9t44cdf5xmvdmrbuw5afdu2plyo
                        name: Jens Henderson
                        collector_number: 9000000
                        rules_text:
                          - 'voluptatem quidem: rerum consequatur aut modi magnam'
                        flavor_text: Perspiciatis commodi placeat numquam.
                        ink: nil
                        inkwell: false
                        version: Et qui ratione et.
                        cost: 10
                        move_cost: 10
                        strength: nil
                        willpower: 8
                        lore: nil
                        layout: normal
                        types:
                          - et
                          - eum"
                        classifications:
                          - corrupti
                          - nostrum
                          - est
                        artist:
                          - Julianne Steuber DDS
                        set_uuid: 8deeb414-8899-4fed-86d6-02828475cbd9
                        set_lorcast_id: set_ngfzkdponmjl8y8lryql3jursyv51x1y
                        set_name: Into the Inklands
                        release_date: '2016-10-02'
                        set_code: LQP
                        rarity_uuid: e44fcbee-5c0a-4982-83ff-1ade1b4babfa
                        rarity: Enchanted
                        price_tcgplayer: 24
                  sets:
                    - 71440c1d-3109-4b6a-9d39-f48d40f17912:
                        - name: voluptatem inventore
                          release_date: '2024-03-10'
                          set_code: ZHP
                          uuid: 71440c1d-3109-4b6a-9d39-f48d40f17912
  /lorcana_sets:
    get:
      tags:
        - Lorcana Sets
      summary: Get All Lorcana Sets
      description: Get all Lorcana Sets
      operationId: GetAllLorcanaSets
      parameters:
        - name: query
          in: query
          required: false
          schema:
            type: string
            items:
              type: string
            example: Inklands
          description: string to fuzzy match against the lorcana set name
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Get all Lorcana card sets
                required: &ref_351
                  - lorcana_sets
                type: object
                properties: &ref_352
                  card_sets:
                    type: array
                    example:
                      - id: 1
                        uuid: b6c75bf3-d1c2-4967-97c7-d63e9e1fdcb1
                        name: Inklands
                        lorcast_id: set_7ecb0e0c71af496a9e0110e23824e0a5
                        release_date: '2004-10-27'
                        code: '1'
                        search_name: Inklands
                        created_at: '2023-02-16T22:47:02.794Z'
                        updated_at: '2023-02-16T22:47:02.794Z'
                    description: ''
  /lorcana_card_identifiers:
    get:
      tags:
        - Lorcana Identifiers
      summary: Get all lorcana card identifiers
      description: Get all lorcana card identifiers
      operationId: GetAllLorcanaCardIdentifiers
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Get all lorcana card identifiers
                required: &ref_353
                  - lorcana_card_identifiers
                type: object
                properties: &ref_354
                  card_identifiers:
                    type: array
                    example:
                      - card_uuid: c3b70b5b-b04b-49dd-9ad7-480708bac61f
                        service: tcg_player
                        external_id: 34268
                        name: Ariel
                        collector_number: '1'
                        match_info: null
                        set_name: The First Chapter
                    description: ''
  /lorcana_skus:
    get:
      tags:
        - Lorcana Skus
      summary: Get all lorcana skus
      description: Get all lorcana skus
      operationId: GetAllLorcanaSkus
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Get all lorcana skus
                required: &ref_355
                  - lorcana_skus
                type: object
                properties: &ref_356
                  skus:
                    type: array
                    example:
                      - card_uuid: 88065402-6b90-4dc2-8fec-3a3e6cc124c7
                        code: 2836104638
                        service: tcg_player
                        condition: Near Mint
                        finish: normal
                        language: en
                    description: ''
  /lorcana_finishes:
    get:
      tags:
        - Lorcana Finishes
      summary: Get all lorcana finishes
      description: Get all lorcana finishes
      operationId: GetAllLorcanaFinishes
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Get all lorcana finishes
                required: &ref_357
                  - lorcana_finishes
                type: object
                properties: &ref_358
                  finishes:
                    type: array
                    example:
                      - uuid: 88065402-6b90-4dc2-8fec-3a3e6cc124c7
                        name: Normal
                        fallback_order: 1
                      - uuid: 4825dfdb-21bd-44a9-8f04-53abe4be659b
                        name: Holo Foil
                        fallback_order: 2
                      - uuid: c0f30e1b-01b7-43ec-8f7b-bf1609cc6d15
                        name: Cold Foil
                        fallback_order: 3
                    description: ''
  /lorcana_rarities:
    get:
      tags:
        - Lorcana Rarities
      summary: Get all lorcana rarities
      description: Get all lorcana rarities
      operationId: GetAllLorcanaRarities
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Get all lorcana rarities
                required: &ref_359
                  - lorcana_rarities
                type: object
                properties: &ref_360
                  rarities:
                    type: array
                    example:
                      - uuid: 88065402-6b90-4dc2-8fec-3a3e6cc124c7
                        name: Common
                        rarity_order: 1
                      - uuid: 4825dfdb-21bd-44a9-8f04-53abe4be659b
                        name: Uncommon
                        rarity_order: 2
                      - uuid: c0f30e1b-01b7-43ec-8f7b-bf1609cc6d15
                        name: Rare
                        rarity_order: 3
                      - ...
                    description: ''
  /mcr/hashes:
    get:
      tags:
        - MCR
      summary: Get Hash Version for MCR >= v3
      description: |-
        Get the MCR hash version for a corresponding MCR version.
        + version (string) - Specify the MCR version to get the corresponding hash. This is required.
      operationId: GetHashVersionForMcr
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Get Hash Version for MCR >= v3 response
                required: &ref_361
                  - version
                  - deprecated
                type: object
                properties: &ref_362
                  version:
                    type: string
                    example: 09f64a7e1cab6a0821ceb0aafc0256ef09a4ef77
                  deprecated:
                    type: boolean
                    example: false
                example: &ref_363
                  version: 09f64a7e1cab6a0821ceb0aafc0256ef09a4ef77
                  deprecated: false
        '400':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                title: 404 Error
                required: &ref_112
                  - error
                type: object
                properties: &ref_113
                  error:
                    type: string
                    example: Error Message
                example: &ref_114
                  error: Error Message
        '404':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                title: 404 Error
                required: *ref_112
                type: object
                properties: *ref_113
                example: *ref_114
  /mcr/metrics:
    post:
      tags:
        - MCR
      summary: Upload Metrics for MCR
      description: |-
        Upload metrics about the MCR as a binary blob. Send as a form data object and multipart.
        + metrics (object) - The blog of binary data to upload
      operationId: UploadMetricsForMcr
      security:
        - OAuth2:
            - user
        - cookieAuth: []
      requestBody:
        description: ''
        content:
          application/json:
            schema:
              title: Upload Metrics for MCR request
              required: &ref_364
                - metrics
              type: object
              properties: &ref_365
                metrics:
                  type: string
                  example: <blob>
              example: &ref_366
                metrics: <blob>
        required: true
      responses:
        '204':
          description: ''
        '400':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
  /users:
    get:
      tags:
        - User
      summary: Get Current User
      description: |-
        Get the details of the currently logged in user. Also updates their devise
        tracking fields.
      operationId: GetCurrentUser
      security:
        - OAuth2:
            - user
        - cookieAuth: []
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Default User Details Response
                required: &ref_115
                  - id
                  - username
                  - email
                  - name
                  - created_at
                  - confirmed_at
                  - twitter
                  - facebook
                  - referral_link
                  - share_link
                  - subscription
                  - avatar
                  - preferences
                  - credit
                  - user_hash
                  - ios_user_hash
                  - android_user_hash
                type: object
                properties: &ref_116
                  id:
                    type: integer
                    format: int32
                    example: 1
                  username:
                    type: string
                    example: lollollollol
                  email:
                    type: string
                    example: <EMAIL>
                  name:
                    type: string
                    example: lol
                  created_at:
                    type: string
                    example: 10/30/2015 7:03:00 AM
                  confirmed_at:
                    type: string
                    example: 10/30/2015 7:13:00 AM
                    nullable: true
                  twitter:
                    type: string
                    nullable: true
                  facebook:
                    type: string
                    nullable: true
                  referral_link:
                    type: string
                    example: http://cardcastle.co/users/referrals/9ab2efab-840e-4c9d-8d7e-d428325d69d0
                  share_link:
                    type: string
                    nullable: true
                  subscription:
                    type: boolean
                  avatar:
                    type: object
                    example:
                      large: /avatar_fallback/black_large.jpg
                      medium: /avatar_fallback/black_medium.jpg
                      thumb: /avatar_fallback/black_thumb.jpg
                  preferences:
                    type: object
                  user_hash:
                    type: string
                  ios_user_hash:
                    type: string
                  android_user_hash:
                    type: string
                example: &ref_117
                  id: 1
                  username: lollollollol
                  email: <EMAIL>
                  name: lol
                  created_at: '2015-10-30T07:03:00.265Z'
                  confirmed_at: '2015-10-30T07:03:10.265Z'
                  twitter: null
                  facebook: null
                  referral_link: http://cardcastle.co/users/referrals/9ab2efab-840e-4c9d-8d7e-d428325d69d0
                  share_link: null
                  avatar:
                    large: /uploads/user/avatar/large_9ab2efab-840e-4c9d-8d7e-d428325d69d0.jpg
                    medium: /uploads/user/avatar/medium_9ab2efab-840e-4c9d-8d7e-d428325d69d0.jpg
                    thumb: /uploads/user/avatar/thumb_9ab2efab-840e-4c9d-8d7e-d428325d69d0.jpg
                  subscription: false
                  preferences:
                    localization:
                      currency: USD
                      timezone: US/Pacific
                      card_language: en
                    privacy:
                      public_collection: false
                    collection:
                      view_by: grid
                      group_by: printing
                      sort_by: date
                      order_by: desc
                      condition: Near Mint
                    pricing:
                      source: tcg_player
                  user_hash: 402ebe0805178f9c8d71c9a2bc64a36b5bf89cd38d03406bea504c243a225e06
                  ios_user_hash: 402ebe0805178f9c8d71c9a2bc64a36b5bf89cd38d03406bea504c243a225e06
                  android_user_hash: 402ebe0805178f9c8d71c9a2bc64a36b5bf89cd38d03406bea504c243a225e06
    post:
      tags:
        - User
      summary: Create a User
      description: ''
      operationId: CreateAUser
      security:
        - OAuth2:
            - user
        - cookieAuth: []
      requestBody:
        description: ''
        content:
          application/json:
            schema:
              title: Create a User request
              required: &ref_367
                - username
                - email
                - password
                - password_confirmation
                - uid
              type: object
              properties: &ref_368
                username:
                  type: string
                  maximum: 64
                  example: cardcastle
                email:
                  type: string
                  maximum: 254
                  example: <EMAIL>
                password:
                  type: string
                  example: 12345678
                password_confirmation:
                  type: string
                  example: 12345678
                uid:
                  type: string
                  example: <EMAIL>
              example: &ref_369
                username: cardcastle
                email: <EMAIL>
                password: 12345678
                password_confirmation: 12345678
                uid: <EMAIL>
        required: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Default User Details Response
                required: *ref_115
                type: object
                properties: *ref_116
                example: *ref_117
        '400':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
    delete:
      tags:
        - User
      summary: Delete Current User
      description: Delete the currently logged in user and logs them out. The delete is queued asynchronously.
      operationId: DeleteCurrentUser
      security:
        - OAuth2:
            - user
        - cookieAuth: []
      responses:
        '200':
          description: ''
    patch:
      tags:
        - User
      summary: Update User Details
      description: ''
      operationId: UpdateUserDetails
      security:
        - OAuth2:
            - user
        - cookieAuth: []
      requestBody:
        description: ''
        content:
          application/json:
            schema:
              title: Update User Details request
              required: &ref_370
                - username
                - email
                - name
                - twitter
                - facebook
              type: object
              properties: &ref_371
                username:
                  type: string
                  example: lollollollol
                email:
                  type: string
                  example: <EMAIL>
                name:
                  type: string
                  example: lol
                twitter:
                  type: string
                  nullable: true
                facebook:
                  type: string
                  nullable: true
        required: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Default User Details Response
                required: *ref_115
                type: object
                properties: *ref_116
                example: *ref_117
        '400':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
  /users/search:
    post:
      tags:
        - User
      summary: Search For Users
      description: ''
      operationId: SearchUsers
      security:
        - OAuth2:
            - admin
      requestBody:
        description: ''
        content:
          application/json:
            schema:
              title: User Search Requst
              required: &ref_372
                - query
              type: string
              example: <EMAIL>
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: User Search Response
                type: array
                items: &ref_373
                  type: object
                  example:
                    username: lololololol
                    email: <EMAIL>
                    name: null
                    created_at: '2015-12-16T02:55:34.086Z'
                    twitter: null
                    facebook: null
                    avatar:
                      large: /avatar_fallback/black_large.jpg
                      medium: /avatar_fallback/black_medium.jpg
                      thumb: /avatar_fallback/black_thumb.jpg
                    subscription: false
                    confirmed_at: '2016-06-20T05:55:37.089Z'
                    preferences:
                      localization:
                        currency: USD
                        timezone: Australia/Canberra
  /users/reset:
    post:
      tags:
        - User
      summary: Reset User Password
      description: |-
        Reset the password for a user. This is an unauthenticated API. Assuming a correct request is sent
        as specified, the API will not return anything even if the user does not exists. This is done for security
        purposes.
        + username (string) - the username or email of the user to be reset
      operationId: ResetUserPassword
      requestBody:
        description: ''
        content:
          application/json:
            schema:
              title: Reset User Password request
              required: &ref_374
                - username
              type: object
              properties: &ref_375
                username:
                  type: string
                  example: <EMAIL>
              example: &ref_376
                username: <EMAIL>
        required: true
      responses:
        '200':
          description: ''
          content:
            text/plain:
              schema:
                type: object
        '400':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
  /users/prefs:
    patch:
      tags:
        - User
      summary: Update User Prefs
      description: Update the user preferences
      operationId: UpdateUserPrefs
      security:
        - OAuth2:
            - user
        - cookieAuth: []
      requestBody:
        description: ''
        content:
          application/json:
            schema:
              title: Update User Prefs request
              type: object
              properties: &ref_377
                localization:
                  type: object
                  properties:
                    currency:
                      type: string
                    timezone:
                      type: string
                    language:
                      type: string
                  example:
                    currency: USD
                    timezone: Australia/Canberra
                    card_language: en
                privacy:
                  type: object
                  properties:
                    public_collection:
                      type: boolean
                    hide_prices:
                      type: boolean
                  example:
                    public_collection: true
                    hide_prices: false
                collection:
                  type: object
                  properties:
                    view_by:
                      type: string
                    group_by:
                      type: string
                    sort_by:
                      type: string
                    order_by:
                      type: string
                    condition_by:
                      type: string
                  example:
                    view_by: grid
                    group_by: none
                    sort_by: date
                    order: desc
                    condition: damaged
                pricing:
                  type: object
                  properties:
                    source:
                      type: string
                      enum:
                        - tcg_player
                        - tcg_player_low
                        - tcg_player_mid
                        - tcg_player_high
                        - tcg_player_direct_low
                        - card_kingdom
                        - card_market
              example: &ref_378
                localization:
                  currency: USD
                  timezone: Australia/Canberra
                  card_language: en
                privacy:
                  public_collection: true
                  hide_prices: true
                collection:
                  view_by: grid
                  group_by: none
                  sort_by: date
                  order: desc
                  condition: damaged
                pricing:
                  source: tcg_player
        required: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Default User Details Response
                required: *ref_115
                type: object
                properties: *ref_116
                example: *ref_117
  /users/password:
    patch:
      tags:
        - User
      summary: Update User Password
      description: |-
        Update the password of the currently logged in user.
        + current_password (string) - old password
        + password (string) - new password
        + password_confirmation (string) - confirm new password
      operationId: UpdateUserPassword
      requestBody:
        description: ''
        content:
          text/plain:
            schema:
              type: object
        required: true
      responses:
        '200':
          description: ''
          content:
            text/plain:
              schema:
                type: object
        '400':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
  /users/avatar:
    post:
      tags:
        - User
      summary: Update User Avatar
      description: |-
        Upload an Avatar for the currently logged in user.
        + avatar (string) - file containing the new avatar
        + avatar_options (optional, object)    + scale (number) - how much to scale the image
            + coord_x (number) - top left corner x coordinate
            + coord_y (number) - top left corner y corrdinate
            + height (number) - height to resize to
            + width (number) - width to resize to
      operationId: UpdateUserAvatar
      security:
        - OAuth2:
            - user
        - cookieAuth: []
      requestBody:
        description: ''
        content:
          application/json:
            schema:
              title: Update User Avatar request
              required: &ref_379
                - avatar
                - avatar_options
              type: object
              properties: &ref_380
                avatar:
                  type: string
                  example: file
                avatar_options:
                  type: object
                  example:
                    scale: 1
                    coord_x: 0
                    coord_y: 0
                    height: 200
                    width: 200
              example: &ref_381
                avatar: file
                avatar_options:
                  scale: 1
                  coord_x: 0
                  coord_y: 0
                  height: 200
                  width: 200
        required: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Update User Avatar response
                required: &ref_382
                  - id
                  - username
                  - email
                  - name
                  - created_at
                  - twitter
                  - facebook
                  - referral_link
                  - avatar
                type: object
                properties: &ref_383
                  id:
                    type: integer
                    format: int32
                    example: 1
                  username:
                    type: string
                    example: lollollollol
                  email:
                    type: string
                    example: <EMAIL>
                  name:
                    type: string
                    example: lol
                  created_at:
                    type: string
                    example: 10/30/2015 7:03:00 AM
                  twitter:
                    type: string
                    nullable: true
                  facebook:
                    type: string
                    nullable: true
                  referral_link:
                    type: string
                    example: http://localhost/users/referrals/9ab2efab-840e-4c9d-8d7e-d428325d69d0
                  avatar:
                    type: object
                    example:
                      large: /uploads/user/avatar/large_9ab2efab-840e-4c9d-8d7e-d428325d69d0.jpg
                      medium: /uploads/user/avatar/medium_9ab2efab-840e-4c9d-8d7e-d428325d69d0.jpg
                      thumb: /uploads/user/avatar/thumb_9ab2efab-840e-4c9d-8d7e-d428325d69d0.jpg
                example: &ref_384
                  id: 1
                  username: lollollollol
                  email: <EMAIL>
                  name: lol
                  created_at: '2015-10-30T07:03:00.265Z'
                  twitter: null
                  facebook: null
                  referral_link: http://localhost/users/referrals/9ab2efab-840e-4c9d-8d7e-d428325d69d0
                  avatar:
                    large: /uploads/user/avatar/large_9ab2efab-840e-4c9d-8d7e-d428325d69d0.jpg
                    medium: /uploads/user/avatar/medium_9ab2efab-840e-4c9d-8d7e-d428325d69d0.jpg
                    thumb: /uploads/user/avatar/thumb_9ab2efab-840e-4c9d-8d7e-d428325d69d0.jpg
        '400':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
  /tags:
    get:
      tags:
        - Tag
      summary: Get All Tags for User
      description: Gets a listing of call the tags the current user has.
      operationId: GetAllTagsForUser
      parameters:
        - name: query
          in: query
          required: false
          schema:
            type: string
            description: search parameter to filter tags by name
      security:
        - OAuth2:
            - user
        - cookieAuth: []
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Get All Tags for User response
                required: &ref_385
                  - tags
                type: object
                properties: &ref_386
                  tags:
                    type: array
                    items:
                      type: object
                      example:
                        - id: 1
                          name: tag1
                          created_at: 2015-10-30 18:20:26 +1100
                        - id: 2
                          name: tag2
                          created_at: 2015-11-30 18:20:26 +1100
                    description: ''
                example: &ref_387
                  tags:
                    - id: 1
                      name: tag1
                      created_at: 2015-10-30 18:20:26 +1100
                    - id: 2
                      name: tag2
                      created_at: 2015-11-30 18:20:26 +1100
    post:
      tags:
        - Tag
      summary: Create Tag for Card Users
      description: Create or add an existing tag and add card users to it
      operationId: CreateTagForCardUsers
      parameters:
        - name: name
          in: query
          required: true
          schema:
            type: string
            example: rakdos_deck
            description: White spaces are not allowed. Max length is 64 characters
        - name: card_ids
          in: query
          required: true
          schema:
            type: array
      security:
        - OAuth2:
            - user
        - cookieAuth: []
      requestBody:
        description: ''
        content:
          application/json:
            schema:
              title: Create Tag for Card Users request
              required: &ref_388
                - card_ids
                - name
              type: object
              properties: &ref_389
                card_ids:
                  type: array
                  example:
                    - 1
                    - 2
                    - 3
                    - 4
                name:
                  type: string
                  maximum: 64
                  example: Urza
        required: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Create Tag for Card Users response
                required: &ref_390
                  - tag
                type: object
                properties: &ref_391
                  tag:
                    type: object
                    example:
                      id: 1
                      name: tag1
                      created_at: 2015-10-30 18:20:26 +1100
                example: &ref_392
                  tag:
                    id: 1
                    name: tag1
                    created_at: 2015-10-30 18:20:26 +1100
        '400':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
        '404':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
  /tags/lookup:
    post:
      tags:
        - Tag
      summary: Lookup Tags for Card Users
      description: Gets all the tags for a set for card users.
      operationId: LookupTagsForCardUsers
      parameters:
        - name: card_ids
          in: query
          required: true
          schema:
            type: array
      security:
        - OAuth2:
            - user
        - cookieAuth: []
      requestBody:
        description: ''
        content:
          application/json:
            schema:
              title: Lookup Tags for Card Users request
              required: &ref_393
                - card_ids
              type: object
              properties: &ref_394
                card_ids:
                  type: array
                  items:
                    type: integer
                    format: int32
                    example:
                      - 1
                      - 2
                      - 3
                      - 4
                  description: ''
              example: &ref_395
                card_ids:
                  - 1
                  - 2
                  - 3
                  - 4
        required: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Lookup Tags for Card Users response
                required: &ref_396
                  - tags
                type: object
                properties: &ref_397
                  tags:
                    type: array
                    items:
                      type: object
                      example:
                        - id: 1
                          name: tag1
                          created_at: 2015-10-30 18:20:26 +1100
                        - id: 2
                          name: tag2
                          created_at: 2015-11-30 18:20:26 +1100
                    description: ''
                example: &ref_398
                  tags:
                    - id: 1
                      name: tag1
                      created_at: 2015-10-30 18:20:26 +1100
                    - id: 2
                      name: tag2
                      created_at: 2015-11-30 18:20:26 +1100
        '400':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
        '404':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
  /tags/remove:
    post:
      tags:
        - Tag
      summary: Remove Card Users for Tag
      description: Remove a list of card users from being associated from a card. If the tag no longer has any more card users the it is destroyed.
      operationId: RemoveCardUsersForTag
      parameters:
        - name: id
          in: query
          required: true
          schema:
            type: integer
            description: id of the tag to remove the card users from
        - name: card_ids
          in: query
          required: true
          schema:
            type: array
      security:
        - OAuth2:
            - user
        - cookieAuth: []
      requestBody:
        description: ''
        content:
          text/plain:
            schema:
              type: object
        required: true
      responses:
        '200':
          description: ''
          content:
            text/plain:
              schema:
                type: object
        '400':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
        '404':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
  /tags/:id:
    delete:
      tags:
        - Tag
      summary: Destroy Tag
      description: Destroy a tag for the current user. Any cards associated will be disassocated and not deleted in the transaction. The id must be present in the url.
      operationId: DestroyTag
      parameters:
        - name: id
          in: query
          required: true
          schema:
            type: integer
            description: id of the tag to delete
      security:
        - OAuth2:
            - user
        - cookieAuth: []
      responses:
        '200':
          description: ''
        '404':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                title: 404 Error
                required: *ref_112
                type: object
                properties: *ref_113
                example: *ref_114
  /filters:
    get:
      tags:
        - Saved Filters
      summary: List All Saved Filters
      description: Lists the user's saved filters
      operationId: GetSavedFilters
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Get Saved Filters Response
                type: object
                properties: &ref_399
                  saved_filters:
                    type: array
                    items:
                      type: object
                      properties:
                        name:
                          type: string
                        uuid:
                          type: string
                        created_at:
                          type: string
                          format: date-time
                        payload:
                          type: object
                example: &ref_400
                  saved_filters:
                    - name: Red Creatures
                      created_at: '2015-12-16T02:55:34.086Z'
                      uuid: abcdef1234567890
                      payload:
                        colors:
                          red: true
                        types:
                          creature: true
                    - name: Blue Planeswalkers
                      created_at: '2015-12-16T02:55:34.086Z'
                      uuid: 1234567890abcdef
                      payload:
                        colors:
                          blue: true
                        types:
                          planeswalker: true
        '400':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
    post:
      tags:
        - Saved Filters
      summary: Save a Filter
      description: Creates a new Saved Filter
      operationId: CreateSavedFilter
      requestBody:
        content:
          application/json:
            schema:
              title: Create Saved Filter Request
              type: object
              required: &ref_401
                - payload
              properties: &ref_402
                name:
                  type: string
                  maximum: 64
                payload:
                  type: object
              example: &ref_403
                name: Blue Creatures
                payload:
                  colors:
                    blue: true
                  types:
                    creature: true
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                title: Create Saved Filters Response
                type: object
                properties: &ref_404
                  name:
                    type: string
                  uuid:
                    type: string
                  created_at:
                    type: string
                    format: date-time
                  payload:
                    type: object
                example: &ref_405
                  saved_filter:
                    name: Blue Creatures
                    created_at: '2015-12-16T02:55:34.086Z'
                    uuid: 1234567890abcdef
                    payload:
                      colors:
                        blue: true
                      types:
                        creature: true
        '400':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
  /filters/{uuid}:
    get:
      tags:
        - Saved Filters
      summary: Show Saved Filter
      description: Gets the details of a Saved Filter
      operationId: ShowSavedFilter
      parameters:
        - name: uuid
          in: path
          required: true
          schema:
            type: string
            example: 7cfabcf0-438a-48f5-a4af-5b0f89f768a4
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Show Saved Filter Response
                type: object
                properties: &ref_406
                  name:
                    type: string
                  uuid:
                    type: string
                  created_at:
                    type: string
                    format: date-time
                  payload:
                    type: object
                example: &ref_407
                  saved_filter:
                    name: Blue Creatures
                    created_at: '2015-12-16T02:55:34.086Z'
                    uuid: 1234567890abcdef
                    payload:
                      colors:
                        blue: true
                      types:
                        creature: true
        '400':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
        '404':
          description: Saved Filter not found. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
    delete:
      tags:
        - Saved Filters
      summary: Remove Saved Filter
      description: Removes a Saved Filter
      operationId: RemoveSavedFilter
      parameters:
        - name: uuid
          in: path
          required: true
          schema:
            type: string
            example: 7cfabcf0-438a-48f5-a4af-5b0f89f768a4
      responses:
        '200':
          description: ''
        '400':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
        '404':
          description: Card List not found. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
    patch:
      tags:
        - Saved Filters
      summary: Update Saved Filter
      description: Updates a Saved Filter
      operationId: UpdateSavedFilter
      parameters:
        - name: uuid
          in: path
          required: true
          schema:
            type: string
            example: 7cfabcf0-438a-48f5-a4af-5b0f89f768a4
      requestBody:
        content:
          application/json:
            schema:
              title: Update Saved Filter Request
              type: object
              properties: &ref_408
                name:
                  type: string
                  maximum: 64
                payload:
                  type: object
              example: &ref_409
                name: New Name
                payload:
                  colors:
                    blue: true
                  types:
                    creature: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Update Card List Response
                type: object
                properties: &ref_410
                  name:
                    type: string
                  uuid:
                    type: string
                  created_at:
                    type: string
                    format: date-time
                  payload:
                    type: object
                example: &ref_411
                  saved_filter:
                    name: Blue Creatures
                    created_at: '2015-12-16T02:55:34.086Z'
                    uuid: 1234567890abcdef
                    payload:
                      colors:
                        blue: true
                      types:
                        creature: true
        '400':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
        '404':
          description: Card List not found. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
  /statistics/tags:
    post:
      tags:
        - Statistics
      summary: Get number of cards and total value by tag
      description: Get total number of cards and total value for each matching tag in descending order by tag name.
      operationId: GetTagStatistics
      parameters:
        - name: query
          in: query
          required: false
          schema:
            type: string
        - name: sort_by
          in: query
          required: false
          schema:
            type: string
            default: name
            enum:
              - name
              - total_value
              - total_cards
        - name: order
          in: query
          required: false
          schema:
            type: string
            default: desc
            enum:
              - asc
              - desc
        - name: limit
          in: query
          required: false
          schema:
            type: integer
            default: 10
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Set Completion Response
                type: object
                example: &ref_412
                  - tag:
                      id: 1
                      name: graveyard
                      created_at: Mon
                      27 Jun 2016 04:38:38 UTC +00:00: null
                    total_cards: 50
                    total_value: 1500
                  - tag:
                      id: 2
                      name: graveyard_hate
                      created_at: Mon
                      27 Jun 2016 04:38:38 UTC +00:00: null
                    total_cards: 8
                    total_value: 250
  /statistics/colors:
    get:
      tags:
        - Statistics
      summary: Get statistics of owned cards for each color identity
      description: Get total cards per color identity.
      operationId: GetStatisticsColors
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                required:
                  - mana_counts
                type: object
                properties:
                  total_count:
                    type: object
                    example: 438
                  white:
                    type: object
                    example: 22
                  blue:
                    type: object
                    example: 44
                  black:
                    type: object
                    example: 30
                  red:
                    type: object
                    example: 27
                  green:
                    type: object
                    example: 34
                  colorless:
                    type: object
                    example: 9
                  white_blue:
                    type: object
                    example: 14
                  red_white:
                    type: object
                    example: 18
                  blue_black:
                    type: object
                    example: 8
                  black_green:
                    type: object
                    example: 31
                  red_green:
                    type: object
                    example: 20
                  blue_red:
                    type: object
                    example: 5
                  white_black:
                    type: object
                    example: 17
                  black_red:
                    type: object
                    example: 21
                  white_green:
                    type: object
                    example: 23
                  blue_green:
                    type: object
                    example: 10
                  white_black_green:
                    type: object
                    example: 3
                  white_blue_green:
                    type: object
                    example: 5
                  white_blue_black:
                    type: object
                    example: 11
                  blue_black_red:
                    type: object
                    example: 8
                  white_blue_red:
                    type: object
                    example: 15
                  black_red_green:
                    type: object
                    example: 1
                  white_black_red:
                    type: object
                    example: 9
                  white_red_green:
                    type: object
                    example: 13
                  blue_black_green:
                    type: object
                    example: 21
                  blue_red_green:
                    type: object
                    example: 14
                  blue_black_red_green:
                    type: object
                    example: 1
                  white_black_red_green:
                    type: object
                    example: 0
                  white_blue_red_green:
                    type: object
                    example: 0
                  white_blue_black_green:
                    type: object
                    example: 1
                  white_blue_black_red:
                    type: object
                    example: 2
                  white_blue_black_red_green:
                    type: object
                    example: 1
  /statistics/types:
    get:
      tags:
        - Statistics
      summary: Get statistics of owned cards for each card type
      description: 'Get total collection size and total number of cards, total value for each card type. The core types only include cards that have 1 type, while ''Multitype'' includes all cards that have more than 1 type (e.g: enchantment creature). The ''Other'' category includes all single type cards that are are not part of the core categories (e.g.: tribal)'
      operationId: GetStatisticsTypes
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                required:
                  - card_types
                type: object
                properties:
                  total_cards:
                    type: integer
                    example: 132
                  type_counts:
                    type: object
                    properties:
                      land:
                        type: object
                        example:
                          total_cards: 12
                          total_value: 522
                      creature:
                        type: object
                        example:
                          total_cards: 44
                          total_value: 698
                      instant:
                        type: object
                        example:
                          total_cards: 16
                          total_value: 114
                      sorcery:
                        type: object
                        example:
                          total_cards: 35
                          total_value: 471
                      enchantment:
                        type: object
                        example:
                          total_cards: 19
                          total_value: 267
                      artifact:
                        type: object
                        example:
                          total_cards: 6
                          total_value: 166
                      planeswalker:
                        type: object
                        example:
                          total_cards: 9
                          total_value: 870
                      multitype:
                        type: object
                        example:
                          total_cards: 25
                          total_value: 339
                      other:
                        type: object
                        example:
                          total_cards: 3
                          total_value: 119
  /statistics/mana:
    get:
      tags:
        - Statistics
      summary: Get amount of mana produced by basic lands
      description: Get total amount of mana produced by all basic lands and also for each type of basic land. Includes snow-covered basic lands.
      operationId: GetStatisticsMana
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                required:
                  - card_types
                type: object
                properties:
                  mana_count:
                    type: integer
                    example: 117
                  mana_colors:
                    type: object
                    properties:
                      white:
                        type: object
                        example: 23
                      blue:
                        type: object
                        example: 30
                      black:
                        type: object
                        example: 19
                      red:
                        type: object
                        example: 35
                      green:
                        type: object
                        example: 10
  /statistics/set_completion:
    post:
      tags:
        - Statistics
      summary: Get set completion statistics
      description: Get set statistics for all matching sets. If query param is present then card_set_id is disabled
      operationId: GetSetCompletionStatistics
      parameters:
        - name: query
          in: query
          required: false
          schema:
            type: string
        - name: sort_by
          in: query
          required: false
          schema:
            type: string
            default: release_date
            enum:
              - release_date
              - set_name
              - set_code
              - completion
        - name: order
          in: query
          required: false
          schema:
            type: string
            default: desc
            enum:
              - asc
              - desc
        - name: limit
          in: query
          required: false
          schema:
            type: integer
            default: 10
          deprecated: true
          description: '<b>Note</b>: use `per_page` instead'
        - name: per_page
          in: query
          required: false
          schema:
            type: integer
            default: 10
        - name: page
          in: query
          required: false
          schema:
            type: integer
            default: 1
        - name: card_set_id
          in: query
          required: false
          schema:
            type: integer
            default: nil
        - name: set_types
          in: query
          required: false
          schema:
            type: string
            default: nil
            enum:
              - core
              - masters
              - expansion
              - memorabilia
              - commander
              - archenemy
              - box
              - draft_innovation
              - arsenal
              - funny
              - starter
              - duel_deck
              - from_the_vault
              - masterpiece
              - promo
              - premium_deck
              - planechase
              - token
              - vanguard
              - spellbook
        - name: include_empty
          in: query
          required: false
          schema:
            type: boolean
            default: false
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Set Completion Response
                type: object
                example: &ref_413
                  - card_set:
                      id: 1
                      name: Urza's Saga
                      set_code: USG
                      set_type: expansion
                      release_date: '1998-10-01T00:00:00.000Z'
                    total_cards: 350
                    total_owned: 369
                    unique_owned: 241
                    completion: 0.69
                  - card_set:
                      id: 2
                      name: Collector's Edition
                      set_code: CED
                      set_type: expansion
                      release_date: '1993-12-01T00:00:00.000Z'
                    total_cards: 363
                    total_owned: 427
                    unique_owned: 300
                    completion: 0.7
                  - card_set:
                      id: 2
                      name: International Collector's Edition
                      set_code: CEI
                      set_type: expansion
                      release_date: '1993-12-01T00:00:00.000Z'
                    total_cards: 363
                    total_owned: 427
                    unique_owned: 300
                    completion: 0.7
  /statistics/overview:
    get:
      tags:
        - Statistics
      summary: Get overview statistics for whole collection
      description: Get count and/or value for staged, cards, decks, and tags.
      operationId: GetStatisticsOverview
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                required:
                  - overview
                type: object
                properties:
                  overview:
                    type: object
                    properties:
                      staged_value:
                        type: integer
                        example: 120
                      staged_count:
                        type: integer
                        example: 14
                      card_value:
                        type: integer
                        example: 3844
                      card_count:
                        type: integer
                        example: 1208
                      deck_count:
                        type: integer
                        example: 9
                      deck_value:
                        type: integer
                        example: 1715
                      tag_count:
                        type: integer
                        example: 22
  /input_sources:
    get:
      tags:
        - Input Sources
      summary: Get list of input sources
      description: Get list of input sources for authenticated user
      operationId: InputSourcesIndex
      parameters:
        - name: source_type
          in: query
          required: false
          schema:
            type: string
            enum:
              - cardbot
              - app
              - manual
              - import
              - unknown
        - name: name
          in: query
          required: false
          schema:
            type: string
        - name: ancilliary_id
          in: query
          required: false
          schema:
            type: string
            example: 7cfabcf0-438a-48f5-a4af-5b0f89f768a4
        - name: legacy
          in: query
          required: false
          schema:
            type: boolean
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Get Input Sources Response
                required: &ref_414
                  - input_sources
                type: object
                properties: &ref_415
                  input_sources:
                    type: array
                    example:
                      - id: 7
                        uuid: b6c75bf3-d1c2-4967-97c7-d63e9e1fdcb1
                        name: iPhone
                        source_type: app
                        user_id: 3
                        legacy: false
                        ancilliary_id: a5e3ad9c-a63e-49e8-908a-99b86a68b432
                        created_at: '2023-02-16T22:47:02.792Z'
                        updated_at: '2023-02-16T22:47:02.792Z'
                      - id: 8
                        uuid: 8c37a872-e1dc-4e9b-9b3d-acedb5eb21d3
                        name: carbot
                        source_type: cardbot
                        user_id: 3
                        legacy: true
                        ancilliary_id: g67ko6v0-a63e-49e8-908a-99b86a68b432
                        created_at: '2023-02-16T22:47:02.794Z'
                        updated_at: '2023-02-16T22:47:02.794Z'
    post:
      tags:
        - Input Sources
      summary: Create new input source
      description: Create new input source for authenticated user
      operationId: InputSourcesCreate
      parameters:
        - name: source_type
          in: query
          required: true
          schema:
            type: string
            enum:
              - cardbot
              - app
              - manual
              - import
              - unknown
        - name: name
          in: query
          required: true
          schema:
            type: string
        - name: ancilliary_id
          in: query
          required: false
          schema:
            type: string
            example: 7cfabcf0-438a-48f5-a4af-5b0f89f768a4
          description: ancilliary_id should be a unique identifier created by the client
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Create Input Source Response
                required: &ref_416
                  - input_source
                type: object
                properties: &ref_417
                  input_source:
                    type: object
                    example:
                      id: 7
                      uuid: b6c75bf3-d1c2-4967-97c7-d63e9e1fdcb1
                      name: iPhone
                      source_type: app
                      user_id: 3
                      legacy: false
                      ancilliary_id: a5e3ad9c-a63e-49e8-908a-99b86a68b432
                      created_at: '2023-02-16T22:47:02.792Z'
                      updated_at: '2023-02-16T22:47:02.792Z'
  /input_sources/{uuid}:
    get:
      tags:
        - Input Sources
      summary: Get input source
      description: Get specific input source for authenticated user
      operationId: InputSourcesShow
      parameters:
        - name: uuid
          in: path
          required: true
          schema:
            type: string
            example: 7cfabcf0-438a-48f5-a4af-5b0f89f768a4
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Show Input Source Response
                required: &ref_118
                  - input_source
                type: object
                properties: &ref_119
                  input_source:
                    type: object
                    example:
                      id: 7
                      uuid: b6c75bf3-d1c2-4967-97c7-d63e9e1fdcb1
                      name: iPhone
                      source_type: app
                      user_id: 3
                      legacy: false
                      ancilliary_id: a5e3ad9c-a63e-49e8-908a-99b86a68b432
                      created_at: '2023-02-16T22:47:02.792Z'
                      updated_at: '2023-02-16T22:47:02.792Z'
    patch:
      tags:
        - Input Sources
      summary: Update input source name
      description: Update name of specific input source owned by authenticated user
      operationId: InputSourcesUpdate
      parameters:
        - name: uuid
          in: path
          required: true
          schema:
            type: string
            example: 7cfabcf0-438a-48f5-a4af-5b0f89f768a4
        - name: name
          in: query
          required: true
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Show Input Source Response
                required: *ref_118
                type: object
                properties: *ref_119
  /sessions:
    get:
      tags:
        - Sessions
      summary: Get list of input sessions
      description: Get list of input sessions for authenticated user
      operationId: GetSessionsIndex
      parameters:
        - name: staged
          in: query
          required: false
          schema:
            type: boolean
        - name: source_type
          in: query
          required: false
          schema:
            enum:
              - cardbot
              - app
              - manual
              - import
              - unknown
        - name: source_name
          in: query
          required: false
          schema:
            type: string
        - name: sort_by
          in: query
          required: false
          schema:
            enum:
              - created_at
              - committed_at
        - name: order
          in: query
          required: false
          schema:
            enum:
              - asc
              - desc
        - name: game
          in: query
          required: false
          schema:
            type: string
            default: mtg
            enum:
              - poke
              - mtg
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Get Sessions Response
                required: &ref_418
                  - sessions
                  - total_cards
                  - total_value
                  - total
                type: object
                properties: &ref_419
                  total_cards:
                    type: integer
                    example: 5
                  total_value:
                    type: integer
                    example: 5605
                  total:
                    type: integer
                    example: 3
                  sessions:
                    type: array
                    example:
                      - id: 8
                        uuid: 8c37a872-e1dc-4e9b-9b3d-acedb5eb21d3
                        committed_at: '2023-02-16T22:47:02.793Z'
                        user_id: 3
                        input_source_id: 3
                        deleted_at: nil
                        created_at: '2023-02-16T22:47:02.794Z'
                        updated_at: '2023-02-16T22:47:02.794Z'
                        total_value: 2480
                        total_count: 3
                        metadata:
                          id: 1
                          uuid: 8c37a872-e1dc-4e9b-9b3d-acedb5eb21d3
                          input_session_id: 7
                          created_at: '2023-02-16T22:47:02.792Z'
                          updated_at: '2023-02-16T22:47:02.792Z'
                        game: mtg
                      - id: 7
                        uuid: b6c75bf3-d1c2-4967-97c7-d63e9e1fdcb1
                        committed_at: '2023-02-16T22:47:02.792Z'
                        user_id: 3
                        input_source_id: 3
                        deleted_at: nil
                        created_at: '2023-02-16T22:47:02.792Z'
                        updated_at: '2023-02-16T22:47:02.792Z'
                        total_value: 3125
                        total_count: 2
                        metadata:
                          id: 2
                          uuid: b6c75bf3-d1c2-4967-97c7-d63e9e1fdcb1
                          input_session_id: 7
                          created_at: '2023-02-16T22:47:02.792Z'
                          updated_at: '2023-02-16T22:47:02.792Z'
                        game: mtg
    post:
      tags:
        - Sessions
      summary: Create input session
      description: Create a new session for authenticated user. The input source UUID must belong to the user.
      operationId: PostSessionsCreate
      parameters:
        - name: source
          in: query
          required: true
          schema:
            type: string
            example: 7cfabcf0-438a-48f5-a4af-5b0f89f768a4
        - name: metadata
          in: query
          required: false
          schema:
            type: object
          description: polymorphic object, structure depends on the the source type associated with the session
        - name: game
          in: query
          required: false
          schema:
            type: string
            default: mtg
            enum:
              - poke
              - mtg
              - yugi
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Create Sessions Response
                required: &ref_420
                  - session
                type: object
                properties: &ref_421
                  session:
                    type: object
                    example:
                      id: 7
                      uuid: b6c75bf3-d1c2-4967-97c7-d63e9e1fdcb1
                      committed_at: '2023-02-16T22:47:02.792Z'
                      user_id: 3
                      input_source_id: 3
                      deleted_at: nil
                      created_at: '2023-02-16T22:47:02.792Z'
                      updated_at: '2023-02-16T22:47:02.792Z'
                      total_value: 3125
                      total_count: 2
                      metadata:
                        id: 2
                        uuid: 8c37a872-e1dc-4e9b-9b3d-acedb5eb21d3
                        input_session_id: 7
                        created_at: '2023-02-16T22:47:02.792Z'
                        updated_at: '2023-02-16T22:47:02.792Z'
                      game: mtg
  /sessions/{uuid}:
    get:
      tags:
        - Sessions
      summary: Get an input session
      description: Get a specific session for authenticated user
      operationId: GetSessionsUUID
      parameters:
        - name: uuid
          in: path
          required: true
          schema:
            type: string
            example: 7cfabcf0-438a-48f5-a4af-5b0f89f768a4
        - name: source
          in: query
          required: true
          schema:
            type: string
            example: 7cfabcf0-438a-48f5-a4af-5b0f89f768a4
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Get Specific Session Response
                required: &ref_120
                  - session
                type: object
                properties: &ref_121
                  session:
                    type: object
                    example:
                      id: 7
                      uuid: b6c75bf3-d1c2-4967-97c7-d63e9e1fdcb1
                      committed_at: '2023-02-16T22:47:02.792Z'
                      user_id: 3
                      input_source_id: 3
                      deleted_at: nil
                      created_at: '2023-02-16T22:47:02.792Z'
                      updated_at: '2023-02-16T22:47:02.792Z'
                      total_value: 2480
                      total_count: 3
                      metadata: nil
                      game: mtg
    delete:
      tags:
        - Sessions
      summary: Delete uncommitted session
      description: Delete a specific uncommitted session and all associated card instances
      operationId: DeleteSessionsUUID
      parameters:
        - name: uuid
          in: path
          required: true
          schema:
            type: string
            example: 7cfabcf0-438a-48f5-a4af-5b0f89f768a4
        - name: game
          in: query
          required: false
          schema:
            type: string
            default: mtg
            enum:
              - mtg
              - poke
              - yugi
      responses:
        '200':
          description: ''
  /sessions/{uuid}/add_cards:
    post:
      tags:
        - Sessions
      summary: Add cards to input session
      description: Adds cards to an existing session that belongs to the authenticated user
      operationId: PostAddCardsToSession
      parameters:
        - name: uuid
          in: path
          required: true
          schema:
            type: string
            example: 7cfabcf0-438a-48f5-a4af-5b0f89f768a4
        - name: card_users
          in: query
          required: true
          schema:
            type: array
            items:
              type: object
              properties:
                json_id:
                  type: string
                  description: Only applicable for MTG input sessions
                uuid:
                  type: string
                quality:
                  type: string
                foil:
                  type: boolean
                language:
                  type: string
                quantity:
                  type: integer
                finish_uuid:
                  type: string
                  description: UUID of poke_finish (Only applicable for pokemon input sessions)
                card_set_uuid:
                  type: string
                  description: UUID of yugi_card_set (Only applicable for yugioh input sessions)
                card_printing_uuid:
                  type: string
                  description: UUID of yugi_card_printing (Only applicable for yugioh input sessions)
            example: '[{ json_id: 5574a332e1dc7eda41dbe27086c8b12, quality: Near Mint, foil: false }, { json_id: 5574a332e1dc7eda41dbe27086d1t85, quality: Near Mint, foil: false }]'
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Add Cards To Sessions Response
                required: &ref_422
                  - card_users
                type: object
                properties: &ref_423
                  card_users:
                    type: array
                    example:
                      - id: 1
                        card_id: 2
                        foil: false
                        quality: Near Mint
                        language: en
                        created_at: '2023-02-16 23:59:39.17455'
                        updated_at: '2023-02-16 23:59:39.17455'
                        input_session_id: 2
                        json_id: 5574a332e1dc7eda41dbe27086c8b12
                        mana_cost:
                          - quam
                          - odit
                          - fuga
                        name: molestiae voluptates
                        price: 145
                        price_foil: 3053
                        ck_price: 2460
                        ck_price_foil: 1551
                        cm_price: 51
                        cm_price_foil: 65
                        collector_number: 83a
                        sub_type:
                          - eaque
                          - omnis
                          - labore
                        types:
                          - Elite
                          - Scheme
                          - Wolf
                          - Instant
                        rarity: Mythic Rare
                        set_code: AUR
                        set_name: rerum repudiandae
                        foreign_name: architecto ex
                  errors:
                    type: array
                    example: []
  /sessions/{uuid}/restore:
    patch:
      tags:
        - Sessions
      summary: Revert deletion of uncommitted input session
      description: Revert deletion of uncommitted input session
      operationId: PatchSessionsUUID
      parameters:
        - name: uuid
          in: path
          required: true
          schema:
            type: string
            example: 7cfabcf0-438a-48f5-a4af-5b0f89f768a4
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Get Specific Session Response
                required: *ref_120
                type: object
                properties: *ref_121
  /sessions/{uuid}/commit:
    post:
      tags:
        - Sessions
      summary: Commit a session
      description: Commit a session. Route is polymorphic, and will accept input sessions for all games.
      operationId: PostSessionsCommit
      parameters:
        - name: uuid
          in: path
          required: true
          schema:
            type: string
            example: 7cfabcf0-438a-48f5-a4af-5b0f89f768a4
          description: session passed must not be committed
        - name: tags
          in: query
          required: false
          schema:
            type: array
            example: '[''binder 5'', ''$ > 100'', ''black'']'
            description: <b>Only implemented for MTG</b>. Adds tags to all card_users in session
        - name: location_path
          in: query
          required: false
          schema:
            type: array
            example: '[''warehouse-1'', ''rack-1'', ''shelf-A'', ''box-1'']'
          description: <b>Only implemented for MTG</b>. Creates empty locations in the path when they do not already exist. Also creates stacked cards from the card users in the session and links them to the last location in the path.
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Commit a specific session
                required: &ref_424
                  - card_ids
                type: object
                properties: &ref_425
                  card_ids:
                    type: array
                    example:
                      - 7
                      - 8
                      - 10
                      - 56
                      - 88
                      - 14
                  location:
                    type: object
                    example:
                      id: 1
                      name: box-1
                      uuid: b6c75bf3-d1c2-4967-97c7-d63e9e1fdcb1
                      parent_id: 1
                      user_id: 1
                      created_at: '2023-02-16T22:47:02.792Z'
                      updated_at: '2023-02-16T22:47:02.792Z'
  /sessions/clear_staged:
    delete:
      tags:
        - Sessions
      summary: Delete staged sessions
      description: Delete all staged sessions and associated card instances
      parameters:
        - name: source_type
          in: query
          required: false
          schema:
            type: string
            enum:
              - cardbot
              - app
              - manual
              - import
              - unknown
        - name: source_name
          in: query
          required: false
          schema:
            type: string
          description: Selects all sources that include the query string in their name
        - name: game
          in: query
          required: false
          schema:
            type: string
            default: mtg
            enum:
              - poke
              - mtg
              - yugi
      operationId: SessionsClearStaged
      responses:
        '200':
          description: ''
  /sessions/commit_staged:
    post:
      tags:
        - Sessions
      summary: Commit staged sessions
      description: Commit all staged sessions of the correct game (defaults to MTG). If params are passed, only commits sessions that match the params.
      parameters:
        - name: source_type
          in: query
          required: false
          schema:
            enum:
              - cardbot
              - app
              - manual
              - import
              - unknown
        - name: source_name
          in: query
          required: false
          schema:
            type: string
          description: Selects all sources that include the query string in their name
        - name: game
          in: query
          required: false
          schema:
            type: string
            default: mtg
            enum:
              - poke
              - mtg
      operationId: SessionsCommitStaged
      responses:
        '200':
          description: ''
  /stacked_cards/{uuid}/update_position:
    post:
      tags:
        - Stacked Cards
      summary: Update position of stacked card
      description: Update position of stacked card. Also updates other stacked card positions in the same location
      operationId: StackedCardsUpdatePosition
      parameters:
        - name: uuid
          in: path
          required: true
          schema:
            type: string
            example: 7cfabcf0-438a-48f5-a4af-5b0f89f768a4
        - name: position
          in: query
          required: true
          schema:
            type: integer
            description: New position for the stacked card
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Update position of specific stacked card
                required: &ref_426
                  - stacked_card
                type: object
                properties: &ref_427
                  stacked_card:
                    type: object
                    example:
                      id: 11
                      uuid: f18092a2-deb0-45c7-b0d6-ede97fb509b3
                      position: 1
                      card_user_id: 21
                      location_id: 3
                      created_at: '2023-02-16T22:47:02.792Z'
                      updated_at: '2023-02-16T22:47:02.792Z'
  /stacked_cards/{uuid}:
    delete:
      tags:
        - Stacked Cards
      summary: Delete stacked card
      description: Delete a specific stacked sards
      operationId: StackedCardDelete
      parameters:
        - name: uuid
          in: path
          required: true
          schema:
            type: string
            example: 7cfabcf0-438a-48f5-a4af-5b0f89f768a4
      responses:
        '204':
          description: No Content
  /locations:
    get:
      tags:
        - Locations
      summary: Get list of locations
      description: Get list of locations for authenticated user
      operationId: LocationsIndex
      parameters:
        - name: uuid
          in: query
          required: false
          schema:
            type: integer
            description: Selects a parent location with the matching `uuid`. When used in combination with `depth`, allows for returning up to 2 level of children, starting from the parent location selected. When `uuid` is not present or is null, it returns all top level locations.
        - name: name
          in: query
          required: false
          schema:
            type: string
            description: Selects locations with matching `name`. These locations become the parent locations in case a depth param is also passed.
        - name: depth
          in: query
          required: false
          schema:
            type: integer
            default: 1
            minimum: 1
            maximum: 3
            description: Number of children levels to be returned. A depth of 1 (the default) returns only the parents; a depth of 2 returns parents and children; and a depth of 3 (the max) returns parents, children, and grandchildren.
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Get Locations Response
                required: &ref_122
                  - locations
                type: object
                properties: &ref_123
                  locations:
                    type: object
                    example:
                      dbb82cb8-e362-4078-ac86-d2cbebac6ed8:
                        id: 8
                        uuid: dbb82cb8-e362-4078-ac86-d2cbebac6ed8
                        name: Shelf-1
                        parent_id: 2
                        user_id: 1
                        created_at: '2023-02-16T22:47:02.792Z'
                        updated_at: '2023-02-16T22:47:02.792Z'
                        children: {}
                      b6c75bf3-d1c2-4967-97c7-d63e9e1fdcb1:
                        id: 7
                        uuid: b6c75bf3-d1c2-4967-97c7-d63e9e1fdcb1
                        name: Box-2
                        parent_id: 5
                        user_id: 1
                        created_at: '2023-02-16T22:47:02.792Z'
                        updated_at: '2023-02-16T22:47:02.792Z'
                        children: {}
    post:
      tags:
        - Locations
      summary: Create new location
      description: Create new location for authenticated user
      operationId: LocationsCreate
      parameters:
        - name: name
          in: query
          required: true
          schema:
            type: string
        - name: parent
          in: query
          required: false
          schema:
            type: string
            example: 7cfabcf0-438a-48f5-a4af-5b0f89f768a4
          description: Links an existing location as parent
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Create Locations Response
                required: &ref_428
                  - location
                type: object
                properties: &ref_429
                  location:
                    type: object
                    example:
                      id: 8
                      uuid: dbb82cb8-e362-4078-ac86-d2cbebac6ed8
                      name: Shelf-1
                      parent_id: 2
                      user_id: 1
                      created_at: '2023-02-16T22:47:02.792Z'
                      updated_at: '2023-02-16T22:47:02.792Z'
  /locations/create_path:
    post:
      tags:
        - Locations
      summary: Create locations in path
      description: Create locations in path for authenticated user. If record already exists with a name in the path then a new record is not created.
      operationId: LocationsPathCreate
      parameters:
        - name: locations_list
          in: query
          required: true
          schema:
            type: array
            example: '[''Warehouse-1'', ''Shelf-A'', ''Box-2'']'
          description: Names of locations to be created
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Get Locations Response
                required: *ref_122
                type: object
                properties: *ref_123
  /locations/merge:
    post:
      tags:
        - Locations
      summary: Merge location stacked cards
      description: Merge stacked cards from several locations onto one target location
      operationId: LocationsMerge
      parameters:
        - name: target
          in: query
          required: true
          schema:
            type: string
            example: 7cfabcf0-438a-48f5-a4af-5b0f89f768a4
        - name: source_locations
          in: query
          required: true
          schema:
            type: array
            example: '[''7cfabcf0-438a-48f5-a4af-5b0f89f768a4'', ''38bd4a74-6f97-4f2c-ad55-6fe59702b7df'']'
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Show Locations Response
                required: &ref_124
                  - location
                type: object
                properties: &ref_125
                  location:
                    type: object
                    example:
                      id: 8
                      uuid: dbb82cb8-e362-4078-ac86-d2cbebac6ed8
                      name: Shelf-1
                      parent_id: 2
                      user_id: 1
                      created_at: '2023-02-16T22:47:02.792Z'
                      updated_at: '2023-02-16T22:47:02.792Z'
  /locations/{uuid}:
    get:
      tags:
        - Locations
      summary: Get location
      description: Get specific location for authenticated user
      operationId: LocationsShow
      parameters:
        - name: uuid
          in: path
          required: true
          schema:
            type: string
            example: 7cfabcf0-438a-48f5-a4af-5b0f89f768a4
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Show Locations Response
                required: *ref_124
                type: object
                properties: *ref_125
    patch:
      tags:
        - Locations
      summary: Update location
      description: Update name or parent of specific location owned by authenticated user
      operationId: LocationsUpdate
      parameters:
        - name: uuid
          in: path
          required: true
          schema:
            type: string
            example: 7cfabcf0-438a-48f5-a4af-5b0f89f768a4
        - name: parent
          in: query
          required: false
          schema:
            type: string
            example: 8av5iif9-438a-48f5-a4af-5b0f89f768a2
        - name: name
          in: query
          required: false
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Show Locations Response
                required: *ref_124
                type: object
                properties: *ref_125
    delete:
      tags:
        - Locations
      summary: Delete location
      description: Delete a specific location
      operationId: DeleteLocationUUID
      parameters:
        - name: uuid
          in: path
          required: true
          schema:
            type: string
            example: 7cfabcf0-438a-48f5-a4af-5b0f89f768a4
      responses:
        '204':
          description: No Content
  /locations/{uuid}/path:
    get:
      tags:
        - Locations
      summary: Get locations in path
      description: Get records in path between top of the order structure till the target location.
      operationId: LocationsPathGet
      parameters:
        - name: uuid
          in: path
          required: true
          schema:
            type: string
            example: 7cfabcf0-438a-48f5-a4af-5b0f89f768a4
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Get Locations Response
                required: *ref_122
                type: object
                properties: *ref_123
  /subscriptions:
    get:
      tags:
        - Subscription
      summary: Get Subscription Details
      description: Get the details of the current subscription plan available.
      operationId: GetSubscriptionDetails
      security:
        - OAuth2:
            - user
        - cookieAuth: []
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Get Subscription Details response
                required: &ref_430
                  - id
                  - object
                  - amount
                  - created
                  - currency
                  - interval
                  - interval_count
                  - livemode
                  - metadata
                  - name
                  - statement_descriptor
                  - trial_period_days
                type: object
                properties: &ref_431
                  id:
                    type: string
                    example: gold21323
                  object:
                    type: string
                    example: plan
                  amount:
                    type: integer
                    format: int32
                    example: 2000
                  created:
                    type: integer
                    format: int32
                    example: 1386247539
                  currency:
                    type: string
                    example: usd
                  interval:
                    type: string
                    example: month
                  interval_count:
                    type: integer
                    format: int32
                    example: 1
                  livemode:
                    type: boolean
                    example: false
                  metadata:
                    type: object
                    example: {}
                  name:
                    type: string
                    example: New plan name
                  statement_descriptor:
                    type: string
                    nullable: true
                  trial_period_days:
                    type: string
                    nullable: true
                example: &ref_432
                  id: gold21323
                  object: plan
                  amount: 2000
                  created: 1386247539
                  currency: usd
                  interval: month
                  interval_count: 1
                  livemode: false
                  metadata: {}
                  name: New plan name
                  statement_descriptor: null
                  trial_period_days: null
    post:
      tags:
        - Subscription
      summary: Create Subscription
      description: |-
        Create a subscription for the user. The token object passed should be generated using
        the Stripe API and not manually generated.
      operationId: CreateSubscription
      security:
        - OAuth2:
            - user
        - cookieAuth: []
      requestBody:
        description: ''
        content:
          application/json:
            schema:
              title: Create Subscription request
              required: &ref_433
                - token
              type: object
              properties: &ref_434
                token:
                  type: object
                  example:
                    id: tok_17QPb32eZvKYlo2C56XNrzfR
                    object: token
                    card:
                      id: card_17QPb32eZvKYlo2CI6T5FqpO
                      object: card
                      address_city: null
                      address_country: null
                      address_line1: null
                      address_line1_check: null
                      address_line2: null
                      address_state: null
                      address_zip: null
                      address_zip_check: null
                      brand: Visa
                      country: US
                      cvc_check: null
                      dynamic_last4: null
                      exp_month: 8
                      exp_year: 2017
                      funding: credit
                      last4: 4242
                      metadata: {}
                      name: null
                      tokenization_method: null
                    client_ip: null
                    created: 1452118657
                    livemode: false
                    type: card
                    used: false
                  description: The stripe token object returned by the Stripe API
              example: &ref_435
                token:
                  id: tok_17QPb32eZvKYlo2C56XNrzfR
                  object: token
                  card:
                    id: card_17QPb32eZvKYlo2CI6T5FqpO
                    object: card
                    address_city: null
                    address_country: null
                    address_line1: null
                    address_line1_check: null
                    address_line2: null
                    address_state: null
                    address_zip: null
                    address_zip_check: null
                    brand: Visa
                    country: US
                    cvc_check: null
                    dynamic_last4: null
                    exp_month: 8
                    exp_year: 2017
                    funding: credit
                    last4: 4242
                    metadata: {}
                    name: null
                    tokenization_method: null
                  client_ip: null
                  created: 1452118657
                  livemode: false
                  type: card
                  used: false
        required: true
      responses:
        '200':
          description: ''
          content:
            text/plain:
              schema:
                type: object
        '400':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
    delete:
      tags:
        - Subscription
      summary: Cancel Subscription
      description: Cancel the current subscription the user has and logs reason in record
      operationId: CancelSubscription
      parameters:
        - name: reason
          in: query
          required: false
          schema:
            type: string
            example: Spent all my money on magic cards
      security:
        - OAuth2:
            - user
        - cookieAuth: []
      responses:
        '200':
          description: ''
  /subscriptions/referral:
    post:
      tags:
        - Subscription
      summary: Send Subscription Referrals
      description: Sends referral emails for people to join CardCastle
      operationId: SendSubscriptionReferrals
      security:
        - OAuth2:
            - user
        - cookieAuth: []
      requestBody:
        description: ''
        content:
          application/json:
            schema:
              title: Send Subscription Referrals request
              required: &ref_436
                - email
              type: object
              properties: &ref_437
                email:
                  type: array
                  items:
                    type: string
                    example:
                      - <EMAIL>
                      - <EMAIL>
                      - <EMAIL>
                  description: ''
              example: &ref_438
                email:
                  - <EMAIL>
                  - <EMAIL>
                  - <EMAIL>
        required: true
      responses:
        '200':
          description: ''
          content:
            text/plain:
              schema:
                type: object
        '400':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
  /subscriptions/promotion:
    post:
      tags:
        - Subscription
      summary: Apply Coupon
      description: Apply a coupon and create a promotional subscription for the user
      operationId: ApplyCoupon
      security:
        - OAuth2:
            - user
        - cookieAuth: []
      requestBody:
        description: ''
        content:
          application/json:
            schema:
              title: Apply Coupon request
              required: &ref_439
                - code
              type: object
              properties: &ref_440
                code:
                  type: string
                  example: example code
              example: &ref_441
                code: example code
        required: true
      responses:
        '200':
          description: ''
          content:
            text/plain:
              schema:
                type: object
        '400':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
  /subscriptions/paypal:
    get:
      tags:
        - Subscription
      summary: Get Paypal redirect
      description: Undocumented, used by frontend to redirect user to the paypal website to sign in
      responses:
        '301':
          description: Successful redirect to paypal
  /subscriptions/paypal/process:
    post:
      tags:
        - Subscription
      summary: Process paypal subscription
      description: Undocumented, used by paypal to redirect user to the back to the frontend, also subscribes the user with the details passed
      responses:
        '200':
          description: Successful subscribed user
  /subscriptions/configuration:
    get:
      tags:
        - Subscription
      summary: Get subscription provider configurations
      description: Returns global configuration values for support payment providers
      responses:
        '200':
          description: Successful subscribed user
          content:
            application/json:
              schema:
                title: Subscription configuration
                type: object
                properties: &ref_442
                  stripe:
                    type: object
                    properties:
                      customer_portal:
                        type: string
                        example: https://stripe.com
                      payment_links:
                        type: object
                        properties:
                          knight:
                            type: object
                            properties:
                              annual:
                                type: string
                                example: https://stripe.com
                              monthly:
                                type: string
                                example: https://stripe.com
  /subscriptions/stripe_portal:
    get:
      tags:
        - Subscription
      summary: Get route for user stripe portal
      description: Returns route for user stripe portal
      responses:
        '200':
          description: Successfully created portal url
          content:
            application/json:
              schema:
                title: Subscription stripe portal
                type: object
                properties: &ref_443
                  portal_session:
                    type: object
                    properties:
                      id:
                        type: string
                        example: bpc_1MrnZsLkdIwHu7ixNiQL1xPM
                      object:
                        type: string
                        example: billing_portal.session
                      configuration:
                        type: string
                        example: bpc_1MAhNDLkdIwHu7ixckACO1Jq
                      created:
                        type: integer
                        example: 1680210639
                      customer:
                        type: string
                        example: cus_NciAYcXfLnqBoz
                      flow:
                        type: object
                        example: null
                      livemode:
                        type: boolean
                        example: false
                      locale:
                        type: string
                        example: null
                      on_behalf_of:
                        type: string
                        example: null
                      return_url:
                        type: string
                        example: https://cardcastle.co/settings/subscriptions
                      url:
                        type: string
                        example: https://billing.stripe.com/p/session/test_YWNjdF8xTTJKVGtMa2RJd0h1N2l4LF9OY2lBYjJXcHY4a2NPck96UjBEbFVYRnU5bjlwVUF50100BUtQs3bl
  /public/{username}:
    get:
      tags:
        - Public
      summary: Get Current User
      description: ''
      operationId: GetCurrentUser1
      parameters:
        - name: username
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: ''
  /public/{username}/total:
    get:
      tags:
        - Public
      summary: Get Collection Total
      description: ''
      operationId: GetPublicCollectionTotal
      parameters:
        - name: username
          in: path
          required: true
          schema:
            type: string
        - name: staged
          in: query
          required: true
          schema:
            type: boolean
      responses:
        '200':
          description: ''
  /public/{username}/lookup:
    post:
      tags:
        - Public
      summary: Lookup Card by Name or Json ID
      description: ''
      operationId: LookupCardByNameOrJsonId
      parameters:
        - name: username
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: ''
  /public/{username}/search:
    post:
      tags:
        - Public
      summary: Search Collection
      description: ''
      operationId: SearchCollection
      parameters:
        - name: username
          in: path
          required: true
          schema:
            type: string
        - name: quality
          in: query
          required: false
          schema: *ref_126
        - name: quantity
          in: query
          required: false
          schema: *ref_55
        - name: price
          in: query
          required: false
          schema: *ref_127
        - name: foil
          in: query
          required: false
          schema: *ref_128
        - name: tags
          in: query
          required: false
          schema: *ref_129
        - name: in_deck
          in: query
          required: false
          schema: *ref_130
          description: Cards in collection need to be linked/not linked to a deck. If passed an optional uuid param, it will return only cards that are present/not present in a specific deck
        - name: language
          in: query
          required: false
          schema: *ref_131
        - name: source
          in: query
          required: false
          schema: *ref_56
        - name: input_session
          in: query
          required: false
          schema: *ref_57
        - name: location
          in: query
          required: false
          schema: *ref_132
        - name: sort_by
          in: query
          required: false
          schema: *ref_58
        - name: confidence
          in: query
          required: false
          schema: *ref_59
        - name: json_id
          in: query
          required: false
          schema: *ref_1
        - name: card_list
          in: query
          required: false
          schema: *ref_2
        - name: order
          in: query
          required: false
          schema: *ref_3
        - name: group_by
          in: query
          required: false
          schema: *ref_4
        - name: query
          in: query
          required: false
          schema: *ref_5
        - name: starts_with
          in: query
          required: false
          schema: *ref_6
        - name: rules_text_filter
          in: query
          required: false
          schema: *ref_8
          description: Searches for the whole string, so it must wholly exist within the rules text.
        - name: oracle_text_filter
          in: query
          required: false
          schema: *ref_9
          description: Searches for the whole string, so it must wholly exist within the rules text.
        - name: per_page
          in: query
          required: false
          schema: *ref_10
        - name: page
          in: query
          required: false
          schema: *ref_11
        - name: colors
          in: query
          required: false
          schema: *ref_12
        - name: color_identity
          in: query
          required: false
          schema: *ref_13
        - name: types
          in: query
          required: false
          schema: *ref_14
          description: Accepted types can be retrieved from the `/types` route.
        - name: sub_types
          in: query
          required: false
          schema: *ref_15
        - name: super_types
          in: query
          required: false
          schema: *ref_16
        - name: set_names
          in: query
          required: false
          schema: *ref_17
        - name: artists
          in: query
          required: false
          schema: *ref_18
        - name: rarity
          in: query
          required: false
          schema: *ref_19
        - name: power
          in: query
          required: false
          schema: *ref_20
        - name: toughness
          in: query
          required: false
          schema: *ref_21
        - name: loyalty
          in: query
          required: false
          schema: *ref_22
          description: 'The loyalty filter will only match planeswalkers with loyalty that is not an integer (eg: ''1d4+1'', ''*'', or ''X'') when min is 0.'
        - name: mana_value (converted mana cost)
          in: query
          required: false
          schema: *ref_23
        - name: format_legality
          in: query
          required: false
          schema: *ref_24
        - name: set_types
          in: query
          required: false
          schema: *ref_25
        - name: is_reserved
          in: query
          required: false
          schema: *ref_26
        - name: border
          in: query
          required: false
          schema: *ref_27
      responses:
        '200':
          description: ''
  /public/{username}/list:
    post:
      tags:
        - Public
      summary: List Card ids  Json ID
      description: ''
      operationId: ListCardIdsJsonId
      parameters:
        - name: username
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: ''
  /public/{username}/tags:
    get:
      tags:
        - Public
      summary: Get All Tags for User
      description: ''
      operationId: GetAllTagsForUser1
      parameters:
        - name: username
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: ''
  /public/{username}/tags/lookup:
    post:
      tags:
        - Public
      summary: Lookup Tags for Card Users
      description: ''
      operationId: LookupTagsForCardUsers1
      parameters:
        - name: username
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: ''
  /decks:
    get:
      tags:
        - Deck
      summary: Get all Decks
      description: Get all the decks for a user.
      operationId: GetAllDecks
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Get all decks response
                required: &ref_444
                  - decks
                type: object
                properties: &ref_445
                  decks:
                    type: array
                    items:
                      type: object
                      example:
                        - id: 39
                          name: Untitled Deck
                          image: bdd41c871438ab4f8d2ed0c52cd4e6e4f1dca228
                          legality: null
                          uuid: 779813c1-d08e-4e60-a837-b8eae77141dc
                          created_at: '2017-04-06T06:40:58.601Z'
                          updated_at: '2017-04-06T06:41:10.632Z'
                          color_black: false
                          color_blue: false
                          color_red: false
                          color_green: false
                          color_white: false
                          color_colorless: false
                          price: 19736.206236
                          total: 80
                    description: ''
                example: &ref_446
                  decks:
                    - id: 39
                      name: Untitled Deck
                      image: bdd41c871438ab4f8d2ed0c52cd4e6e4f1dca228
                      legality: null
                      uuid: 779813c1-d08e-4e60-a837-b8eae77141dc
                      created_at: '2017-04-06T06:40:58.601Z'
                      updated_at: '2017-04-06T06:41:10.632Z'
                      color_black: false
                      color_blue: false
                      color_red: false
                      color_green: false
                      color_white: false
                      color_colorless: false
                      price: 19736.206236
                      total: 80
    post:
      tags:
        - Deck
      summary: Create a Deck
      description: Create a deck for the current user. Price reflects currency and pricing source preferences of the deck owner.
      operationId: CreateADeck
      parameters:
        - name: name
          in: query
          required: false
          schema:
            type: string
            items:
              type: string
            example: Modern Discard
          description: If no name is passed, deck name defaults to `Untitled Deck`. Name cannot be longer than `100` characters
        - name: image
          in: query
          required: false
          schema:
            type: string
            example: 7cfabcf0-438a-48f5-a4af-5b0f89f768a4
          description: json_id of the crop art image to use as the deck cover
        - name: legality
          in: query
          required: false
          schema:
            type: string
            example: modern
        - name: public
          in: query
          required: false
          schema:
            type: boolean
            example: false
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                title: Create a Deck response
                required: &ref_447
                  - deck
                type: object
                properties: &ref_448
                  deck:
                    type: object
                    example:
                      id: 54
                      name: Untitled Deck
                      image: null
                      legality: null
                      public: false
                      uuid: cfa99175-49f7-499f-b5aa-05762884f422
                      created_at: '2017-05-15T04:43:48.698Z'
                      updated_at: '2017-05-15T04:43:48.752Z'
                      color_black: false
                      color_blue: false
                      color_red: false
                      color_green: false
                      color_white: false
                      color_colorless: false
                      price: 0
                      total: 0
                      boards:
                        - id: 82
                          name: Main
                          created_at: '2017-05-15T04:43:48.737Z'
                        - id: 83
                          name: Side
                          created_at: '2017-05-15T04:43:48.750Z'
                      card_decks: []
                      cards: []
                      deck_tags: []
                example: &ref_449
                  deck:
                    id: 54
                    name: Untitled Deck
                    image: null
                    legality: null
                    public: false
                    uuid: cfa99175-49f7-499f-b5aa-05762884f422
                    created_at: '2017-05-15T04:43:48.698Z'
                    updated_at: '2017-05-15T04:43:48.752Z'
                    color_black: false
                    color_blue: false
                    color_red: false
                    color_green: false
                    color_white: false
                    color_colorless: false
                    price: 0
                    total: 0
                    boards:
                      - id: 82
                        name: Main
                        created_at: '2017-05-15T04:43:48.737Z'
                      - id: 83
                        name: Side
                        created_at: '2017-05-15T04:43:48.750Z'
                    card_decks: []
                    cards: []
                    deck_tags: []
  /decks/search:
    post:
      tags:
        - Deck
      summary: Search for Decks
      description: Search for decks owned by the currently logged in user.
      operationId: SearchForDecks
      parameters:
        - name: sort
          in: query
          required: false
          schema:
            type: string
            enum:
              - asc
              - desc
        - name: query
          in: query
          required: false
          schema:
            type: string
            example: discard
          description: Search for decks by name
        - name: per_page
          in: query
          required: false
          schema:
            type: number
            default: 100
        - name: page
          in: query
          required: false
          schema:
            type: number
            default: 1
        - name: legality
          in: query
          required: false
          schema:
            type: string
            example: modern
        - name: tags
          in: query
          required: false
          schema:
            type: object
            example: '{ with: [discard], without: [mono-green] }'
        - name: colors
          in: query
          required: false
          schema:
            type: object
            enum:
              - red: true/false
              - white: true/false
              - black: true/false
              - green: true/false
              - blue: true/false
              - colorless: true/false
            example: '{ red: true, white: false, black: true }'
          description: Filters by the color and boolean passed. Colors filters are optional
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Search for Decks response
                required: &ref_450
                  - decks
                type: object
                properties: &ref_451
                  decks:
                    type: array
                    items:
                      type: object
                      example:
                        - id: 39
                          name: Untitled Deck
                          image: bdd41c871438ab4f8d2ed0c52cd4e6e4f1dca228
                          legality: null
                          uuid: 779813c1-d08e-4e60-a837-b8eae77141dc
                          created_at: '2017-04-06T06:40:58.601Z'
                          updated_at: '2017-04-06T06:41:10.632Z'
                          color_black: false
                          color_blue: false
                          color_red: false
                          color_green: false
                          color_white: false
                          color_colorless: false
                          price: 19736.206236
                          total: 80
                    description: ''
                example: &ref_452
                  decks:
                    - id: 39
                      name: Untitled Deck
                      image: bdd41c871438ab4f8d2ed0c52cd4e6e4f1dca228
                      legality: null
                      uuid: 779813c1-d08e-4e60-a837-b8eae77141dc
                      created_at: '2017-04-06T06:40:58.601Z'
                      updated_at: '2017-04-06T06:41:10.632Z'
                      color_black: false
                      color_blue: false
                      color_red: false
                      color_green: false
                      color_white: false
                      color_colorless: false
                      price: 19736.206236
                      total: 80
  /decks/clear:
    delete:
      tags:
        - Deck
      summary: Delete all Decks
      description: Delete all decks owned by the user.
      operationId: DeleteAllDecks
      responses:
        '204':
          description: ''
  /decks/import:
    get:
      tags:
        - Deck
      summary: Import a Deck
      description: ''
      operationId: ImportADeck
      parameters: []
      responses:
        '200':
          description: ''
  /decks/{uuid}:
    get:
      tags:
        - Deck
      summary: Get a Deck
      description: Get a particular deck. If user is not logged in, it will try to get a public deck. Price reflects currency and pricing source preferences of the deck owner.
      operationId: GetADeck
      parameters:
        - name: uuid
          in: path
          required: true
          schema:
            type: string
            items:
              type: string
            example: 6da368f5-48f8-4a5c-8411-d00e1bb93bab
        - name: arrange_by
          in: query
          required: false
          schema:
            type: string
            default: card_type
            enum:
              - card_type
              - mana_cost
          description: Main grouping of cards in deck
        - name: group_by
          in: query
          required: false
          schema:
            type: string
            default: none
            enum:
              - printing
              - name
              - none
          description: Sub-grouping of cards within the `arrange_by` grouping
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Get a Deck response
                required: &ref_453
                  - deck
                type: object
                properties: &ref_454
                  deck:
                    type: object
                    example:
                      id: 54
                      name: Untitled Deck
                      image: null
                      legality: null
                      public: false
                      uuid: cfa99175-49f7-499f-b5aa-05762884f422
                      created_at: '2017-05-15T04:43:48.698Z'
                      updated_at: '2017-05-15T04:43:48.752Z'
                      color_black: false
                      color_blue: false
                      color_red: false
                      color_green: false
                      color_white: false
                      color_colorless: false
                      price: 0
                      total: 0
                      boards:
                        - id: 82
                          name: Main
                          created_at: '2017-05-15T04:43:48.737Z'
                        - id: 83
                          name: Side
                          created_at: '2017-05-15T04:43:48.750Z'
                      card_decks: []
                      cards: []
                      deck_tags: []
                      arrangement:
                        '1':
                          Creature:
                            - - 1
                            - - 2
                            - - 4
                              - 5
                        '2':
                          Artifact:
                            - - 6
                              - 7
                              - 8
                            - - 9
                              - 10
                              - 11
                example: &ref_455
                  deck:
                    id: 54
                    name: Untitled Deck
                    image: null
                    legality: null
                    public: false
                    uuid: cfa99175-49f7-499f-b5aa-05762884f422
                    created_at: '2017-05-15T04:43:48.698Z'
                    updated_at: '2017-05-15T04:43:48.752Z'
                    color_black: false
                    color_blue: false
                    color_red: false
                    color_green: false
                    color_white: false
                    color_colorless: false
                    price: 0
                    total: 0
                    boards:
                      - id: 82
                        name: Main
                        created_at: '2017-05-15T04:43:48.737Z'
                      - id: 83
                        name: Side
                        created_at: '2017-05-15T04:43:48.750Z'
                    card_decks: []
                    cards: []
                    deck_tags: []
                    arrangements:
                      0 drops:
                        - - 1
                          - 3
                          - 5
                        - - 2
                      1 drops:
                        - - 4
    delete:
      tags:
        - Deck
      summary: Delete a Deck
      description: Delete a Deck
      operationId: DeleteADeck
      parameters:
        - name: uuid
          in: path
          required: true
          schema:
            type: string
            items:
              type: string
            example: 6da368f5-48f8-4a5c-8411-d00e1bb93bab
      responses:
        '204':
          description: ''
    patch:
      tags:
        - Deck
      summary: Update a Deck
      description: Updates the attributes of the deck.
      operationId: UpdateADeck
      parameters:
        - name: uuid
          in: path
          required: true
          schema:
            type: string
            items:
              type: string
            example: 6da368f5-48f8-4a5c-8411-d00e1bb93bab
        - name: name
          in: query
          required: false
          schema:
            type: string
            items:
              type: string
            example: Modern Discard
          description: Name of the deck
        - name: image
          in: query
          required: false
          schema:
            type: string
            example: 7cfabcf0-438a-48f5-a4af-5b0f89f768a4
          description: json_id of the crop art image to use as the deck cover
        - name: legality
          in: query
          required: false
          schema:
            type: string
            example: modern
        - name: public
          in: query
          required: false
          schema:
            type: boolean
            example: false
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Update a Deck response
                required: &ref_456
                  - deck
                type: object
                properties: &ref_457
                  deck:
                    type: object
                    example:
                      id: 54
                      name: Untitled Deck
                      image: null
                      legality: null
                      public: false
                      uuid: cfa99175-49f7-499f-b5aa-05762884f422
                      created_at: '2017-05-15T04:43:48.698Z'
                      updated_at: '2017-05-15T04:43:48.752Z'
                      color_black: false
                      color_blue: false
                      color_red: false
                      color_green: false
                      color_white: false
                      color_colorless: false
                      price: 0
                      total: 0
                      boards:
                        - id: 82
                          name: Main
                          created_at: '2017-05-15T04:43:48.737Z'
                        - id: 83
                          name: Side
                          created_at: '2017-05-15T04:43:48.750Z'
                      card_decks: []
                      cards: []
                      deck_tags: []
                example: &ref_458
                  deck:
                    id: 54
                    name: Untitled Deck
                    image: null
                    legality: null
                    public: false
                    uuid: cfa99175-49f7-499f-b5aa-05762884f422
                    created_at: '2017-05-15T04:43:48.698Z'
                    updated_at: '2017-05-15T04:43:48.752Z'
                    color_black: false
                    color_blue: false
                    color_red: false
                    color_green: false
                    color_white: false
                    color_colorless: false
                    price: 0
                    total: 0
                    boards:
                      - id: 82
                        name: Main
                        created_at: '2017-05-15T04:43:48.737Z'
                      - id: 83
                        name: Side
                        created_at: '2017-05-15T04:43:48.750Z'
                    card_decks: []
                    cards: []
                    deck_tags: []
  /decks/{uuid}/validate:
    post:
      tags:
        - Deck
      summary: Validate a Deck
      description: Validate a deck's card are legal for the specified format
      operationId: ValidateDeck
      parameters:
        - name: uuid
          in: path
          required: true
          schema:
            type: string
            items:
              type: string
            example: 6da368f5-48f8-4a5c-8411-d00e1bb93bab
        - name: deck_format
          in: query
          required: false
          schema:
            type: string
            enum:
              - brawl
              - commander
              - duel
              - freeform
              - legacy
              - modern
              - oldschool
              - pauper
              - pioneer
              - premodern
              - standard
              - tiny leaders
              - vintage
          description: 'Note: All legalities will be returned when legality param is not sent'
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Validate a deck response
                required: &ref_459
                  - deck_legalities
                type: object
                properties: &ref_460
                  deck_legalities:
                    type: object
                    example:
                      commander:
                        legal: false
                        errors:
                          - You must have exactly one Commander Board
                          - You must have at least 100 cards in your Deck
  /decks/{uuid}/statistics:
    post:
      tags:
        - Deck
      summary: Get Deck Statistics
      description: Get statistics for a specific deck
      operationId: DeckStatistics
      parameters:
        - name: uuid
          in: path
          required: true
          schema:
            type: string
            items:
              type: string
            example: 6da368f5-48f8-4a5c-8411-d00e1bb93bab
        - name: type
          in: query
          required: false
          schema:
            type: string
            enum:
              - mana_curve
              - mana_ratios
              - card_type_ratios
          description: 'Note: All types will be returned when `type` param is not sent'
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Deck statistics response
                required: &ref_461
                  - deck_statistics
                type: object
                properties: &ref_462
                  deck_statistics:
                    type: object
                    example:
                      mana_curve:
                        '1': 8
                        '2': 12
                        '3': 11
                        '4': 5
                        '5': 3
                        '6': 0
                        '7': 1
                      mana_ratios:
                        white: 0.36
                        blue: 0.4
                        black: 0.22
                        red: 0.1
                        green: 0
                        colorless: 0.25
                      card_type_ratios:
                        Creature: 0.36
                        Enchantment: 0.4
                        Instant: 0.22
                        Sorcery: 0.1
                        Planswalker: 0
                        Artifact: 0.25
                        Land: 0.43
  /decks/{uuid}/turn_probabilities:
    post:
      tags:
        - Deck
      summary: Get Deck Turn Probabilities
      description: Get the probability of drawing a card in the first 3 turns based on it's type, mana cost, and/or name
      operationId: DeckTurnProbabilities
      parameters:
        - name: uuid
          in: path
          required: true
          schema:
            type: string
            items:
              type: string
            example: 6da368f5-48f8-4a5c-8411-d00e1bb93bab
        - name: group_by
          in: query
          required: false
          schema:
            type: string
            enum:
              - name
              - mana_cost
              - card_type
          description: 'Note: All groupings will be returned when `group_by` param is not sent'
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Deck turn probabilities response
                required: &ref_463
                  - turn_probabilities
                type: object
                properties: &ref_464
                  turn_probabilities:
                    type: object
                    example:
                      names:
                        Swamp:
                          - 0.28
                          - 0.32
                          - 0.33
                        Yawgmoth, Thran Physician:
                          - 0.11
                          - 0.12
                          - 0.14
                        Toshiro Umezawa:
                          - 0.2
                          - 0.22
                          - 0.24
                      mana_costs:
                        '1':
                          - 0.28
                          - 0.32
                          - 0.33
                        '2':
                          - 0.35
                          - 0.36
                          - 0.38
                        '3':
                          - 0.2
                          - 0.22
                          - 0.23
                        '4':
                          - 0.08
                          - 0.1
                          - 0.12
                      card_types:
                        Creature:
                          - 0.18
                          - 0.2
                          - 0.21
                        Land:
                          - 0.08
                          - 0.1
                          - 0.12
                        Enchantment:
                          - 0.33
                          - 0.34
                          - 0.35
  /decks/{uuid}/to_pdf:
    post:
      tags:
        - Deck
      summary: Export deck as PDF
      description: Creates a PDF and returns the raw data to the client
      operationId: DeckToPdf
      parameters:
        - name: uuid
          in: path
          required: true
          schema:
            type: string
            items:
              type: string
            example: 6da368f5-48f8-4a5c-8411-d00e1bb93bab
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Deck to pdf response
                required: &ref_465
                  - pdf
                type: object
                properties: &ref_466
                  pdf:
                    type: string
                    example: |-
                      %PDF-1.4
                      %ÿÿÿÿ
                      1 0 obj
                      << /Creator <feff0050007200610077006e>
                      /Producer <feff0050007200610077006e>
                      >>
                      endobj
                      2 0 obj
                      << /Type /Catalog
                      /Pages 3 0 R
                      >>
                      endobj
                      3 0 obj
                      << /Type /Pages
                      /Count 1
                      /Kids [5 0 R]
                      >>
                      endobj
                      4 0 obj
                      << /Length 11278
                      >>
                      stream
                      q

                      q
                      74.75 0.0 0.0 72.8 36.0 693.2 cm
                      /I1 Do
                      Q
                      1 w
                      131.0...
  /decks/{uuid}/clone:
    post:
      tags:
        - Deck
      summary: Clone a Deck
      description: Clones the specified deck one to one, except for any tags. It can only clone public decks or those owned by the user. Price reflects currency and pricing source preferences of the deck owner.
      operationId: CloneADeck
      parameters:
        - name: uuid
          in: path
          required: true
          schema:
            type: string
            items:
              type: string
            example: 6da368f5-48f8-4a5c-8411-d00e1bb93bab
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                title: Clone a Deck response
                required: &ref_467
                  - deck
                type: object
                properties: &ref_468
                  deck:
                    type: object
                    example:
                      id: 54
                      name: Untitled Deck
                      image: null
                      legality: null
                      public: false
                      uuid: cfa99175-49f7-499f-b5aa-05762884f422
                      created_at: '2017-05-15T04:43:48.698Z'
                      updated_at: '2017-05-15T04:43:48.752Z'
                      color_black: false
                      color_blue: false
                      color_red: false
                      color_green: false
                      color_white: false
                      color_colorless: false
                      price: 0
                      total: 0
                      boards:
                        - id: 82
                          name: Main
                          created_at: '2017-05-15T04:43:48.737Z'
                        - id: 83
                          name: Side
                          created_at: '2017-05-15T04:43:48.750Z'
                      card_decks: []
                      cards: []
                      deck_tags: []
                example: &ref_469
                  deck:
                    id: 54
                    name: Untitled Deck
                    image: null
                    legality: null
                    public: false
                    uuid: cfa99175-49f7-499f-b5aa-05762884f422
                    created_at: '2017-05-15T04:43:48.698Z'
                    updated_at: '2017-05-15T04:43:48.752Z'
                    color_black: false
                    color_blue: false
                    color_red: false
                    color_green: false
                    color_white: false
                    color_colorless: false
                    price: 0
                    total: 0
                    boards:
                      - id: 82
                        name: Main
                        created_at: '2017-05-15T04:43:48.737Z'
                      - id: 83
                        name: Side
                        created_at: '2017-05-15T04:43:48.750Z'
                    card_decks: []
                    cards: []
                    deck_tags: []
  /decks/{uuid}/export:
    get:
      tags:
        - Deck
      summary: Export a Deck
      description: ''
      operationId: ExportADeck
      parameters:
        - name: uuid
          in: path
          required: true
          schema:
            type: string
            items:
              type: string
            example: 6da368f5-48f8-4a5c-8411-d00e1bb93bab
      responses:
        '200':
          description: ''
  /decks/{uuid}/deck_boards:
    post:
      tags:
        - Deck Board
      summary: Create Deck Board
      description: Adds a board to the specified deck. Name of the board must be unique per deck
      operationId: CreateDeckBoard
      parameters:
        - name: uuid
          in: path
          required: true
          schema:
            type: string
            items:
              type: string
            example: 6da368f5-48f8-4a5c-8411-d00e1bb93bab
      requestBody:
        description: ''
        content:
          application/json:
            schema:
              title: Create Deck Board request
              required: &ref_470
                - name
              type: object
              properties: &ref_471
                name:
                  type: string
                  maximum: 100
                  example: Untitled
              example: &ref_472
                name: Untitled
        required: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Create Deck Board response
                required: &ref_473
                  - deck_board
                type: object
                properties: &ref_474
                  deck_board:
                    type: object
                    example:
                      id: 84
                      name: Untitled
                      created_at: '2017-05-15T05:01:41.647Z'
                example: &ref_475
                  deck_board:
                    id: 84
                    name: Untitled
                    created_at: '2017-05-15T05:01:41.647Z'
  /decks/{uuid}/deck_boards/:id:
    delete:
      tags:
        - Deck Board
      summary: Delete Deck Board
      description: Deletes the deck board and any cards which are on it.
      operationId: DeleteDeckBoard
      parameters:
        - name: uuid
          in: path
          required: true
          schema:
            type: string
            items:
              type: string
              example: 7cfabcf0-438a-48f5-a4af-5b0f89f768a4
      responses:
        '204':
          description: ''
        '400':
          description: Main boards cannot be deleted
    patch:
      tags:
        - Deck Board
      summary: Update Deck Board
      description: Updates the attributes of the deck
      operationId: UpdateDeckBoard
      parameters:
        - name: uuid
          in: path
          required: true
          schema:
            type: string
            items:
              type: string
            example: 6da368f5-48f8-4a5c-8411-d00e1bb93bab
      requestBody:
        description: ''
        content:
          application/json:
            schema:
              title: Update Deck Board request
              required: &ref_476
                - name
              type: object
              properties: &ref_477
                name:
                  type: string
                  example: Untitled2
              example: &ref_478
                name: Untitled2
        required: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Update Deck Board response
                required: &ref_479
                  - deck_board
                type: object
                properties: &ref_480
                  deck_board:
                    type: object
                    example:
                      id: 84
                      name: Untitled2
                      created_at: '2017-05-15T05:01:41.647Z'
                example: &ref_481
                  deck_board:
                    id: 84
                    name: Untitled2
                    created_at: '2017-05-15T05:01:41.647Z'
  /decks/{uuid}/deck_boards/:id/add_cards:
    post:
      tags:
        - Deck Board
      summary: Add Multiple Cards To A Deck Board
      description: Add multiple cards to a deck board
      parameters:
        - name: uuid
          in: path
          required: true
          schema:
            type: string
            items:
              type: string
            example: 6da368f5-48f8-4a5c-8411-d00e1bb93bab
        - name: cards
          in: query
          required: true
          schema:
            type: object
            example: '{''246c605d725aa1cc3dc83b8d5ce1cb11'': 2, ''66c864c14044c27d566eaacc18a05112'': 3}'
          description: key = card_json_id, value = quantity of deck cards to create
      operationId: AddMultipleCardsToDeck
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Add Multiple Cards To Deck response
                required: &ref_482
                  - cards
                type: object
                properties: &ref_483
                  cards:
                    type: object
                    example:
                      - id: 5
                        card_id: 10
                        deck_board_id: 6
                        card_user_id: null
                        created_at: ''
                      - id: 6
                        card_id: 10
                        deck_board_id: 6
                        card_user_id: null
                        created_at: ''
  /decks/{uuid}/card_decks:
    post:
      tags:
        - Card Deck
      summary: Add Card To Deck
      description: |-
        Add a card to the deck
        + deck_board_id (integer) - id of the deck board
        + json_id (string) - json_id of the card to add
      operationId: AddCardToDeck
      parameters:
        - name: uuid
          in: path
          required: true
          schema:
            type: string
            items:
              type: string
            example: 6da368f5-48f8-4a5c-8411-d00e1bb93bab
      deprecated: true
      requestBody:
        description: ''
        content:
          application/json:
            schema:
              title: Add Card To Deck request
              required: &ref_484
                - deck_board_id
                - json_id
              type: object
              properties: &ref_485
                deck_board_id:
                  type: integer
                  format: int32
                  example: 5
                json_id:
                  type: string
                  example: jsonids
              example: &ref_486
                deck_board_id: 5
                json_id: jsonids
        required: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Add Card To Deck response
                required: &ref_487
                  - card_deck
                  - card
                type: object
                properties: &ref_488
                  card_deck:
                    type: object
                    example:
                      id: 5
                      card_id: 10
                      deck_board_id: 6
                      card_user_id: null
                      created_at: ''
                  card:
                    type: object
                    example:
                      card: object
                example: &ref_489
                  card_deck:
                    id: 5
                    card_id: 10
                    deck_board_id: 6
                    card_user_id: null
                    created_at: ''
                  card:
                    card: object
  /decks/{uuid}/card_decks/selection:
    post:
      tags:
        - Card Deck
      summary: Add Selection of Card Users to Deck
      description: |-
        Add a selection of card_users to a deck and auto link them
        + card_ids (array) - array of card_user_ids
      operationId: AddSelectionOfCardUsersToDeck
      parameters:
        - name: uuid
          in: path
          required: true
          schema:
            type: string
            items:
              type: string
            example: 6da368f5-48f8-4a5c-8411-d00e1bb93bab
      requestBody:
        description: ''
        content:
          application/json:
            schema:
              title: Add Selection of Card Users to Deck request
              required: &ref_490
                - card_ids
              type: object
              properties: &ref_491
                card_ids:
                  type: array
                  items:
                    type: integer
                    format: int32
                    example:
                      - 1
                      - 2
                      - 3
                      - 5
                  description: ''
              example: &ref_492
                card_ids:
                  - 1
                  - 2
                  - 3
                  - 5
        required: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Add Selection of Card Users to Deck response
                required: &ref_493
                  - card_deck
                  - card
                type: object
                properties: &ref_494
                  card_deck:
                    type: object
                    example:
                      id: 5
                      card_id: 10
                      deck_board_id: 6
                      card_user_id: null
                      created_at: ''
                  card:
                    type: object
                    example:
                      card: object
                example: &ref_495
                  card_deck:
                    id: 5
                    card_id: 10
                    deck_board_id: 6
                    card_user_id: null
                    created_at: ''
                  card:
                    card: object
  /decks/{uuid}/card_decks/lands:
    post:
      tags:
        - Card Deck
      summary: Add lands to deck
      description: |-
        Add the latest land of a selected color to a deck
        + color (string) - color of the land required
      operationId: AddLandsToDeck
      parameters:
        - name: uuid
          in: path
          required: true
          schema:
            type: string
            items:
              type: string
            example: 6da368f5-48f8-4a5c-8411-d00e1bb93bab
      requestBody:
        description: ''
        content:
          application/json:
            schema:
              title: Add lands to deck request
              required: &ref_496
                - colors
              type: object
              properties: &ref_497
                colors:
                  type: string
                  example: wastes
              example: &ref_498
                colors: wastes
        required: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Add lands to deck response
                required: &ref_499
                  - card_deck
                  - card
                type: object
                properties: &ref_500
                  card_deck:
                    type: object
                    example:
                      id: 5
                      card_id: 10
                      deck_board_id: 6
                      card_user_id: null
                      created_at: ''
                  card:
                    type: object
                    example:
                      card: object
                example: &ref_501
                  card_deck:
                    id: 5
                    card_id: 10
                    deck_board_id: 6
                    card_user_id: null
                    created_at: ''
                  card:
                    card: object
  /decks/{uuid}/card_decks/:id:
    delete:
      tags:
        - Card Deck
      summary: Remove Card from Deck
      description: Remove card from deck
      operationId: RemoveCardFromDeck
      parameters:
        - name: uuid
          in: path
          required: true
          schema:
            type: string
            items:
              type: string
            example: 6da368f5-48f8-4a5c-8411-d00e1bb93bab
      responses:
        '204':
          description: ''
    patch:
      tags:
        - Card Deck
      summary: Update Card Deck
      description: |-
        + deck_board_id (integer) - id of the deck board
        + json_id (string) - json_id of the card to add
      operationId: UpdateCardDeck
      parameters:
        - name: uuid
          in: path
          required: true
          schema:
            type: string
            items:
              type: string
            example: 6da368f5-48f8-4a5c-8411-d00e1bb93bab
      requestBody:
        description: ''
        content:
          application/json:
            schema:
              title: Update Card Deck request
              required: &ref_502
                - deck_board_id
                - json_id
              type: object
              properties: &ref_503
                deck_board_id:
                  type: integer
                  format: int32
                  example: 5
                json_id:
                  type: string
                  example: jsonidsfkdslkdskldfs
              example: &ref_504
                deck_board_id: 5
                json_id: jsonidsfkdslkdskldfs
        required: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Update Card Deck response
                required: &ref_505
                  - card_deck
                  - card
                type: object
                properties: &ref_506
                  card_deck:
                    type: object
                    example:
                      id: 5
                      card_id: 10
                      deck_board_id: 6
                      card_user_id: null
                      created_at: ''
                  card:
                    type: object
                    example:
                      card: object
                example: &ref_507
                  card_deck:
                    id: 5
                    card_id: 10
                    deck_board_id: 6
                    card_user_id: null
                    created_at: ''
                  card:
                    card: object
  /decks/{uuid}/card_decks/:id/link:
    patch:
      tags:
        - Card Deck
      summary: Link Card User to Card Deck
      description: ''
      operationId: LinkCardUserToCardDeck
      parameters:
        - name: uuid
          in: path
          required: true
          schema:
            type: string
            items:
              type: string
            example: 6da368f5-48f8-4a5c-8411-d00e1bb93bab
      responses:
        '200':
          description: ''
  /decks/{uuid}/card_decks/:id/unlink:
    delete: &ref_133
      tags:
        - Card Deck
      summary: Unlink Card User from Card Deck
      description: ''
      operationId: UnlinkCardUserFromCardDeck
      parameters:
        - name: uuid
          in: path
          required: true
          schema:
            type: string
            items:
              type: string
            example: 6da368f5-48f8-4a5c-8411-d00e1bb93bab
      responses:
        '200':
          description: ''
  /decks/{uuid}/card_decks/remove_all:
    delete:
      tags:
        - Card Deck
      summary: Remove all Cards from Deck
      description: Remove all cards in deck
      operationId: RemoveAllCardsFromDeck
      parameters:
        - name: uuid
          in: path
          required: true
          schema:
            type: string
            items:
              type: string
            example: 6da368f5-48f8-4a5c-8411-d00e1bb93bab
      responses:
        '204':
          description: ''
  /decks/{uuid}/card_decks/linkable:
    delete: *ref_133
  /decks/{uuid}/deck_tags:
    post:
      tags:
        - Deck Tag
      summary: Add tag to deck
      description: Adds a tag to a specified deck, If the tag does not exist, it is created.
      operationId: AddTagToDeck
      parameters:
        - name: uuid
          in: path
          required: true
          schema:
            type: string
            items:
              type: string
            example: 6da368f5-48f8-4a5c-8411-d00e1bb93bab
      requestBody:
        description: ''
        content:
          application/json:
            schema:
              title: Add tag to deck request
              required: &ref_134
                - name
              type: object
              properties: &ref_135
                name:
                  type: string
                  maximum: 64
                  example: cardcastle
        required: true
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Add tag to deck response
                required: &ref_508
                  - name
                type: object
                properties: &ref_509
                  name:
                    type: string
                    example: test
                example: &ref_510
                  name: test
    delete:
      tags:
        - Deck Tag
      summary: Remove tag from deck
      description: ''
      operationId: RemoveTagFromDeck
      parameters:
        - name: uuid
          in: path
          required: true
          schema:
            type: string
            items:
              type: string
            example: 6da368f5-48f8-4a5c-8411-d00e1bb93bab
      requestBody:
        description: ''
        content:
          application/json:
            schema:
              title: Add tag to deck request
              required: *ref_134
              type: object
              properties: *ref_135
        required: true
      responses:
        '204':
          description: ''
  /deck_tags:
    get:
      tags:
        - Deck Tag
      summary: Get All Tags
      description: |-
        Get the 10 tags used by all decks
        + query (string, optional) - partial query string to match against all tags
      operationId: GetAllTags
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Get All Tags response
                required: &ref_511
                  - deck_tags
                type: object
                properties: &ref_512
                  deck_tags:
                    type: array
                    items:
                      type: object
                      example:
                        - name: test
                          created_at: '2017-04-30T23:59:36.046Z'
                          updated_at: '2017-04-30T23:59:36.046Z'
                          id: 1
                        - name: test2
                          created_at: '2017-05-01T00:48:42.077Z'
                          updated_at: '2017-05-01T00:48:42.077Z'
                          id: 2
                    description: ''
                example: &ref_513
                  deck_tags:
                    - name: test
                      created_at: '2017-04-30T23:59:36.046Z'
                      updated_at: '2017-04-30T23:59:36.046Z'
                      id: 1
                    - name: test2
                      created_at: '2017-05-01T00:48:42.077Z'
                      updated_at: '2017-05-01T00:48:42.077Z'
                      id: 2
  /card_bots:
    get:
      tags:
        - CardBot
      summary: Get All CardBots
      description: Get All CardBots
      operationId: GetCardBotIndex
      security:
        - OAuth2:
            - admin
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: List of Devices
                type: array
                example: &ref_514
                  - name: Fredricka Schuster
                    uuid: ed0ba4bc-fbc1-4716-83eb-fd1f8d29cab7
                    credentials:
                      client_id: ae404262dae9ee4792d37ad02aa89d8ad0184d04bed7d10ad8d7ba1704186b15e
                      client_secret: 05d9c88d7033da83c9dc3730c0114290d42cbc2974e12b22583a3e988a6ed2c6e
                      redirect_uri: https://localhost
                  - name: Zane Volkman
                    uuid: 7114b47e-293f-4e8f-a55a-585c0fa39730
                    credentials:
                      client_id: 49209084a8686a11a8ddbc5038726409784d85ee0a3de39bbca474038e98e57c2
                      client_secret: b07a0eea760e2541648a9bb805e43959384e53d4c3aa032470816e3031ba82a9e
                      redirect_uri: https://localhost
    post:
      tags:
        - CardBot
      summary: Create CardBot
      description: Create a CardBot
      operationId: CreateCardBot
      parameters:
        - name: name
          in: query
          required: true
          schema:
            type: string
            example: new cardbot
      security:
        - OAuth2:
            - admin
      requestBody:
        content:
          application/json:
            schema:
              title: Card Bot Create
              required: &ref_515
                - name
              type: string
              example: Joe's cardbot
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Card Bot Create Response
                type: object
                example: &ref_137
                  - name: Fredricka Schuster
                    uuid: ed0ba4bc-fbc1-4716-83eb-fd1f8d29cab7
                    credentials: null
  /card_bots/pair:
    post:
      tags:
        - CardBot
      summary: Pair CardBot
      description: Send CardBot Pairing Request
      operationId: PairCardBot
      security:
        - OAuth2:
            - bot
      requestBody:
        content:
          application/json:
            schema:
              title: Request to Pair Card Bot
              required: &ref_516
                - user_id
                - mfa_token
              type: object
              properties: &ref_517
                user_id:
                  type: number
                  example: 5
                mfa_token:
                  type: string
                  example: asdAD3GFNSCEGDs
              example: &ref_518
                user_id: 5
                mfa_token: asRGSDFesadfDAs
      responses:
        '201':
          description: ''
  /card_bots/command:
    get:
      tags:
        - CardBot
      summary: Get Message for CardBot
      description: Get Message for CardBot
      security:
        - OAuth2:
            - bot
      operationId: GetCardBotMessage
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: CardBot Message
                type: object
                example: &ref_136
                  message: Message
                  user_id: 5
    post:
      tags:
        - CardBot
      summary: Send Message to CardBot
      description: Send Message to CardBot
      operationId: SendCardBotMessage
      security:
        - OAuth2:
            - user
      requestBody:
        content:
          application/json:
            schema:
              title: CardBot Message
              type: object
              example: *ref_136
      responses:
        '201':
          description: ''
        '404':
          description: ''
  /card_bots/report:
    get:
      tags:
        - CardBot
      summary: Get Message From CardBot
      description: Get Message From CardBot
      operationId: ReceiveCardBotMessage
      security:
        - OAuth2:
            - user
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: CardBot Message
                type: object
                example: *ref_136
        '204':
          description: ''
    post:
      tags:
        - CardBot
      summary: Send Message To Backend
      description: Send Message to Backend
      operationId: SendMessageToBackend
      security:
        - OAuth2:
            - bot
      requestBody:
        content:
          application/json:
            schema:
              title: CardBot Message
              type: object
              example: *ref_136
      responses:
        '201':
          description: ''
  /card_bots/status:
    post:
      tags:
        - CardBot
      summary: Update CardBot Status
      description: Update CardBot Status Information
      operationId: GetCardBotStatus
      parameters:
        - name: software
          in: query
          required: false
          schema:
            type: string
            enum:
              - core
              - server
              - firmware
            example: '{ core: ''1.2.2'', server: ''1.0.1'', firmware: ''1.3.0'' }'
        - name: odometry
          in: query
          required: false
          schema:
            type: string
            enum:
              - left_steps
              - middle_steps
              - right_steps
              - gantry_steps
              - left_jams
              - middle_jams
              - right_jams
              - accept_cycles
              - reject_cycles
            example: '{ left_steps: 1000, middle_steps: 2000, right_steps: 1500, gantry_steps: 1000, left_jams: { recovered: 500, unrecoverable: 100 }, middle_jams: { recovered: 500, unrecoverable: 100 }, right_jams: { recovered: 500, unrecoverable: 100 }, accept_cycles: { cards_processed: 900 }, reject_cycles: { cards_processed: 120 } }'
        - name: system
          in: query
          required: false
          schema:
            type: string
            enum:
              - hardware_version
              - base_image_file
              - pickup_head_revision
            example: '{ hardware_version: ''1.2'' , base_image_file: ''unknown'', pickup_head_revision: ''unknown'' }'
      security:
        - OAuth2:
            - bot
      responses:
        '204':
          description: ''
  /card_bots/{device_id}:
    get:
      tags:
        - CardBot
      summary: Get Device
      description: Get Device
      operationId: GetDevice
      parameters:
        - name: device_id
          in: path
          required: true
          schema:
            type: string
            example: 7cfabcf0-438a-48f5-a4af-5b0f89f768a4
      security:
        - OAuth2:
            - admin
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Card Bot Create Response
                type: object
                example: *ref_137
    patch:
      tags:
        - CardBot
      summary: Update CardBot disabled status
      description: Update CardBot disabled status
      operationId: UpdateCardBotDisabled
      parameters:
        - name: device_id
          in: path
          required: true
          schema:
            type: string
            example: 7cfabcf0-438a-48f5-a4af-5b0f89f768a4
        - name: disabled
          in: query
          required: true
          schema:
            type: boolean
      security:
        - OAuth2:
            - admin
      responses:
        '204':
          description: ''
  /card_bots/{device_id}/link:
    patch:
      tags:
        - CardBot
      summary: Link User to CardBots
      description: Link User to CardBot
      operationId: LinkUserToCardBot
      parameters:
        - name: device_id
          in: path
          required: true
          schema:
            type: string
            example: 7cfabcf0-438a-48f5-a4af-5b0f89f768a4
      security:
        - OAuth2:
            - admin
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                user_id:
                  type: number
      responses:
        '204':
          description: ''
        '404':
          description: ''
  /card_bots/{device_id}/unlink:
    patch:
      tags:
        - CardBot
      summary: Unlink User to CardBots
      description: Unlink User to CardBots
      operationId: UnlinkUserToCardBot
      parameters:
        - name: device_id
          in: path
          required: true
          schema:
            type: string
            example: 7cfabcf0-438a-48f5-a4af-5b0f89f768a4
      security:
        - OAuth2:
            - admin
      responses:
        '204':
          description: ''
        '404':
          description: ''
  /banners/latest:
    get:
      tags:
        - Banners
      summary: Get Banner Contents
      description: ''
      operationId: GetBanners
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Banner Response
                type: object
                required: &ref_519
                  - id
                  - message
                  - button
                  - url
                  - token
                  - published
                  - created_at
                  - updated_at
                properties: &ref_520
                  id:
                    type: number
                    example: 1
                  message:
                    type: string
                    example: lol
                  button:
                    type: string
                    example: button
                  url:
                    type: string
                    example: http://google.com
                  token:
                    type: string
                    example: oPkWVwflO13n0fgus4hrQA
                  published:
                    type: string
                    example: true
                  created_at:
                    type: string
                    example: Mon, 27 Jun 2016 04:38:38 UTC +00:00
                  updated_at:
                    type: string
                    example: Mon, 27 Jun 2016 04:38:s55 UTC +00:00
                example: &ref_521
                  id: 1
                  message: lol
                  button: button
                  url: http://google.com
                  token: oPkWVwflO13n0fgus4hrQA
                  published: true
                  created_at: Mon, 27 Jun 2016 04:38:38 UTC +00:00
                  updated_at: Mon, 27 Jun 2016 04:38:55 UTC +00:00
  /import_export/manifest:
    get:
      tags:
        - Import Export
      summary: Show Import/Export Service Manifest
      description: Show full range and availability of import and export formats. Availability is based on user's roles and active subscriptions
      operationId: GetImportExportServices
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Import/Export Service Manifest
                type: object
                properties: &ref_524
                  services:
                    type: array
                    items:
                      title: Data service for importing and exporting cards
                      type: object
                      properties: &ref_523
                        type:
                          type: string
                          example: cardcastle
                        import:
                          title: Action information for data service
                          type: object
                          properties: &ref_138
                            formats:
                              type: array
                              items:
                                title: Format information for import/export action
                                type: object
                                properties: &ref_522
                                  type:
                                    type: string
                                    example: csv
                                  instructions:
                                    type: array
                                    items:
                                      type: string
                                    example:
                                      - 'Header Format: Card Name, Set Name, Collector Number'
                                      - Make sure to pat your CSV on the head before uploading
                            available:
                              type: boolean
                            games:
                              type: array
                              items:
                                type: string
                              example:
                                - poke
                                - mtg
                        export:
                          title: Action information for data service
                          type: object
                          properties: *ref_138
                        export_staged:
                          title: Action information for data service
                          type: object
                          properties: *ref_138
        '400':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
  /import_export/export:
    get:
      tags:
        - Import Export
      summary: Export collection
      description: Queues a CSV export job. A link to the resulting CSV is emailed to the user
      operationId: GetImportExportCSVExport
      parameters:
        - name: type
          in: query
          required: false
          schema:
            type: string
            default: cardcastle
            enum:
              - cardcastle
              - simple
              - deckbox
              - echomtg
              - binderpos
              - crystalcommerce
              - tcgplayer_kiosk
              - tcgplayer_online
              - tcg_vault
        - name: kind
          in: query
          required: false
          schema:
            type: string
            default: csv
            enum:
              - csv
              - txt
            description: Only `echomtg` support txt
        - name: staged
          in: query
          required: false
          schema:
            type: boolean
            default: false
          description: when true, exports all staged card users accross all sessions. Otherwise exports all owned card users of the selected game. If no game is selected, only Magic cards are exported
        - name: source
          in: query
          required: false
          schema:
            type: string
            enum:
              - cardbot
              - app
              - manual
              - import
              - unknown
        - name: input_session
          in: query
          required: false
          schema:
            type: string
            example: 7cfabcf0-438a-48f5-a4af-5b0f89f768a4
          description: the input session must be finalized, otherwise the export will fail
        - name: game
          in: query
          required: false
          schema:
            type: string
            default: mtg
            enum:
              - mtg
              - poke
          description: Pokemon only supports `tcgplayer_kiosk`, `tcgplayer_online`, and `binderpos` types for export
      responses:
        '202':
          description: ''
        '400':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
  /subscription_prices:
    get:
      tags:
        - Subscription Prices
      summary: Get Subscription Prices
      description: Get the details of subscription prices available.
      operationId: GetSubscriptionPrices
      security:
        - OAuth2:
            - admin
        - cookieAuth: []
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Get All Subscription Prices response
                required: &ref_525
                  - subscription_prices
                properties: &ref_526
                  subscription_prices:
                    type: object
                    example:
                      - name: guildmage-annual
                        price: 9000
                        currency: USD
                        cycle: annual
                        downgrade: guildmage-monthly
                        upgrade: null
                        deprecated: false
                      - name: guildmage-monthly
                        price: 900
                        currency: USD
                        cycle: monthly
                        downgrade: null
                        upgrade: null
                        deprecated: false
                    description: ''
    post:
      tags:
        - Subscription Prices
      summary: Create Subscription Price
      description: Create a New Subscription Price
      operationId: PostSubscriptionPrice
      parameters:
        - name: name
          in: query
          required: true
          schema:
            type: string
        - name: cycle
          in: query
          required: true
          schema:
            type: string
            enum:
              - annual
              - monthly
        - name: price
          in: query
          required: true
          schema:
            type: integer
        - name: currency
          in: query
          required: false
          schema:
            type: string
            default: USD
        - name: downgrade
          in: query
          required: false
          schema:
            type: string
            description: name of downgrade subscription plan
        - name: upgrade
          in: query
          required: false
          schema:
            type: string
            description: name of upgrade subscription plan
      security:
        - OAuth2:
            - admin
        - cookieAuth: []
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Create New Subscription Price response
                required: &ref_139
                  - subscription_price
                properties: &ref_140
                  subscription_price:
                    type: object
                    example:
                      name: guildmage-annual
                      price: 9000
                      currency: USD
                      cycle: annual
                      downgrade: guildmage-monthly
                      upgrade: null
                      deprecated: false
                    description: ''
  /subscription_prices/{name}:
    patch:
      tags:
        - Subscription Prices
      summary: Update Subscription Price
      description: Update Subscription Price
      operationId: UpdateSubscriptionPrice
      parameters:
        - name: name
          in: path
          required: true
          schema:
            type: string
        - name: deprecated
          in: query
          required: false
          schema:
            type: boolean
        - name: downgrade
          in: query
          required: false
          schema:
            type: string
            description: name of downgrade subscription plan
        - name: upgrade
          in: query
          required: false
          schema:
            type: string
            description: name of upgrade subscription plan
      security:
        - OAuth2:
            - admin
        - cookieAuth: []
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Create New Subscription Price response
                required: *ref_139
                properties: *ref_140
    delete:
      tags:
        - Subscription Prices
      summary: Delete Subscription Price
      description: Delete Subscription Price
      operationId: DeleteSubscriptionPrices
      parameters:
        - name: name
          in: path
          required: true
          schema:
            type: string
      security:
        - OAuth2:
            - admin
        - cookieAuth: []
      responses:
        '204':
          description: No Content
  /provider_plans:
    get:
      tags:
        - Provider Plans
      summary: Get Provider Plans
      description: Get All Provider Plans Available
      operationId: GetProviderPlans
      security:
        - OAuth2:
            - admin
        - cookieAuth: []
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Get All Provider Plans response
                required: &ref_527
                  - provider_plans
                properties: &ref_528
                  provider_plans:
                    type: object
                    example:
                      - id: 1
                        provider: stripe
                        plan_id: p_372dc1a7c986d69e79785b29
                        subscription_price: merchant-annual
                      - id: 2
                        provider: stripe
                        plan_id: p_616a765d0a698dc7069a64d9
                        subscription_price: merchant-monthly
                    description: ''
    post:
      tags:
        - Provider Plans
      summary: Create Provider Plan
      description: Create a New Provider Plan
      operationId: PostProviderPlan
      parameters:
        - name: provider
          in: query
          required: true
          schema:
            type: string
            enum:
              - stripe
              - paypal
              - paypal-v2
        - name: plan_id
          in: query
          required: true
          schema:
            type: string
            example: P-372dc1a7c986d69e79785b29
        - name: subscription_price
          in: query
          required: true
          schema:
            type: string
            example: merchant-annual-2022
          description: name of associated subscription price
      security:
        - OAuth2:
            - admin
        - cookieAuth: []
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Create New Provider Plan response
                required: &ref_141
                  - provider_plan
                properties: &ref_142
                  provider_plan:
                    type: object
                    example:
                      id: 2
                      provider: stripe
                      plan_id: p_616a765d0a698dc7069a64d9
                      subscription_price: merchant-monthly
                    description: ''
  /provider_plans/{id}:
    patch:
      tags:
        - Provider Plans
      summary: Update Provider Plan
      description: Update Provider Plan
      operationId: UpdateProviderPlan
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
        - name: provider
          in: query
          required: true
          schema:
            type: string
            enum:
              - stripe
              - paypal
              - paypal-v2
        - name: plan_id
          in: query
          required: true
          schema:
            type: string
            example: P-372dc1a7c986d69e79785b29
        - name: subscription_price
          in: query
          required: true
          schema:
            type: string
            example: merchant-annual-2022
          description: name of associated subscription price
      security:
        - OAuth2:
            - admin
        - cookieAuth: []
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Create New Provider Plan response
                required: *ref_141
                properties: *ref_142
    delete:
      tags:
        - Provider Plans
      summary: Delete Provider Plan
      description: Delete Provider Plan
      operationId: DeleteProviderPlan
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      security:
        - OAuth2:
            - admin
        - cookieAuth: []
      responses:
        '204':
          description: No Content
  /coupons:
    post:
      tags:
        - Coupon
      summary: Batch Create Coupons
      security:
        - OAuth2:
            - admin
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                prefix:
                  type: string
                name:
                  type: string
                count:
                  type: number
                duration:
                  type: number
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: array
  /price:
    post:
      tags:
        - Price
      summary: Get the Price for a List of Cards
      description: |-
        Get the prices for a list of cards for a selected denomination. Authentication is required.
        All prices returned are in cents for the denomination selected.
        + current_code (string, optional) - selection of currency to get the prices in, any ISO 3 letter currency is valid. If unspecified the users preferred currency will be used
        + json_ids (object) - array of the json_ids representing the cards the prices are being requested for
      operationId: GetThePriceForAListOfCards
      security:
        - OAuth2:
            - user
        - cookieAuth: []
      requestBody:
        description: ''
        content:
          application/json:
            schema:
              title: Get the Price for a List of Cards request
              required: &ref_529
                - currency_code
                - json_ids
              type: object
              properties: &ref_530
                currency_code:
                  type: string
                  example: AUD
                json_ids:
                  type: array
                  items:
                    type: string
                    example:
                      - id1
                      - id2
                      - id3
                  description: ''
        required: true
      responses:
        '200':
          description: ''
          content:
            text/plain:
              schema:
                type: object
        '400':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
  /events:
    get:
      tags:
        - Events
      summary: Get All Events
      description: Get all the events for the current logged in user
      operationId: GetAllEvents
      security:
        - OAuth2:
            - user
        - cookieAuth: []
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Get All Events response
                required: &ref_531
                  - events
                type: object
                properties: &ref_532
                  events:
                    type: array
                    items:
                      type: object
                      example:
                        - id: 11
                          action: ADD
                          event_type: CARD
                          items:
                            - type: CardUser
                              card_name: Bituminous Blast
                              set_name: Planechase Anthology
                              set_code: PCA
                              rarity: Uncommon
                            - type: Tag
                              name: Foiled
                          created_at: '2017-01-24T03:21:23.947Z'
                    description: ''
                example: &ref_533
                  events:
                    - id: 11
                      action: ADD
                      event_type: CARD
                      items:
                        - type: CardUser
                          card_name: Bituminous Blast
                          set_name: Planechase Anthology
                          set_code: PCA
                          rarity: Uncommon
                        - type: Tag
                          name: Foiled
                      created_at: '2017-01-24T03:21:23.947Z'
  /snapshots:
    get:
      tags:
        - Snapshot
      summary: Get All SnapShots for User
      description: |-
        Gets all the snapshots in order to creation. The value is localized
        to the users preference.
      operationId: GetAllSnapshotsForUser
      security:
        - OAuth2:
            - user
        - cookieAuth: []
      responses:
        '200':
          description: ''
          content:
            text/plain:
              schema:
                type: object
  /trades/start:
    get:
      tags:
        - Trade
      summary: Get code to start trade
      description: Get code to start trade
      operationId: GetTradeCode
      security:
        - OAuth2:
            - user
        - cookieAuth: []
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Get Start Code Response
                required: &ref_534
                  - code
                type: object
                properties: &ref_535
                  code:
                    type: string
                    example: 1D56K9
                    description: ''
  /trades:
    get:
      tags:
        - Trade
      summary: Get all owned trade records
      description: Get all owned trade records
      operationId: GetAllTrades
      parameters:
        - name: sort_by
          in: query
          required: false
          schema:
            type: string
            enum:
              - created_at
              - completed_at
        - name: order
          in: query
          required: false
          schema:
            type: string
            enum:
              - asc
              - desc
      security:
        - OAuth2:
            - user
        - cookieAuth: []
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Get All Trades Response
                required: &ref_536
                  - trades
                type: object
                properties: &ref_537
                  trades:
                    type: array
                    example:
                      - id: 1
                        uuid: 20900c68-b1f0-427e-bcab-50509fbaf85b
                        cash_adjustment: 0
                        completed_at: '2023-09-04T03:59:30.010Z'
                        primary_user_id: 6
                        secondary_user_id: 7
                        created_at: '2023-09-04T03:59:29.993Z'
                        updated_at: '2023-09-04T03:59:29.993Z'
                      - id: 2
                        uuid: 6644f5c1-7869-40b1-8a87-2b155cfbfc7d
                        cash_adjustment: -100
                        completed_at: null
                        primary_user_id: 6
                        secondary_user_id: 7
                        created_at: '2023-09-04T03:59:29.993Z'
                        updated_at: '2023-09-04T03:59:29.993Z'
                    description: ''
  /trades/{uuid}:
    get:
      tags:
        - Trade
      summary: Get a specific trade record
      description: Get a specific trade record
      operationId: ShowTrade
      parameters:
        - name: uuid
          in: path
          required: true
          schema:
            type: string
            example: 7cfabcf0-438a-48f5-a4af-5b0f89f768a4
      security:
        - OAuth2:
            - user
        - cookieAuth: []
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Show Trades Response
                required: &ref_538
                  - trade
                type: object
                properties: &ref_539
                  trade:
                    type: array
                    example:
                      - id: 1
                        uuid: 20900c68-b1f0-427e-bcab-50509fbaf85b
                        cash_adjustment: 0
                        completed_at: '2023-09-04T03:59:30.010Z'
                        primary_user_id: 6
                        secondary_user_id: 7
                        created_at: '2023-09-04T03:59:29.993Z'
                        updated_at: '2023-09-04T03:59:29.993Z'
                        trade_events:
                          - id: 1
                            trade_id: 1
                            card:
                              - id: 4
                                name: Some card
                                description: Card ability
                            original_card_user:
                              - id: 11
                                card_id: 6
                                foil: true
                            new_card_user:
                              - id: 16
                                card_id: 6
                                foil: true
                            price: 3000
                            created_at: '2023-09-04T03:59:29.993Z'
                            updated_at: '2023-09-04T03:59:29.993Z'
                    description: ''
  /trade_messages:
    get:
      tags:
        - Trade Actions
      summary: Trades interface
      description: |
        # Connecting to the Trades API

        Clients should connect to a WebSocket at the endpoint `/api/cable`.
        Make sure you use the matching WebSocket protocol to the rest of the API (`ws` for `http`, `wss` for `https`). For example, if the website you're connecting to is `https://kraj.cardcaste.co/` you should connect to `wss://kraj.cardcastle.co/api/cable`.

        Clients must provide an `Authorization` header in the initial GET request which establishes the WebSocket, in the same fashion as all other authenticated endpoints. Clients should not provide any query parameters, as they will be ignored.

        Once the WebSocket is connected, the server will expect the messages to be exchanges according to the ActionCable protocol. The following sections outline a typical use of the ActionCable protocol for the Trades API. For more details on the underlying protocol, see https://docs.anycable.io/misc/action_cable_protocol

        All messages are JSON-formatted and should be sent as WebSocket text messages. Some fields are strings which are themselves JSON-formatted a second time - these will be pointed out as we encounter them.

        Note that unfortunately the protocol uses different fields at different levels of nesting in the payload to specify which type a message is, depending on whether it's an ActionCable protocol message (contains a `type` field) or a Trades API protocol message (contains a `type` field inside the `message` field).

        A sensible approach to parsing messages is to first attempt to parse protocol messages by checking for the presence of a `type` field. This will allow the client to identify `welcome`, `disconnect`, `confirm_subscription`, `reject_subscription` and `ping` messages. If the `type` field is not present, the client can check for the presence of an `identifier` field, check that it matches the expected value for the current trade, and then parse the `message` field.

        ## Handshake
        Immediately after connecting, the server will send either a `welcome` or a `disconnect` message:
        ```json
        { 
            "type": "welcome"
        }
        ```
        or
        ```json
        {
            "type": "disconnect",
            "reason": "Something went wrong" 
        }
        ```
        The `reason` field may be `null` or not present.
        If present, it should be treated as a human-readable debug string, and its value should not be relied upon or directly compared against.

        Clients should wait for the `welcome` message before using the connection.

        ## Subscription and initialization

        Once the server confirms that the connection is successful with a `welcome` message, the client should attempt to subscribe to the trade channel. This is done by sending a `subscribe` message:
        ```json
        {
          "command": "subscribe",
          "identifier": "{\"channel\":\"TradeChannelV3\",\"code\":\"1234\"}
        }
        ```
        The `identifier` field is double-encoded JSON. The `code` field in the identifier is the trade code.

        After receiving the `subscribe`, the server will reply with a `confirm_subscription` message with a matching `identifier`:
        ```json
        {
            "type": "confirm_subscription",
            "identifier": "{\"channel\":\"TradeChannelV3\",\"code\":\"1234\"}
        }
        ```
        Instead of the `confirm_subscription` message, the server may send a `reject_subscription` message.
        This typically means that the trade is already in use by two other users, or a failure to authenticate the user.
        ```json
        {
            "type": "reject_subscription",
            "identifier": "{\"channel\":\"TradeChannelV3\",\"code\":\"1234\"}
        }
        ```

        If the subscription is successful, immediately after the `confirm_subscription` message, the server will also send an `INIT` message:
        ```json
        {
            "identifier": "{\"channel\":\"TradeChannelV3\",\"code\":\"1234\"},
            "message": "\{\"type\": \"INIT\", \"trade_state\":{\"users\":{\"primary_user\":null,\"secondary_user\":null},\"trade_record\":{\"id\":null},\"cash_adjust\":0,\"selection\":{\"primary_user\":[],\"secondary_user\":[]},\"acceptance\":{\"primary_user\":false,\"secondary_user\":false},\"confirmation\":{\"primary_user\":false,\"secondary_user\":false},\"trade_phase\":\"INIT\"}}"
        }
        ```
        The `INIT` message contains the current state of the trade. Both `identifier` and `message` fields are double-encoded JSON.
        Note that the trade may have already been initiated, either by the user on the other end or by a previous connection by the same user. Clients should not make any assumptions about the state of the trade on subscription, and fully initialize it from the contents of the `INIT` message.

        The state of ongoing trades is stored for a certain amount of time (currently a day), and a client may re-join a trade they were already a part of by re-entering the same code again - the `INIT` message will contain the state of the trade as previously left.

        The first user to join the trade will be the primary user, and their `uuid` will be present in the `users` key of the `INIT` message. The secondary user (if present), is the second user to join the trade. There are no functional differences between the primary and secondary users, and the terminology is only used for book-keeping - the client should keep track whether their user is the primary or secondary user in order to correctly interpret other information from the trades API.

        Take note of the `trade_phase` value in the `INIT` message. If the value of `trade_phase` is `INIT`, that means that the client's user was the primary user, and no secondary user has joined the trade yet. Until a secondary user joins the trade, the channel is not fully initialized yet, and actions cannot be sent yet.

        Once a second user joins the trade, the first user will receive a second `INIT` message, with the `secondary_user` key set, and the `trade_phase` key set to `SELECTION`.

        ## Actions

        After initialization, the client may send action messages:
        ```json
        {
            "command": "message",
            "identifier": "{\"channel\":\"TradeChannelV3\",\"code\":\"1234\"},
            "data": "{\"action\":\"cash_adjust\", \"cash_adjust\":10 }"
        }
        ```
        The `identifier` and `data` fields are double-encoded JSON. Refer to the Trade Flow and Client Actions section for which actions the client may send and when.
        Clients should not assume that actions they sent will be acted upon. For example, after client 1 sends an action but before it is received, client 2 (connected as the other user in the trade) may send a different action which conflicts with client 1's action.
        Instead, clients should wait to receive a corresponding update before updating their local state of the trade.

        ## Updates

        After initialization, the server may send update messages at any time. These will be sent as a result of either connected client's actions.
        ```json
        {
            "identifier": "{\"channel\":\"TradeChannelV3\",\"code\":\"1234\"},
            "message": "{\"type\":\"add\",\"user\":\"f69ef44b-1a81-4967-81b4-1493e1aaa68d\",\"card_users\":[\"6d2b92da-d9f5-424b-81b2-cb15a6fefb71\"]}"
        }
        ```
        Both `identifier` and `message` fields are double-encoded JSON.
        Note that all clients will receive all updates, including those caused by their own actions. The `user` field in the payload contains one of the connected user's uuid and identifies which user's action was responsible for the update.

        ## Errors
        Similarly to updates, the server may send error messages to inform the clients that an action could not be performed:
        ```json
        {
            "identifier": "{\"channel\":\"TradeChannelV3\",\"code\":\"1234\"},
            "message": "{\"type\":\"error\",\"user\":\"f69ef44b-1a81-4967-81b4-1493e1aaa68d\",\"message\":\"That didn't work\"}"
        }
        ```
        Like updates, `error` messages will contain a `user` field to identify which user in the trade was responsible for the action that could not be performed.

        ## Disconnection
        At any point during the connection, the server may send a `disconnect` message, indicating that the client should disconnect and no more messages will be processed or sent (see Handshake for an example).
        This will typically happen if something went wrong on the server side, or if the client sends a malformed message.

        ## Pings
        At regular intervals during the connection (by default 3 seconds), the server will send a `ping` message:
        ```json
        { 
            "type": "ping", 
            "message": 1696989785 
        }
        ```
        The `message` field is the number of seconds since the Unix epoch when the ping was sent.

        The client does not need to respond to or acknowledge `ping` messages, but should be able to receive them without error. A client may keep track of `ping` messages to determine whether the connection has silently failed.

        # Trade Flow

        Any given trade is in one of the following phases: `INIT`, `SELECTION`, `ACCEPT`, `CONFIRM` or `COMPLETED`.

        When the first (or primary) user joins the trade, the trade is in the `INIT` phase, waiting for the second (or secondary) user to join.

        Once the secondary user joins, the trade phase moves to `SELECTION`. During the `SELECTION` phase, users can add or remove cards to their trade offer, as well as set a cash balance to be exchanged to balance an uneven trade. Both users can freely modify their trade offer and see live the other user's changes to their respective offer.

        Once a user is happy with a trade, they can `ACCEPT` it. When both users `ACCEPT` a trade, the trade moves to the `ACCEPT` phase. During the `ACCEPT` phase. The user is guaranteed that the trade won't change once it has entered the `ACCEPT` phase, giving them an opportunity to review the full trade offer without the other user being able to modify it. If either user modifies their offer during the `ACCEPT` phase, it reverts back to the `SELECTION` phase.

        Once a user has reviewed the trade and is happy with it, they can `CONFIRM` it. Once both users `CONFIRM` a trade, it moves to the `COMPLETED` phase and the cards are exchanged.

        # Message Types

        ## Client Actions

        ## Add to Selection
        The `add_to_selection` action lets the use add cards from their own collection to the trade. If the trade is currently in the `ACCEPT` or `CONFIRM` phase, it will remove those flags and revert it back to the `SELECTION` phase. Its corresponding update message contains all cards that are currently selected to trade, after adding the ones specified by the action.

        **Parameters:**
        - card_users: [ `'5680a399-38e1-4c02-83af-144f3338c160'`, `'b6a985e9-6acd-40a0-9b62-ca737dbe8fd3'` ]

        **Update message:**
        - `{ user: { uuid: '5680a399-38e1-4c02-83af-144f3338c160' }, type: 'ADD', card_users: [{ id: 1, card_id: 2, foil: true, quality: 'Near Mint', created_at: '2023-09-04T06:55:01.640Z', updated_at: '2023-09-04T06:55:01.640Z', language: 'en', uuid: '0ef0db75-de3a-4ee5-8115-72fabbf82236', scanned_image: {url: nil}, input_session_id: 4 }, {...}] }`

        ### Remove from Selection
        The `remove_to_selection` action lets the use add cards from their own collection to the trade. If the trade is currently in the `ACCEPT` or `CONFIRM` phase, it will remove those flags and revert it back to the `SELECTION` phase. Its corresponding update message contains all cards that are currently selected to trade, after removing the ones specified by the action.

        **Parameters:**
        - card_users: [ `'5680a399-38e1-4c02-83af-144f3338c160'`, `'b6a985e9-6acd-40a0-9b62-ca737dbe8fd3'` ]    

        **Update message:**
        - `{ user: { uuid: '5680a399-38e1-4c02-83af-144f3338c160' }, type: 'REMOVE', card_users: [ '5680a399-38e1-4c02-83af-144f3338c160', 'b6a985e9-6acd-40a0-9b62-ca737dbe8fd3' ] }`

        ### Cash Adjust
        The `cash_adjust` action sets the cash balance to the transaction, which represents the difference in value between both users' selections. The action takes optional parameters `cash_adjust` and `user` that adds a set amount to the balance.

        ***Notes:*** 
        - if the value amount is positive, then the `secondary user` owes the balance to the `primary user`. If the value is negative, then the `primary user` owes the balance to the `secondary user`.
        - If no parameters are passed, the action will automatically calculate the balance for the trade.
        - This action will remove `ACCEPT` and `CONFIRM` flags and move the phase back to `SELECTION`

        **Parameters (optional):**
        - value: `100` (integer, in cents)

        **Update message:**
        - `{ user: { uuid: '5680a399-38e1-4c02-83af-144f3338c160' }, type: 'CASH_ADJUST', value: 100 }`

        ### Accept
        The `accept` action sets the accept flag for the client's user. If the other user has not already accepted the trade, it moves the trade to the `ACCEPT` phase. If ether user adds or removes cards from their selection after either user has accepted, the accept flags will be unset and the phase will revert back to `SELECTION`.

        **Parameters:**
        (none)

        **Update message:**
        - `{ user: { uuid: '5680a399-38e1-4c02-83af-144f3338c160' }, type: 'ACCEPT', accept: true }`

        ### Confirm
        The `confirm` action sets the confirm for the client's user. The action will return an error unless both users have already accepted and the trade is in the `ACCEPT` phase.

        If the other user has not already confirmed the trade, the trade moves to the `CONFIRM` phase.
        If the other user has already confirmed the trade, the trade will be finalized and cards will be exchanged in users' collections.

        If ether user adds or removes cards from their selection after either user has confirmed, the accept and confirm flags will be unset and the phase will revert back to `SELECTION`.

        **Parameters:**
        (none)

        **Update message:**
        When the other user has not yet confirmed:
        - `{ user: { uuid: '5680a399-38e1-4c02-83af-144f3338c160' }, type: 'CONFIRM', confirm: true }`

        When both users have confirmed:
        - `{ user: { uuid: '5680a399-38e1-4c02-83af-144f3338c160' }, type: 'FINALIZE', message: ‘the trade has been completed successfully’ }`

        ### End Trade
        The `end_trade` action stops user and server streams, ending the trade. This action can be called at any time and by any user, unilaterally ending the trade. It is also called automatically after the trade has finalized.

        **Parameters:**
        (none)

        **Update message:**
        When current phase is COMPLETED:
        - `{ user: { uuid: null }, type: 'END_TRADE', message: 'The trade channel has been closed' }`

        During any other phase:
        - `{ user: { uuid: '5680a399-38e1-4c02-83af-144f3338c160' }, type: 'END_TRADE', message: 'The user has cancelled the trade' }`


        ## Non-action update messages

        ### Init message
        This message is sent by the server when the trade is joined, and contains the full state of the trade.

        ```json
        {
            "type": "INIT",
            "trade_state": {
                "users": { 
                    "primary_user": null,
                    "secondary_user": null
                },
                "cash_adjust": 0,
                "selection": { 
                    "primary_user": [],
                    "secondary_user": []
                },
                "acceptance": {
                    "primary_user":false,
                    "secondary_user":false
                },
                "confirmation": {
                    "primary_user":false,
                    "secondary_user":false
                },
                "trade_phase":"SELECTION"
            }
        }
        ```

        ### Trade Phase Changes
        This message is sent by the server whenever the trade phase changes.

        ```json
        {
            type: 'PHASE_CHANGE',
            phase: 'ACCEPT' 
        }
        ```
      responses:
        '200':
          description: ''
  /scan_metadata:
    post:
      tags:
        - Scan Metadata
      summary: Get Scan Metadata details for a list of card instances
      description: Get Scan Metadata details for a list of card instances
      operationId: PostSearchScanMetadata
      parameters:
        - name: instance_uuids
          in: query
          required: true
          schema:
            type: array
            items:
              type: string
            example: '[43c04988-c31b-40c5-b30d-7fbffe938a35, f258a560-8004-40e0-8a1d-39af0b8d84ff]'
        - name: game
          in: query
          required: true
          schema:
            type: string
            enum:
              - mtg
              - poke
              - yugi
              - lorcana
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Search Scan Metadata response
                required: &ref_540
                  - scan_metadata
                type: object
                properties: &ref_541
                  scan_metadata:
                    type: object
                    items:
                      type: object
                    description: ''
                example: &ref_542
                  scan_metadata:
                    0c67391b0056fd6ec898ac4a0b6db596eabb2362:
                      confidence: 0.3615242252375491
                      flipped: true
                      mcr_version: 8.5.4
                      evermind_version: 4.3.4
                      crop:
                        tl:
                          - 0.5579982946030517
                          - 0.3310492777962294
                        tr:
                          - 0.236500461895486
                          - 0.5544843908073481
                        br:
                          - 0.****************
                          - 0.****************
                        bl:
                          - 0.6774267671688897
                          - 0.877932231273274
        '400':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
  /price_check:
    post:
      tags:
        - Price Check
      summary: Retrieve prices for a card
      description: Unauthenticated route to get matching prices for a card
      operationId: GetPriceForCard
      security:
        - {}
        - cookieAuth: []
      parameters:
        - name: uuid
          in: query
          required: true
          schema:
            type: string
            description: Card's UUID or JSONID (for MTG cards)
        - name: game
          in: query
          required: true
          schema:
            type: string
            default: mtg
            enum:
              - mtg
              - poke
              - yugi
              - lorcana
        - name: pricing_source
          in: query
          required: true
          schema:
            type: string
            default: tcg_player
            enum:
              - tcg_player
              - card_kingdom
              - card_market
        - name: currency
          in: query
          required: false
          schema:
            type: string
            default: USD
            description: 'List of supported currencies: https://docs.openexchangerates.org/reference/supported-currencies'
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                title: Get card price
                required: &ref_543
                  - prices
                type: object
                properties: &ref_544
                  prices:
                    type: object
                    example:
                      normal:
                        nm: 800
                        lp: 700
                        mp: 600
                        hp: 400
                        dm: 150
                      foil:
                        nm: 1200
                        lp: 900
                        mp: 800
                        hp: 750
                        dm: 400
                    description: ''
        '400':
          description: Unexpected error in API call. See HTTP response body for details.
          content:
            application/json:
              schema:
                example: *ref_0
components:
  securitySchemes:
    OAuth2:
      type: oauth2
      description: Access to CardCastle API using Token based Auth
      flows:
        password:
          tokenUrl: /oauth2/authorize
          scopes:
            admin: Admin Access
            user: Default user scope
        clientCredentials:
          scopes:
            bot: Bot Scope
          tokenUrl: ''
    cookieAuth:
      type: apiKey
      in: cookie
      name: _OzGuild_session
  schemas:
    GetCollectionTotalResponse:
      title: Get Collection Total response
      required: *ref_143
      type: object
      properties: *ref_144
      example: *ref_145
    LookupCollectionByNameOrJsonIdRequest:
      title: Lookup Collection by Name or Json ID request
      required: *ref_146
      type: object
      properties: *ref_147
      example: *ref_148
    LookupCollectionByNameOrJsonIdResponse:
      title: Lookup Collection by Name or Json ID response
      required: *ref_149
      type: object
      properties: *ref_150
      example: *ref_151
    400Error:
      example: *ref_0
    MtgSearchCollectionResponse:
      title: MTG Search Collection response
      type: object
      example: *ref_35
    ListCardIdsForJsonIdsRequest:
      title: List Card IDs for Json IDs request
      required: *ref_152
      type: object
      properties: *ref_153
      example: *ref_154
    ListCardIdsForJsonIdsResponse:
      title: List Card IDs for Json IDs response
      required: *ref_155
      type: object
      properties: *ref_156
      example: *ref_157
    CountCardusersForNameRequest:
      title: Count CardUsers for Name request
      required: *ref_158
      type: object
      properties: *ref_159
      example: *ref_160
    CountCardusersForNameResponse:
      title: Count CardUsers for Name response
      required: *ref_161
      type: object
      properties: *ref_162
      example: *ref_163
    CollectionCountByResponse:
      title: Count CardUsers by given category response
      required: *ref_164
      type: object
      properties: *ref_165
    GetAllCardsResponse:
      title: Get All Cards response
      required: *ref_166
      type: object
      properties: *ref_167
      example: *ref_168
    GetCardsPrintingsRequest:
      title: Get printings for cards by JSON ids
      required: *ref_28
      type: object
      properties: *ref_29
    GetCardsPrintingsResponse:
      title: Get All Cards printings response
      required: *ref_169
      type: object
      properties: *ref_170
    GetCardsLanguagesResponse:
      title: Get All Cards language response
      required: *ref_171
      type: object
      properties: *ref_172
    GetDetailsForCardResponse:
      title: Get Details for Card response
      required: *ref_30
      type: object
      properties: *ref_31
      example: *ref_32
    GetDetailsForCardByJsonIdRequest:
      title: Get Card By Json Id Request
      required: *ref_173
      type: array
      items: *ref_174
      example: *ref_175
    GetCardsArtistsResponse:
      title: Get all card artists
      required: *ref_176
      type: object
      properties: *ref_177
    GetCardsSuperTypesResponse:
      title: Get all card super_types
      required: *ref_178
      type: object
      properties: *ref_179
    GetCardsSubTypesResponse:
      title: Get all card sub_types
      required: *ref_180
      type: object
      properties: *ref_181
    GetCardsTypesResponse:
      title: Get all card types
      required: *ref_182
      type: object
      properties: *ref_183
    CreateCardsForUserRequest:
      title: Create Cards For User request
      required: *ref_39
      type: object
      properties: *ref_40
      example: *ref_41
    CreateCardsForUserResponse:
      title: Create Cards For User response
      required: *ref_36
      type: object
      properties: *ref_37
      example: *ref_38
    DeleteCardRequest:
      title: Delete Card request
      required: *ref_184
      type: object
      properties: *ref_185
      example: *ref_186
    UpdateFoilOnCardRequest:
      title: Update Foil on Card request
      required: *ref_187
      type: object
      properties: *ref_188
      example: *ref_189
    UpdateQualityOnCardRequest:
      title: Update Quality on Card request
      required: *ref_190
      type: object
      properties: *ref_191
      example: *ref_192
    UpdateLanguageOnCardRequest:
      title: Update Language on Card request
      required: *ref_193
      type: object
      properties: *ref_194
    CommitStagedCardsResponse:
      title: Commit Staged Cards response
      required: *ref_195
      type: object
      properties: *ref_196
      example: *ref_197
    500Error:
      title: 404 Error
      required: *ref_198
      type: object
      properties: *ref_199
      example: *ref_200
    CardDeckResourcesRequest:
      title: Card User Resource request
      required: *ref_201
      type: object
      properties: *ref_202
      example: *ref_203
    CardUserResourceResponse:
      title: Get a Resource response
      required: *ref_204
      type: object
      properties: *ref_205
    CardUserCardResponse:
      title: Patch card users card response
      required: *ref_206
      type: object
      properties: *ref_207
    CardUserScannedImageResponse:
      title: Scanned images response
      required: *ref_208
      type: object
      properties: *ref_209
      example: *ref_210
    GetCardListsResponse:
      title: Get Card List Response
      type: object
      properties: *ref_211
      example: *ref_212
    CreateCardListRequest:
      title: Create Card List Request
      type: object
      required: *ref_213
      properties: *ref_214
      example: *ref_215
    CreateCardListResponse:
      title: Create Card List Response
      type: object
      properties: *ref_216
      example: *ref_217
    ShowCardListResponse:
      title: Show Card List Response
      type: object
      properties: *ref_218
      example: *ref_219
    UpdateCardListRequest:
      title: Update Card List Request
      type: object
      required: *ref_220
      properties: *ref_221
      example: *ref_222
    UpdateCardListResponse:
      title: Update Card List Response
      type: object
      properties: *ref_223
      example: *ref_224
    AddListedCardRequest:
      title: Add Listed Cards to a Card List Request
      type: object
      required: *ref_225
      properties: *ref_226
      example: *ref_227
    AddListedCardResponse:
      title: Add a Listed Cards to a Card List Response
      type: object
      properties: *ref_228
      example: *ref_229
    AddAllPrintingsRequest:
      title: Add All Printings to a Card List Request
      type: object
      required: *ref_230
      properties: *ref_231
      example: *ref_232
    AddAllPrintingsResponse:
      title: Add All Printings to a Card List Request
      type: object
      properties: *ref_233
      example: *ref_234
    RemoveListedCardRequest:
      title: Remove a Listed Card to a Card List Request
      type: object
      required: *ref_235
      properties: *ref_236
      example: *ref_237
    GetAllCardSetsResponse:
      title: Get all card sets
      required: *ref_238
      type: object
      properties: *ref_239
    GetAllCardSetsTypesResponse:
      title: Get all card set types
      required: *ref_240
      type: object
      properties: *ref_241
    poke_card_fields:
      type: array
      items: *ref_54
    SearchPokeCardsResponse:
      title: Search Poke Cards response
      required: *ref_242
      type: object
      properties: *ref_243
      example: *ref_244
    SearchPokeCardsCompactResponse:
      title: Search Poke Cards compact response
      required: *ref_245
      type: object
      properties: *ref_246
      example: *ref_247
    GetPokeCardsPrintingsRequest:
      title: Get printings for poke cards by UUIDs
      required: *ref_248
      type: object
      properties: *ref_249
    GetPokeCardsPrintingsResponse:
      title: Get all poke cards printings response
      required: *ref_250
      type: object
      properties: *ref_251
    GetPokeCardsSuperTypesResponse:
      title: Get all card super_types
      required: *ref_252
      type: object
      properties: *ref_253
    GetPokeCardsSubTypesResponse:
      title: Get all card sub_types
      required: *ref_254
      type: object
      properties: *ref_255
    GetPokeCardsTypesResponse:
      title: Get all card types
      required: *ref_256
      type: object
      properties: *ref_257
    GetPokeCardsRaritiesResponse:
      title: Get all card rarities
      required: *ref_258
      type: object
      properties: *ref_259
    GetPokeCardsFinishesResponse:
      title: Get all card finishes
      required: *ref_260
      type: object
      properties: *ref_261
    GetPokeVersionResponse:
      title: Get version
      required: *ref_262
      type: object
      properties: *ref_263
    AddPokeCardUserResponse:
      title: Add Poke Card User response
      required: *ref_264
      type: object
      properties: *ref_265
      example: *ref_266
    UpdatePokeCardUserResponse:
      title: Update Poke Card User response
      required: *ref_267
      type: object
      properties: *ref_268
      example: *ref_269
    PokeCardUserPokeCardResponse:
      title: Patch poke card users poke card response
      required: *ref_270
      type: object
      properties: *ref_271
    PokeCardUserScannedImageResponse:
      title: Scanned images response
      required: *ref_272
      type: object
      properties: *ref_273
      example: *ref_274
    GetAllPokeCardSetsResponse:
      title: Get all Poke card sets
      required: *ref_275
      type: object
      properties: *ref_276
    collection_summary_fields:
      type: array
      items: *ref_83
    collection_group_fields:
      type: array
      items: *ref_84
    poke_card_instance_fields:
      type: array
      items: *ref_277
    PokeSearchCollectionResponse:
      title: Pokemon Search Collection response
      type: object
      example: *ref_278
    GetAllPokeCardIdentifiersResponse:
      title: Get all poke card identifiers
      required: *ref_279
      type: object
      properties: *ref_280
    GetAllPokeSkusResponse:
      title: Get all poke skus
      required: *ref_281
      type: object
      properties: *ref_282
    GetAllPokeFinishesResponse:
      title: Get all poke finishes
      required: *ref_283
      type: object
      properties: *ref_284
    GetAllPokeRaritiesResponse:
      title: Get all poke rarities
      required: *ref_285
      type: object
      properties: *ref_286
    IndexYugiCardsResponse:
      title: Get all yugi cards response
      required: *ref_287
      type: object
      properties: *ref_288
    yugi_card_fields:
      type: array
      items: *ref_78
    SearchYugiCardsResponse:
      title: Search Yugi Cards response
      required: *ref_289
      type: object
      properties: *ref_290
      example: *ref_291
    SearchYugiCardsCompactResponse:
      title: Search Yugi Cards compact response
      required: *ref_292
      type: object
      properties: *ref_293
      example: *ref_294
    GetYugiCardTypesResponse:
      title: Get all card types
      required: *ref_295
      type: object
      properties: *ref_296
    GetYugiCardFrameTypesResponse:
      title: Get all card frame types
      required: *ref_297
      type: object
      properties: *ref_298
    GetYugiCardArchetypesResponse:
      title: Get all card archetypes
      required: *ref_299
      type: object
      properties: *ref_300
    GetYugiCardRacesResponse:
      title: Get all card races
      required: *ref_301
      type: object
      properties: *ref_302
    GetYugiVersionResponse:
      title: Get version
      required: *ref_303
      type: object
      properties: *ref_304
    UpdateYugiCardInstanceResponse:
      title: Update Yugi Card Instances response
      required: *ref_79
      type: object
      properties: *ref_80
      example: *ref_81
    AddYugiCardInstancesResponse:
      title: Add Yugi Card Instance response
      required: *ref_305
      type: object
      properties: *ref_306
      example: *ref_307
    YugiCardInstanceYugiCardResponse:
      title: Patch yugi card instances yugi card response
      required: *ref_308
      type: object
      properties: *ref_309
      example: *ref_310
    YugiCardInstanceScannedImageResponse:
      title: Scanned images response
      required: *ref_311
      type: object
      properties: *ref_312
      example: *ref_313
    yugi_card_instance_fields:
      type: array
      items: *ref_314
    YugiSearchCollectionResponse:
      title: Yugioh Search Collection response
      type: object
      example: *ref_315
    GetAllYugiSetsResponse:
      title: Get all yugi sets
      required: *ref_316
      type: object
      properties: *ref_317
    GetAllYugiCardSetsResponse:
      title: Get all yugi card sets
      required: *ref_318
      type: object
      properties: *ref_319
    SearchYugiCardSetsResponse:
      title: Search Yugi Card Sets response
      required: *ref_320
      type: object
      example: *ref_321
    GetAllYugiCardIdentifiersResponse:
      title: Get all yugi card identifiers
      required: *ref_322
      type: object
      properties: *ref_323
    GetAllYugiSkusResponse:
      title: Get all yugi skus
      required: *ref_324
      type: object
      properties: *ref_325
    GetAllYugiRaritiesResponse:
      title: Get all yugioh rarities
      required: *ref_326
      type: object
      properties: *ref_327
    get_all_lorcana_cards:
      title: Get all lorcana cards
      required: *ref_328
      type: object
      properties: *ref_329
    lorcana_card_fields:
      type: array
      items: *ref_111
    SearchLorcanaCardsResponse:
      title: Search Lorcana Cards response
      required: *ref_330
      type: object
      properties: *ref_331
    GetLorcanaCardsPrintingsRequest:
      title: Get printings for lorcana cards by UUIDs
      required: *ref_332
      type: object
      properties: *ref_333
    GetLorcanaCardsPrintingsResponse:
      title: Get all lorcana cards printings response
      required: *ref_334
      type: object
      properties: *ref_335
    GetLorcanaVersionResponse:
      title: Get version
      required: *ref_336
      type: object
      properties: *ref_337
    AddLorcanaCardInstancesResponse:
      title: Add Lorcana Card Instance response
      required: *ref_338
      type: object
      properties: *ref_339
      example: *ref_340
    UpdateLorcanaCardInstanceResponse:
      title: Update Lorcana Card Instance response
      required: *ref_341
      type: object
      properties: *ref_342
      example: *ref_343
    LorcanaCardInstanceLorcanaCardResponse:
      title: Patch Lorcana Card Instance Lorcana Card Response
      required: *ref_344
      type: object
      properties: *ref_345
    LorcanaCardInstancesScannedImageResponse:
      title: Scanned images response
      required: *ref_346
      type: object
      properties: *ref_347
      example: *ref_348
    lorcana_card_instance_fields:
      type: array
      items: *ref_349
    LorcanaSearchCollectionResponse:
      title: Lorcana Search Collection response
      type: object
      example: *ref_350
    GetAllLorcanaSetsResponse:
      title: Get all Lorcana card sets
      required: *ref_351
      type: object
      properties: *ref_352
    GetAllLorcanaCardIdentifiersResponse:
      title: Get all lorcana card identifiers
      required: *ref_353
      type: object
      properties: *ref_354
    GetAllLorcanaSkusResponse:
      title: Get all lorcana skus
      required: *ref_355
      type: object
      properties: *ref_356
    GetAllLorcanaFinishesResponse:
      title: Get all lorcana finishes
      required: *ref_357
      type: object
      properties: *ref_358
    GetAllLorcanaRaritiesResponse:
      title: Get all lorcana rarities
      required: *ref_359
      type: object
      properties: *ref_360
    GetHashVersionForMcrResponse:
      title: Get Hash Version for MCR >= v3 response
      required: *ref_361
      type: object
      properties: *ref_362
      example: *ref_363
    404Error:
      title: 404 Error
      required: *ref_112
      type: object
      properties: *ref_113
      example: *ref_114
    UploadMetricsForMcrRequest:
      title: Upload Metrics for MCR request
      required: *ref_364
      type: object
      properties: *ref_365
      example: *ref_366
    DefaultUserDetailsResponse:
      title: Default User Details Response
      required: *ref_115
      type: object
      properties: *ref_116
      example: *ref_117
    CreateAUserRequest:
      title: Create a User request
      required: *ref_367
      type: object
      properties: *ref_368
      example: *ref_369
    UpdateUserDetailsRequest:
      title: Update User Details request
      required: *ref_370
      type: object
      properties: *ref_371
    UserSearchRequest:
      title: User Search Requst
      required: *ref_372
      type: string
      example: <EMAIL>
    UserSearchResponse:
      title: User Search Response
      type: array
      items: *ref_373
    ResetUserPasswordRequest:
      title: Reset User Password request
      required: *ref_374
      type: object
      properties: *ref_375
      example: *ref_376
    UpdateUserPrefsRequest:
      title: Update User Prefs request
      type: object
      properties: *ref_377
      example: *ref_378
    UpdateUserAvatarRequest:
      title: Update User Avatar request
      required: *ref_379
      type: object
      properties: *ref_380
      example: *ref_381
    UpdateUserAvatarResponse:
      title: Update User Avatar response
      required: *ref_382
      type: object
      properties: *ref_383
      example: *ref_384
    GetAllTagsForUserResponse:
      title: Get All Tags for User response
      required: *ref_385
      type: object
      properties: *ref_386
      example: *ref_387
    CreateTagForCardUsersRequest:
      title: Create Tag for Card Users request
      required: *ref_388
      type: object
      properties: *ref_389
    CreateTagForCardUsersResponse:
      title: Create Tag for Card Users response
      required: *ref_390
      type: object
      properties: *ref_391
      example: *ref_392
    LookupTagsForCardUsersRequest:
      title: Lookup Tags for Card Users request
      required: *ref_393
      type: object
      properties: *ref_394
      example: *ref_395
    LookupTagsForCardUsersResponse:
      title: Lookup Tags for Card Users response
      required: *ref_396
      type: object
      properties: *ref_397
      example: *ref_398
    GetSavedFiltersResponse:
      title: Get Saved Filters Response
      type: object
      properties: *ref_399
      example: *ref_400
    CreateSavedFilterRequest:
      title: Create Saved Filter Request
      type: object
      required: *ref_401
      properties: *ref_402
      example: *ref_403
    CreateSavedFilterResponse:
      title: Create Saved Filters Response
      type: object
      properties: *ref_404
      example: *ref_405
    ShowSavedFilterResponse:
      title: Show Saved Filter Response
      type: object
      properties: *ref_406
      example: *ref_407
    UpdateSavedFilterRequest:
      title: Update Saved Filter Request
      type: object
      properties: *ref_408
      example: *ref_409
    UpdateSavedFilterResponse:
      title: Update Card List Response
      type: object
      properties: *ref_410
      example: *ref_411
    StatisticsTagResponse:
      title: Set Completion Response
      type: object
      example: *ref_412
    StatisticsSetCompletionResponse:
      title: Set Completion Response
      type: object
      example: *ref_413
    InputSourcesIndexResponse:
      title: Get Input Sources Response
      required: *ref_414
      type: object
      properties: *ref_415
    InputSourcesCreateResponse:
      title: Create Input Source Response
      required: *ref_416
      type: object
      properties: *ref_417
    InputSourcesShowResponse:
      title: Show Input Source Response
      required: *ref_118
      type: object
      properties: *ref_119
    GetSessionsIndexResponse:
      title: Get Sessions Response
      required: *ref_418
      type: object
      properties: *ref_419
    CreateSessionResponse:
      title: Create Sessions Response
      required: *ref_420
      type: object
      properties: *ref_421
    GetSessionsShowResponse:
      title: Get Specific Session Response
      required: *ref_120
      type: object
      properties: *ref_121
    AddCardsToSessionResponse:
      title: Add Cards To Sessions Response
      required: *ref_422
      type: object
      properties: *ref_423
    SessionsCommitResponse:
      title: Commit a specific session
      required: *ref_424
      type: object
      properties: *ref_425
    StackedCardUpdatePositionResponse:
      title: Update position of specific stacked card
      required: *ref_426
      type: object
      properties: *ref_427
    LocationsIndexResponse:
      title: Get Locations Response
      required: *ref_122
      type: object
      properties: *ref_123
    LocationsCreateResponse:
      title: Create Locations Response
      required: *ref_428
      type: object
      properties: *ref_429
    LocationsShowResponse:
      title: Show Locations Response
      required: *ref_124
      type: object
      properties: *ref_125
    GetSubscriptionDetailsResponse:
      title: Get Subscription Details response
      required: *ref_430
      type: object
      properties: *ref_431
      example: *ref_432
    CreateSubscriptionRequest:
      title: Create Subscription request
      required: *ref_433
      type: object
      properties: *ref_434
      example: *ref_435
    SendSubscriptionReferralsRequest:
      title: Send Subscription Referrals request
      required: *ref_436
      type: object
      properties: *ref_437
      example: *ref_438
    ApplyCouponRequest:
      title: Apply Coupon request
      required: *ref_439
      type: object
      properties: *ref_440
      example: *ref_441
    GetConfigurationResponse:
      title: Subscription configuration
      type: object
      properties: *ref_442
    GetStripePortalResponse:
      title: Subscription stripe portal
      type: object
      properties: *ref_443
    GetAllDecksResponse:
      title: Get all decks response
      required: *ref_444
      type: object
      properties: *ref_445
      example: *ref_446
    CreateADeckResponse:
      title: Create a Deck response
      required: *ref_447
      type: object
      properties: *ref_448
      example: *ref_449
    SearchForDecksResponse:
      title: Search for Decks response
      required: *ref_450
      type: object
      properties: *ref_451
      example: *ref_452
    GetADeckResponse:
      title: Get a Deck response
      required: *ref_453
      type: object
      properties: *ref_454
      example: *ref_455
    UpdateADeckResponse:
      title: Update a Deck response
      required: *ref_456
      type: object
      properties: *ref_457
      example: *ref_458
    ValidateDeckResponse:
      title: Validate a deck response
      required: *ref_459
      type: object
      properties: *ref_460
    DeckStatisticsResponse:
      title: Deck statistics response
      required: *ref_461
      type: object
      properties: *ref_462
    DeckTurnProbabilitiesResponse:
      title: Deck turn probabilities response
      required: *ref_463
      type: object
      properties: *ref_464
    DeckToPdfResponse:
      title: Deck to pdf response
      required: *ref_465
      type: object
      properties: *ref_466
    CloneADeckResponse:
      title: Clone a Deck response
      required: *ref_467
      type: object
      properties: *ref_468
      example: *ref_469
    CreateDeckBoardRequest:
      title: Create Deck Board request
      required: *ref_470
      type: object
      properties: *ref_471
      example: *ref_472
    CreateDeckBoardResponse:
      title: Create Deck Board response
      required: *ref_473
      type: object
      properties: *ref_474
      example: *ref_475
    UpdateDeckBoardRequest:
      title: Update Deck Board request
      required: *ref_476
      type: object
      properties: *ref_477
      example: *ref_478
    UpdateDeckBoardResponse:
      title: Update Deck Board response
      required: *ref_479
      type: object
      properties: *ref_480
      example: *ref_481
    AddMultipleCardsToDeckResponse:
      title: Add Multiple Cards To Deck response
      required: *ref_482
      type: object
      properties: *ref_483
    AddCardToDeckRequest:
      title: Add Card To Deck request
      required: *ref_484
      type: object
      properties: *ref_485
      example: *ref_486
    AddCardToDeckResponse:
      title: Add Card To Deck response
      required: *ref_487
      type: object
      properties: *ref_488
      example: *ref_489
    AddSelectionOfCardUsersToDeckRequest:
      title: Add Selection of Card Users to Deck request
      required: *ref_490
      type: object
      properties: *ref_491
      example: *ref_492
    AddSelectionOfCardUsersToDeckResponse:
      title: Add Selection of Card Users to Deck response
      required: *ref_493
      type: object
      properties: *ref_494
      example: *ref_495
    AddLandsToDeckRequest:
      title: Add lands to deck request
      required: *ref_496
      type: object
      properties: *ref_497
      example: *ref_498
    AddLandsToDeckResponse:
      title: Add lands to deck response
      required: *ref_499
      type: object
      properties: *ref_500
      example: *ref_501
    UpdateCardDeckRequest:
      title: Update Card Deck request
      required: *ref_502
      type: object
      properties: *ref_503
      example: *ref_504
    UpdateCardDeckResponse:
      title: Update Card Deck response
      required: *ref_505
      type: object
      properties: *ref_506
      example: *ref_507
    AddTagToDeckRequest:
      title: Add tag to deck request
      required: *ref_134
      type: object
      properties: *ref_135
    AddTagToDeckResponse:
      title: Add tag to deck response
      required: *ref_508
      type: object
      properties: *ref_509
      example: *ref_510
    GetAllTagsResponse:
      title: Get All Tags response
      required: *ref_511
      type: object
      properties: *ref_512
      example: *ref_513
    CardBotIndexResponse:
      title: List of Devices
      type: array
      example: *ref_514
    CardBotCreateRequest:
      title: Card Bot Create
      required: *ref_515
      type: string
      example: Joe's cardbot
    CardBotCreateResponse:
      title: Card Bot Create Response
      type: object
      example: *ref_137
    CardBotPairingRequest:
      title: Request to Pair Card Bot
      required: *ref_516
      type: object
      properties: *ref_517
      example: *ref_518
    CardBotMessage:
      title: CardBot Message
      type: object
      example: *ref_136
    BannerResponse:
      title: Banner Response
      type: object
      required: *ref_519
      properties: *ref_520
      example: *ref_521
    ImportExportFormat:
      title: Format information for import/export action
      type: object
      properties: *ref_522
    ImportExportAction:
      title: Action information for data service
      type: object
      properties: *ref_138
    DataService:
      title: Data service for importing and exporting cards
      type: object
      properties: *ref_523
    ImportExportServices:
      title: Import/Export Service Manifest
      type: object
      properties: *ref_524
    GetSubscriptionPricesResponse:
      title: Get All Subscription Prices response
      required: *ref_525
      properties: *ref_526
    PostSubscriptionPriceResponse:
      title: Create New Subscription Price response
      required: *ref_139
      properties: *ref_140
    GetProviderPlansResponse:
      title: Get All Provider Plans response
      required: *ref_527
      properties: *ref_528
    PostProviderPlansResponse:
      title: Create New Provider Plan response
      required: *ref_141
      properties: *ref_142
    GetThePriceForAListOfCardsRequest:
      title: Get the Price for a List of Cards request
      required: *ref_529
      type: object
      properties: *ref_530
    GetAllEventsResponse:
      title: Get All Events response
      required: *ref_531
      type: object
      properties: *ref_532
      example: *ref_533
    GetStartCodeResponse:
      title: Get Start Code Response
      required: *ref_534
      type: object
      properties: *ref_535
    GetAllTradesResponse:
      title: Get All Trades Response
      required: *ref_536
      type: object
      properties: *ref_537
    ShowTradeResponse:
      title: Show Trades Response
      required: *ref_538
      type: object
      properties: *ref_539
    SearchScanMetadataResponse:
      title: Search Scan Metadata response
      required: *ref_540
      type: object
      properties: *ref_541
      example: *ref_542
    SearchCardPriceResponse:
      title: Get card price
      required: *ref_543
      type: object
      properties: *ref_544
  parameters:
    quality_filter:
      name: quality
      in: query
      required: false
      schema: *ref_126
    quantity_filter:
      name: quantity
      in: query
      required: false
      schema: *ref_55
    price_filter:
      name: price
      in: query
      required: false
      schema: *ref_127
    foil_filter:
      name: foil
      in: query
      required: false
      schema: *ref_128
    tags_filter:
      name: tags
      in: query
      required: false
      schema: *ref_129
    in_deck_filter:
      name: in_deck
      in: query
      required: false
      schema: *ref_130
      description: Cards in collection need to be linked/not linked to a deck. If passed an optional uuid param, it will return only cards that are present/not present in a specific deck
    language_filter:
      name: language
      in: query
      required: false
      schema: *ref_131
    source_filter:
      name: source
      in: query
      required: false
      schema: *ref_56
    input_session_filter:
      name: input_session
      in: query
      required: false
      schema: *ref_57
    sort_by_filter:
      name: sort_by
      in: query
      required: false
      schema: *ref_58
    location_filter:
      name: location
      in: query
      required: false
      schema: *ref_132
    confidence_filter:
      name: confidence
      in: query
      required: false
      schema: *ref_59
    json_id_filter:
      name: json_id
      in: query
      required: false
      schema: *ref_1
    card_list_filter:
      name: card_list
      in: query
      required: false
      schema: *ref_2
    order_filter:
      name: order
      in: query
      required: false
      schema: *ref_3
    group_by_filter:
      name: group_by
      in: query
      required: false
      schema: *ref_4
    query_filter:
      name: query
      in: query
      required: false
      schema: *ref_5
    starts_with_filter:
      name: starts_with
      in: query
      required: false
      schema: *ref_6
    set_starts_with_filter:
      name: set_starts_with
      in: query
      required: false
      schema: *ref_7
    rules_text_filter:
      name: rules_text_filter
      in: query
      required: false
      schema: *ref_8
      description: Searches for the whole string, so it must wholly exist within the rules text.
    oracle_text_filter:
      name: oracle_text_filter
      in: query
      required: false
      schema: *ref_9
      description: Searches for the whole string, so it must wholly exist within the rules text.
    per_page_filter:
      name: per_page
      in: query
      required: false
      schema: *ref_10
    page_filter:
      name: page
      in: query
      required: false
      schema: *ref_11
    colors_filter:
      name: colors
      in: query
      required: false
      schema: *ref_12
    color_identity_filter:
      name: color_identity
      in: query
      required: false
      schema: *ref_13
    types_filter:
      name: types
      in: query
      required: false
      schema: *ref_14
      description: Accepted types can be retrieved from the `/types` route.
    sub_types_filter:
      name: sub_types
      in: query
      required: false
      schema: *ref_15
    super_types_filter:
      name: super_types
      in: query
      required: false
      schema: *ref_16
    set_names_filter:
      name: set_names
      in: query
      required: false
      schema: *ref_17
    artists_filter:
      name: artists
      in: query
      required: false
      schema: *ref_18
    rarity_filter:
      name: rarity
      in: query
      required: false
      schema: *ref_19
    power_filter:
      name: power
      in: query
      required: false
      schema: *ref_20
    toughness_filter:
      name: toughness
      in: query
      required: false
      schema: *ref_21
    loyalty_filter:
      name: loyalty
      in: query
      required: false
      schema: *ref_22
      description: 'The loyalty filter will only match planeswalkers with loyalty that is not an integer (eg: ''1d4+1'', ''*'', or ''X'') when min is 0.'
    mana_value_filter:
      name: mana_value (converted mana cost)
      in: query
      required: false
      schema: *ref_23
    format_legality_filter:
      name: format_legality
      in: query
      required: false
      schema: *ref_24
    set_types_filter:
      name: set_types
      in: query
      required: false
      schema: *ref_25
    is_reserved_filter:
      name: is_reserved
      in: query
      required: false
      schema: *ref_26
    border_filter:
      name: border
      in: query
      required: false
      schema: *ref_27
    card_filters_sort_by_filter:
      name: sort_by
      in: query
      required: false
      schema: *ref_33
    card_filters_price_filter:
      name: price
      in: query
      required: false
      schema: *ref_34
    name_filter:
      name: name
      in: query
      required: false
      schema: *ref_87
    poke_card_filters_starts_with_filter:
      name: starts_with
      in: query
      required: false
      schema: *ref_52
      description: matches the first letter (case insensitive) of a poke card's name
    poke_card_filters_set_starts_with_filter:
      name: set_starts_with
      in: query
      required: false
      schema: *ref_53
      description: matches the first letter (case insensitive) of a poke card's set name
    poke_card_filters_types_filter:
      name: types
      in: query
      required: false
      schema: *ref_48
      description: Accepted types can be retrieved from the `/types` route.
    poke_card_filters_sub_types_filter:
      name: sub_types
      in: query
      required: false
      schema: *ref_49
    super_type_filter:
      name: super_type
      in: query
      required: false
      schema: *ref_50
    artist_filter:
      name: artist
      in: query
      required: false
      schema: *ref_62
    poke_card_filters_rarity_filter:
      name: rarity_filter
      in: query
      required: false
      schema: *ref_65
    level_filter:
      name: level
      in: query
      required: false
      schema: *ref_63
    hp_filter:
      name: hp
      in: query
      required: false
      schema: *ref_64
    poke_card_filters_rules_text_filter:
      name: rules_text
      in: query
      required: false
      schema: *ref_60
    flavor_text_filter:
      name: flavor_text
      in: query
      required: false
      schema: *ref_90
    condition_filter:
      name: condition
      in: query
      required: false
      schema: *ref_82
    yugi_card_filters_group_by_filter:
      name: group_by
      in: query
      required: false
      schema: *ref_67
    yugi_card_filters_set_names_filter:
      name: set_names
      in: query
      required: false
      schema: *ref_72
      description: Payload can include `with`, `without`, or both
    yugi_card_filters_price_filter:
      name: price
      in: query
      required: false
      schema: *ref_73
      description: when no source is passed, price source defaults to user preference.
    printing_uuid_filter:
      name: printing_uuid
      in: query
      required: false
      schema: *ref_74
    race_filter:
      name: race
      in: query
      required: false
      schema: *ref_75
    archetype_filter:
      name: archetype
      in: query
      required: false
      schema: *ref_76
    yugi_card_filters_starts_with_filter:
      name: starts_with
      in: query
      required: false
      schema: *ref_77
      description: matches the first letter (case insensitive) of a yugi card's name
    lorcana_card_filters_sort_by_filter:
      name: sort_by
      in: query
      required: false
      schema: *ref_545
    lorcana_card_filters_rules_text_filter:
      name: rules_text
      in: query
      required: false
      schema: *ref_89
    layout_filter:
      name: layout
      in: query
      required: false
      schema: *ref_91
    version_filter:
      name: version
      in: query
      required: false
      schema: *ref_92
    ink_filter:
      name: ink
      in: query
      required: false
      schema: *ref_93
    inkwell_filter:
      name: inkwell
      in: query
      required: false
      schema: *ref_94
    collector_number_filter:
      name: collector_number
      in: query
      required: false
      schema: *ref_95
    lorcana_card_filters_types_filter:
      name: types
      in: query
      required: false
      schema: *ref_98
      description: Accepted types can be retrieved from the `/types` route.
    classifications_filter:
      name: classifications
      in: query
      required: false
      schema: *ref_99
    lorcana_card_filters_artist_filter:
      name: artist
      in: query
      required: false
      schema: *ref_100
    lorcana_card_filters_starts_with_filter:
      name: starts_with
      in: query
      required: false
      schema: *ref_102
      description: matches the first letter (case insensitive) of a lorcana card's name
    lorcana_card_filters_set_starts_with_filter:
      name: set_starts_with
      in: query
      required: false
      schema: *ref_103
      description: matches the first letter (case insensitive) of a lorcana card's set name
    strength_filter:
      name: strength
      in: query
      required: false
      schema: *ref_104
    lore_filter:
      name: lore
      in: query
      required: false
      schema: *ref_105
    willpower_filter:
      name: willpower
      in: query
      required: false
      schema: *ref_106
    move_cost_filter:
      name: move_cost
      in: query
      required: false
      schema: *ref_107
    cost_filter:
      name: cost
      in: query
      required: false
      schema: *ref_108
    lorcana_card_filters_price_filter:
      name: price
      in: query
      required: false
      schema: *ref_109
      description: when no source is passed, price source defaults to user preference.
    lorcana_card_filters_rarity_filter:
      name: rarity_filter
      in: query
      required: false
      schema: *ref_110