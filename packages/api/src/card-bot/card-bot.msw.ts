/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import { faker } from '@faker-js/faker';

import { HttpResponse, delay, http } from 'msw';

import type {
  CreateCardBot200,
  GetCardBotIndex200Item,
  GetCardBotMessage200,
  GetDevice200,
  ReceiveCardBotMessage200,
} from '.././model';

export const getGetCardBotIndexResponseMock = (): GetCardBotIndex200Item[] =>
  Array.from(
    { length: faker.number.int({ min: 1, max: 10 }) },
    (_, i) => i + 1,
  ).map(() => ({}));

export const getCreateCardBotResponseMock = (): CreateCardBot200 => ({});

export const getGetCardBotMessageResponseMock =
  (): GetCardBotMessage200 => ({});

export const getReceiveCardBotMessageResponseMock =
  (): ReceiveCardBotMessage200 | void => ({});

export const getGetDeviceResponseMock = (): GetDevice200 => ({});

export const getGetCardBotIndexMockHandler = (
  overrideResponse?:
    | GetCardBotIndex200Item[]
    | ((
        info: Parameters<Parameters<typeof http.get>[1]>[0],
      ) => Promise<GetCardBotIndex200Item[]> | GetCardBotIndex200Item[]),
) => {
  return http.get('*/card_bots', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getGetCardBotIndexResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getCreateCardBotMockHandler = (
  overrideResponse?:
    | CreateCardBot200
    | ((
        info: Parameters<Parameters<typeof http.post>[1]>[0],
      ) => Promise<CreateCardBot200> | CreateCardBot200),
) => {
  return http.post('*/card_bots', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getCreateCardBotResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getPairCardBotMockHandler = (
  overrideResponse?:
    | void
    | ((
        info: Parameters<Parameters<typeof http.post>[1]>[0],
      ) => Promise<void> | void),
) => {
  return http.post('*/card_bots/pair', async (info) => {
    await delay(1000);
    if (typeof overrideResponse === 'function') {
      await overrideResponse(info);
    }
    return new HttpResponse(null, { status: 201 });
  });
};

export const getGetCardBotMessageMockHandler = (
  overrideResponse?:
    | GetCardBotMessage200
    | ((
        info: Parameters<Parameters<typeof http.get>[1]>[0],
      ) => Promise<GetCardBotMessage200> | GetCardBotMessage200),
) => {
  return http.get('*/card_bots/command', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getGetCardBotMessageResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getSendCardBotMessageMockHandler = (
  overrideResponse?:
    | void
    | ((
        info: Parameters<Parameters<typeof http.post>[1]>[0],
      ) => Promise<void> | void),
) => {
  return http.post('*/card_bots/command', async (info) => {
    await delay(1000);
    if (typeof overrideResponse === 'function') {
      await overrideResponse(info);
    }
    return new HttpResponse(null, { status: 201 });
  });
};

export const getReceiveCardBotMessageMockHandler = (
  overrideResponse?:
    | ReceiveCardBotMessage200
    | void
    | ((
        info: Parameters<Parameters<typeof http.get>[1]>[0],
      ) =>
        | Promise<ReceiveCardBotMessage200 | void>
        | ReceiveCardBotMessage200
        | void),
) => {
  return http.get('*/card_bots/report', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getReceiveCardBotMessageResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getSendMessageToBackendMockHandler = (
  overrideResponse?:
    | void
    | ((
        info: Parameters<Parameters<typeof http.post>[1]>[0],
      ) => Promise<void> | void),
) => {
  return http.post('*/card_bots/report', async (info) => {
    await delay(1000);
    if (typeof overrideResponse === 'function') {
      await overrideResponse(info);
    }
    return new HttpResponse(null, { status: 201 });
  });
};

export const getGetCardBotStatusMockHandler = (
  overrideResponse?:
    | void
    | ((
        info: Parameters<Parameters<typeof http.post>[1]>[0],
      ) => Promise<void> | void),
) => {
  return http.post('*/card_bots/status', async (info) => {
    await delay(1000);
    if (typeof overrideResponse === 'function') {
      await overrideResponse(info);
    }
    return new HttpResponse(null, { status: 204 });
  });
};

export const getGetDeviceMockHandler = (
  overrideResponse?:
    | GetDevice200
    | ((
        info: Parameters<Parameters<typeof http.get>[1]>[0],
      ) => Promise<GetDevice200> | GetDevice200),
) => {
  return http.get('*/card_bots/:deviceId', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getGetDeviceResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getUpdateCardBotDisabledMockHandler = (
  overrideResponse?:
    | void
    | ((
        info: Parameters<Parameters<typeof http.patch>[1]>[0],
      ) => Promise<void> | void),
) => {
  return http.patch('*/card_bots/:deviceId', async (info) => {
    await delay(1000);
    if (typeof overrideResponse === 'function') {
      await overrideResponse(info);
    }
    return new HttpResponse(null, { status: 204 });
  });
};

export const getLinkUserToCardBotMockHandler = (
  overrideResponse?:
    | void
    | ((
        info: Parameters<Parameters<typeof http.patch>[1]>[0],
      ) => Promise<void> | void),
) => {
  return http.patch('*/card_bots/:deviceId/link', async (info) => {
    await delay(1000);
    if (typeof overrideResponse === 'function') {
      await overrideResponse(info);
    }
    return new HttpResponse(null, { status: 204 });
  });
};

export const getUnlinkUserToCardBotMockHandler = (
  overrideResponse?:
    | void
    | ((
        info: Parameters<Parameters<typeof http.patch>[1]>[0],
      ) => Promise<void> | void),
) => {
  return http.patch('*/card_bots/:deviceId/unlink', async (info) => {
    await delay(1000);
    if (typeof overrideResponse === 'function') {
      await overrideResponse(info);
    }
    return new HttpResponse(null, { status: 204 });
  });
};
export const getCardBotMock = () => [
  getGetCardBotIndexMockHandler(),
  getCreateCardBotMockHandler(),
  getPairCardBotMockHandler(),
  getGetCardBotMessageMockHandler(),
  getSendCardBotMessageMockHandler(),
  getReceiveCardBotMessageMockHandler(),
  getSendMessageToBackendMockHandler(),
  getGetCardBotStatusMockHandler(),
  getGetDeviceMockHandler(),
  getUpdateCardBotDisabledMockHandler(),
  getLinkUserToCardBotMockHandler(),
  getUnlinkUserToCardBotMockHandler(),
];
