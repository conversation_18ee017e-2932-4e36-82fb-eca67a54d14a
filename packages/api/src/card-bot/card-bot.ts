/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import { useMutation, useQuery } from '@tanstack/react-query';
import type {
  MutationFunction,
  QueryFunction,
  QueryKey,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult,
} from '@tanstack/react-query';

import axios from 'axios';
import type { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios';

import type {
  CreateCardBot200,
  CreateCardBotParams,
  GetCardBotIndex200Item,
  GetCardBotMessage200,
  GetCardBotStatusParams,
  GetDevice200,
  LinkUserToCardBotBody,
  PairCardBotBody,
  ReceiveCardBotMessage200,
  SendCardBotMessageBody,
  SendMessageToBackendBody,
  UpdateCardBotDisabledParams,
} from '.././model';

type AwaitedInput<T> = PromiseLike<T> | T;

type Awaited<O> = O extends AwaitedInput<infer T> ? T : never;

/**
 * Get All CardBots
 * @summary Get All CardBots
 */
export const getCardBotIndex = (
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<GetCardBotIndex200Item[]>> => {
  return axios.get(`/card_bots`, options);
};

export const getGetCardBotIndexQueryKey = () => {
  return [`/card_bots`] as const;
};

export const getGetCardBotIndexQueryOptions = <
  TData = Awaited<ReturnType<typeof getCardBotIndex>>,
  TError = AxiosError<unknown>,
>(options?: {
  query?: UseQueryOptions<
    Awaited<ReturnType<typeof getCardBotIndex>>,
    TError,
    TData
  >;
  axios?: AxiosRequestConfig;
}) => {
  const { query: queryOptions, axios: axiosOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetCardBotIndexQueryKey();

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getCardBotIndex>>> = ({
    signal,
  }) => getCardBotIndex({ signal, ...axiosOptions });

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getCardBotIndex>>,
    TError,
    TData
  > & { queryKey: QueryKey };
};

export type GetCardBotIndexQueryResult = NonNullable<
  Awaited<ReturnType<typeof getCardBotIndex>>
>;
export type GetCardBotIndexQueryError = AxiosError<unknown>;

/**
 * @summary Get All CardBots
 */

export function useGetCardBotIndex<
  TData = Awaited<ReturnType<typeof getCardBotIndex>>,
  TError = AxiosError<unknown>,
>(options?: {
  query?: UseQueryOptions<
    Awaited<ReturnType<typeof getCardBotIndex>>,
    TError,
    TData
  >;
  axios?: AxiosRequestConfig;
}): UseQueryResult<TData, TError> & { queryKey: QueryKey } {
  const queryOptions = getGetCardBotIndexQueryOptions(options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: QueryKey;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Create a CardBot
 * @summary Create CardBot
 */
export const createCardBot = (
  createCardBotBody: string,
  params: CreateCardBotParams,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<CreateCardBot200>> => {
  return axios.post(`/card_bots`, createCardBotBody, {
    ...options,
    params: { ...params, ...options?.params },
  });
};

export const getCreateCardBotMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof createCardBot>>,
    TError,
    { data: string; params: CreateCardBotParams },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof createCardBot>>,
  TError,
  { data: string; params: CreateCardBotParams },
  TContext
> => {
  const mutationKey = ['createCardBot'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof createCardBot>>,
    { data: string; params: CreateCardBotParams }
  > = (props) => {
    const { data, params } = props ?? {};

    return createCardBot(data, params, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type CreateCardBotMutationResult = NonNullable<
  Awaited<ReturnType<typeof createCardBot>>
>;
export type CreateCardBotMutationBody = string;
export type CreateCardBotMutationError = AxiosError<unknown>;

/**
 * @summary Create CardBot
 */
export const useCreateCardBot = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof createCardBot>>,
    TError,
    { data: string; params: CreateCardBotParams },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof createCardBot>>,
  TError,
  { data: string; params: CreateCardBotParams },
  TContext
> => {
  const mutationOptions = getCreateCardBotMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Send CardBot Pairing Request
 * @summary Pair CardBot
 */
export const pairCardBot = (
  pairCardBotBody: PairCardBotBody,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<void>> => {
  return axios.post(`/card_bots/pair`, pairCardBotBody, options);
};

export const getPairCardBotMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof pairCardBot>>,
    TError,
    { data: PairCardBotBody },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof pairCardBot>>,
  TError,
  { data: PairCardBotBody },
  TContext
> => {
  const mutationKey = ['pairCardBot'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof pairCardBot>>,
    { data: PairCardBotBody }
  > = (props) => {
    const { data } = props ?? {};

    return pairCardBot(data, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type PairCardBotMutationResult = NonNullable<
  Awaited<ReturnType<typeof pairCardBot>>
>;
export type PairCardBotMutationBody = PairCardBotBody;
export type PairCardBotMutationError = AxiosError<unknown>;

/**
 * @summary Pair CardBot
 */
export const usePairCardBot = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof pairCardBot>>,
    TError,
    { data: PairCardBotBody },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof pairCardBot>>,
  TError,
  { data: PairCardBotBody },
  TContext
> => {
  const mutationOptions = getPairCardBotMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Get Message for CardBot
 * @summary Get Message for CardBot
 */
export const getCardBotMessage = (
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<GetCardBotMessage200>> => {
  return axios.get(`/card_bots/command`, options);
};

export const getGetCardBotMessageQueryKey = () => {
  return [`/card_bots/command`] as const;
};

export const getGetCardBotMessageQueryOptions = <
  TData = Awaited<ReturnType<typeof getCardBotMessage>>,
  TError = AxiosError<unknown>,
>(options?: {
  query?: UseQueryOptions<
    Awaited<ReturnType<typeof getCardBotMessage>>,
    TError,
    TData
  >;
  axios?: AxiosRequestConfig;
}) => {
  const { query: queryOptions, axios: axiosOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetCardBotMessageQueryKey();

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof getCardBotMessage>>
  > = ({ signal }) => getCardBotMessage({ signal, ...axiosOptions });

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getCardBotMessage>>,
    TError,
    TData
  > & { queryKey: QueryKey };
};

export type GetCardBotMessageQueryResult = NonNullable<
  Awaited<ReturnType<typeof getCardBotMessage>>
>;
export type GetCardBotMessageQueryError = AxiosError<unknown>;

/**
 * @summary Get Message for CardBot
 */

export function useGetCardBotMessage<
  TData = Awaited<ReturnType<typeof getCardBotMessage>>,
  TError = AxiosError<unknown>,
>(options?: {
  query?: UseQueryOptions<
    Awaited<ReturnType<typeof getCardBotMessage>>,
    TError,
    TData
  >;
  axios?: AxiosRequestConfig;
}): UseQueryResult<TData, TError> & { queryKey: QueryKey } {
  const queryOptions = getGetCardBotMessageQueryOptions(options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: QueryKey;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Send Message to CardBot
 * @summary Send Message to CardBot
 */
export const sendCardBotMessage = (
  sendCardBotMessageBody: SendCardBotMessageBody,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<void>> => {
  return axios.post(`/card_bots/command`, sendCardBotMessageBody, options);
};

export const getSendCardBotMessageMutationOptions = <
  TError = AxiosError<void>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof sendCardBotMessage>>,
    TError,
    { data: SendCardBotMessageBody },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof sendCardBotMessage>>,
  TError,
  { data: SendCardBotMessageBody },
  TContext
> => {
  const mutationKey = ['sendCardBotMessage'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof sendCardBotMessage>>,
    { data: SendCardBotMessageBody }
  > = (props) => {
    const { data } = props ?? {};

    return sendCardBotMessage(data, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type SendCardBotMessageMutationResult = NonNullable<
  Awaited<ReturnType<typeof sendCardBotMessage>>
>;
export type SendCardBotMessageMutationBody = SendCardBotMessageBody;
export type SendCardBotMessageMutationError = AxiosError<void>;

/**
 * @summary Send Message to CardBot
 */
export const useSendCardBotMessage = <
  TError = AxiosError<void>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof sendCardBotMessage>>,
    TError,
    { data: SendCardBotMessageBody },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof sendCardBotMessage>>,
  TError,
  { data: SendCardBotMessageBody },
  TContext
> => {
  const mutationOptions = getSendCardBotMessageMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Get Message From CardBot
 * @summary Get Message From CardBot
 */
export const receiveCardBotMessage = (
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<ReceiveCardBotMessage200 | void>> => {
  return axios.get(`/card_bots/report`, options);
};

export const getReceiveCardBotMessageQueryKey = () => {
  return [`/card_bots/report`] as const;
};

export const getReceiveCardBotMessageQueryOptions = <
  TData = Awaited<ReturnType<typeof receiveCardBotMessage>>,
  TError = AxiosError<unknown>,
>(options?: {
  query?: UseQueryOptions<
    Awaited<ReturnType<typeof receiveCardBotMessage>>,
    TError,
    TData
  >;
  axios?: AxiosRequestConfig;
}) => {
  const { query: queryOptions, axios: axiosOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getReceiveCardBotMessageQueryKey();

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof receiveCardBotMessage>>
  > = ({ signal }) => receiveCardBotMessage({ signal, ...axiosOptions });

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof receiveCardBotMessage>>,
    TError,
    TData
  > & { queryKey: QueryKey };
};

export type ReceiveCardBotMessageQueryResult = NonNullable<
  Awaited<ReturnType<typeof receiveCardBotMessage>>
>;
export type ReceiveCardBotMessageQueryError = AxiosError<unknown>;

/**
 * @summary Get Message From CardBot
 */

export function useReceiveCardBotMessage<
  TData = Awaited<ReturnType<typeof receiveCardBotMessage>>,
  TError = AxiosError<unknown>,
>(options?: {
  query?: UseQueryOptions<
    Awaited<ReturnType<typeof receiveCardBotMessage>>,
    TError,
    TData
  >;
  axios?: AxiosRequestConfig;
}): UseQueryResult<TData, TError> & { queryKey: QueryKey } {
  const queryOptions = getReceiveCardBotMessageQueryOptions(options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: QueryKey;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Send Message to Backend
 * @summary Send Message To Backend
 */
export const sendMessageToBackend = (
  sendMessageToBackendBody: SendMessageToBackendBody,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<void>> => {
  return axios.post(`/card_bots/report`, sendMessageToBackendBody, options);
};

export const getSendMessageToBackendMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof sendMessageToBackend>>,
    TError,
    { data: SendMessageToBackendBody },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof sendMessageToBackend>>,
  TError,
  { data: SendMessageToBackendBody },
  TContext
> => {
  const mutationKey = ['sendMessageToBackend'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof sendMessageToBackend>>,
    { data: SendMessageToBackendBody }
  > = (props) => {
    const { data } = props ?? {};

    return sendMessageToBackend(data, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type SendMessageToBackendMutationResult = NonNullable<
  Awaited<ReturnType<typeof sendMessageToBackend>>
>;
export type SendMessageToBackendMutationBody = SendMessageToBackendBody;
export type SendMessageToBackendMutationError = AxiosError<unknown>;

/**
 * @summary Send Message To Backend
 */
export const useSendMessageToBackend = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof sendMessageToBackend>>,
    TError,
    { data: SendMessageToBackendBody },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof sendMessageToBackend>>,
  TError,
  { data: SendMessageToBackendBody },
  TContext
> => {
  const mutationOptions = getSendMessageToBackendMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Update CardBot Status Information
 * @summary Update CardBot Status
 */
export const getCardBotStatus = (
  params?: GetCardBotStatusParams,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<void>> => {
  return axios.post(`/card_bots/status`, undefined, {
    ...options,
    params: { ...params, ...options?.params },
  });
};

export const getGetCardBotStatusMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof getCardBotStatus>>,
    TError,
    { params?: GetCardBotStatusParams },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof getCardBotStatus>>,
  TError,
  { params?: GetCardBotStatusParams },
  TContext
> => {
  const mutationKey = ['getCardBotStatus'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof getCardBotStatus>>,
    { params?: GetCardBotStatusParams }
  > = (props) => {
    const { params } = props ?? {};

    return getCardBotStatus(params, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type GetCardBotStatusMutationResult = NonNullable<
  Awaited<ReturnType<typeof getCardBotStatus>>
>;

export type GetCardBotStatusMutationError = AxiosError<unknown>;

/**
 * @summary Update CardBot Status
 */
export const useGetCardBotStatus = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof getCardBotStatus>>,
    TError,
    { params?: GetCardBotStatusParams },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof getCardBotStatus>>,
  TError,
  { params?: GetCardBotStatusParams },
  TContext
> => {
  const mutationOptions = getGetCardBotStatusMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Get Device
 * @summary Get Device
 */
export const getDevice = (
  deviceId: string,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<GetDevice200>> => {
  return axios.get(`/card_bots/${deviceId}`, options);
};

export const getGetDeviceQueryKey = (deviceId: string) => {
  return [`/card_bots/${deviceId}`] as const;
};

export const getGetDeviceQueryOptions = <
  TData = Awaited<ReturnType<typeof getDevice>>,
  TError = AxiosError<unknown>,
>(
  deviceId: string,
  options?: {
    query?: UseQueryOptions<
      Awaited<ReturnType<typeof getDevice>>,
      TError,
      TData
    >;
    axios?: AxiosRequestConfig;
  },
) => {
  const { query: queryOptions, axios: axiosOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetDeviceQueryKey(deviceId);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getDevice>>> = ({
    signal,
  }) => getDevice(deviceId, { signal, ...axiosOptions });

  return {
    queryKey,
    queryFn,
    enabled: !!deviceId,
    ...queryOptions,
  } as UseQueryOptions<Awaited<ReturnType<typeof getDevice>>, TError, TData> & {
    queryKey: QueryKey;
  };
};

export type GetDeviceQueryResult = NonNullable<
  Awaited<ReturnType<typeof getDevice>>
>;
export type GetDeviceQueryError = AxiosError<unknown>;

/**
 * @summary Get Device
 */

export function useGetDevice<
  TData = Awaited<ReturnType<typeof getDevice>>,
  TError = AxiosError<unknown>,
>(
  deviceId: string,
  options?: {
    query?: UseQueryOptions<
      Awaited<ReturnType<typeof getDevice>>,
      TError,
      TData
    >;
    axios?: AxiosRequestConfig;
  },
): UseQueryResult<TData, TError> & { queryKey: QueryKey } {
  const queryOptions = getGetDeviceQueryOptions(deviceId, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: QueryKey;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Update CardBot disabled status
 * @summary Update CardBot disabled status
 */
export const updateCardBotDisabled = (
  deviceId: string,
  params: UpdateCardBotDisabledParams,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<void>> => {
  return axios.patch(`/card_bots/${deviceId}`, undefined, {
    ...options,
    params: { ...params, ...options?.params },
  });
};

export const getUpdateCardBotDisabledMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof updateCardBotDisabled>>,
    TError,
    { deviceId: string; params: UpdateCardBotDisabledParams },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof updateCardBotDisabled>>,
  TError,
  { deviceId: string; params: UpdateCardBotDisabledParams },
  TContext
> => {
  const mutationKey = ['updateCardBotDisabled'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof updateCardBotDisabled>>,
    { deviceId: string; params: UpdateCardBotDisabledParams }
  > = (props) => {
    const { deviceId, params } = props ?? {};

    return updateCardBotDisabled(deviceId, params, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type UpdateCardBotDisabledMutationResult = NonNullable<
  Awaited<ReturnType<typeof updateCardBotDisabled>>
>;

export type UpdateCardBotDisabledMutationError = AxiosError<unknown>;

/**
 * @summary Update CardBot disabled status
 */
export const useUpdateCardBotDisabled = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof updateCardBotDisabled>>,
    TError,
    { deviceId: string; params: UpdateCardBotDisabledParams },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof updateCardBotDisabled>>,
  TError,
  { deviceId: string; params: UpdateCardBotDisabledParams },
  TContext
> => {
  const mutationOptions = getUpdateCardBotDisabledMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Link User to CardBot
 * @summary Link User to CardBots
 */
export const linkUserToCardBot = (
  deviceId: string,
  linkUserToCardBotBody: LinkUserToCardBotBody,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<void>> => {
  return axios.patch(
    `/card_bots/${deviceId}/link`,
    linkUserToCardBotBody,
    options,
  );
};

export const getLinkUserToCardBotMutationOptions = <
  TError = AxiosError<void>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof linkUserToCardBot>>,
    TError,
    { deviceId: string; data: LinkUserToCardBotBody },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof linkUserToCardBot>>,
  TError,
  { deviceId: string; data: LinkUserToCardBotBody },
  TContext
> => {
  const mutationKey = ['linkUserToCardBot'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof linkUserToCardBot>>,
    { deviceId: string; data: LinkUserToCardBotBody }
  > = (props) => {
    const { deviceId, data } = props ?? {};

    return linkUserToCardBot(deviceId, data, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type LinkUserToCardBotMutationResult = NonNullable<
  Awaited<ReturnType<typeof linkUserToCardBot>>
>;
export type LinkUserToCardBotMutationBody = LinkUserToCardBotBody;
export type LinkUserToCardBotMutationError = AxiosError<void>;

/**
 * @summary Link User to CardBots
 */
export const useLinkUserToCardBot = <
  TError = AxiosError<void>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof linkUserToCardBot>>,
    TError,
    { deviceId: string; data: LinkUserToCardBotBody },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof linkUserToCardBot>>,
  TError,
  { deviceId: string; data: LinkUserToCardBotBody },
  TContext
> => {
  const mutationOptions = getLinkUserToCardBotMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Unlink User to CardBots
 * @summary Unlink User to CardBots
 */
export const unlinkUserToCardBot = (
  deviceId: string,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<void>> => {
  return axios.patch(`/card_bots/${deviceId}/unlink`, undefined, options);
};

export const getUnlinkUserToCardBotMutationOptions = <
  TError = AxiosError<void>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof unlinkUserToCardBot>>,
    TError,
    { deviceId: string },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof unlinkUserToCardBot>>,
  TError,
  { deviceId: string },
  TContext
> => {
  const mutationKey = ['unlinkUserToCardBot'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof unlinkUserToCardBot>>,
    { deviceId: string }
  > = (props) => {
    const { deviceId } = props ?? {};

    return unlinkUserToCardBot(deviceId, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type UnlinkUserToCardBotMutationResult = NonNullable<
  Awaited<ReturnType<typeof unlinkUserToCardBot>>
>;

export type UnlinkUserToCardBotMutationError = AxiosError<void>;

/**
 * @summary Unlink User to CardBots
 */
export const useUnlinkUserToCardBot = <
  TError = AxiosError<void>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof unlinkUserToCardBot>>,
    TError,
    { deviceId: string },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof unlinkUserToCardBot>>,
  TError,
  { deviceId: string },
  TContext
> => {
  const mutationOptions = getUnlinkUserToCardBotMutationOptions(options);

  return useMutation(mutationOptions);
};
