/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import type { SessionsClearStagedSourceType } from './sessionsClearStagedSourceType';
import type { SessionsClearStagedGame } from './sessionsClearStagedGame';

export type SessionsClearStagedParams = {
  source_type?: SessionsClearStagedSourceType;
  /**
   * Selects all sources that include the query string in their name
   */
  source_name?: string;
  game?: SessionsClearStagedGame;
};
