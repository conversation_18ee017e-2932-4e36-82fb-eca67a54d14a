/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */

export type SearchYugiohCollectionGroupBy =
  (typeof SearchYugiohCollectionGroupBy)[keyof typeof SearchYugiohCollectionGroupBy];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const SearchYugiohCollectionGroupBy = {
  printing: 'printing',
  name: 'name',
  none: 'none',
} as const;
