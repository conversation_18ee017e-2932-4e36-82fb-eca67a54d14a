/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import type { UpdatePokeCardUser200CardUsersItem } from './updatePokeCardUser200CardUsersItem';
import type { UpdatePokeCardUser200Cards } from './updatePokeCardUser200Cards';

export type UpdatePokeCardUser200 = {
  card_users: UpdatePokeCardUser200CardUsersItem[];
  cards?: UpdatePokeCardUser200Cards;
};
