/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import type { ShowCardList200CardListListedCardsItem } from './showCardList200CardListListedCardsItem';
import type { ShowCardList200CardListCards } from './showCardList200CardListCards';

export type ShowCardList200CardList = {
  name?: string;
  id?: number;
  count?: number;
  created_at?: string;
  listed_cards?: ShowCardList200CardListListedCardsItem[];
  cards?: ShowCardList200CardListCards;
};
