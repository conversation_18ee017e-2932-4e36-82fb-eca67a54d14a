/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import type { UpdatePokeCardUserCondition } from './updatePokeCardUserCondition';

export type UpdatePokeCardUserParams = {
  uuids: string[];
  card_ids: string[];
  condition?: UpdatePokeCardUserCondition;
  /**
   * Create or update scan metadata with updated card info
   */
  annotate_scan?: boolean;
  finish_uuid?: string;
  /**
   * Update printing of all poke card users. The new printing must be valid for all or the request will fail
   */
  printing?: string;
};
