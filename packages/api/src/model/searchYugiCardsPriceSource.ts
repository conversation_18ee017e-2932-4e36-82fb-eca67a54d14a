/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */

export type SearchYugiCardsPriceSource =
  (typeof SearchYugiCardsPriceSource)[keyof typeof SearchYugiCardsPriceSource];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const SearchYugiCardsPriceSource = {
  tcg_player: 'tcg_player',
  card_kingdom: 'card_kingdom',
  card_market: 'card_market',
} as const;
