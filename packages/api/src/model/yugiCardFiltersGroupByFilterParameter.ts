/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */

/**
 * printing groups by yugi card set
 */
export type YugiCardFiltersGroupByFilterParameter =
  (typeof YugiCardFiltersGroupByFilterParameter)[keyof typeof YugiCardFiltersGroupByFilterParameter];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const YugiCardFiltersGroupByFilterParameter = {
  printing: 'printing',
  name: 'name',
  none: 'none',
} as const;
