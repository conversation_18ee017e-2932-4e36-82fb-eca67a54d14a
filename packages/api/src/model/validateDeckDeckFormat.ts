/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */

export type ValidateDeckDeckFormat =
  (typeof ValidateDeckDeckFormat)[keyof typeof ValidateDeckDeckFormat];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const ValidateDeckDeckFormat = {
  brawl: 'brawl',
  commander: 'commander',
  duel: 'duel',
  freeform: 'freeform',
  legacy: 'legacy',
  modern: 'modern',
  oldschool: 'oldschool',
  pauper: 'pauper',
  pioneer: 'pioneer',
  premodern: 'premodern',
  standard: 'standard',
  tiny_leaders: 'tiny leaders',
  vintage: 'vintage',
} as const;
