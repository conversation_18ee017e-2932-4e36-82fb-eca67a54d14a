/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import type { UpdateYugiCardInstanceResponseCardInstancesItem } from './updateYugiCardInstanceResponseCardInstancesItem';
import type { UpdateYugiCardInstanceResponseCards } from './updateYugiCardInstanceResponseCards';
import type { UpdateYugiCardInstanceResponseSets } from './updateYugiCardInstanceResponseSets';

export interface UpdateYugiCardInstanceResponse {
  card_instances: UpdateYugiCardInstanceResponseCardInstancesItem[];
  cards?: UpdateYugiCardInstanceResponseCards;
  sets?: UpdateYugiCardInstanceResponseSets;
}
