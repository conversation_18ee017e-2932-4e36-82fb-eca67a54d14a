/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */

export type SearchPokemonCollectionGroupBy =
  (typeof SearchPokemonCollectionGroupBy)[keyof typeof SearchPokemonCollectionGroupBy];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const SearchPokemonCollectionGroupBy = {
  printing: 'printing',
  name: 'name',
  none: 'none',
} as const;
