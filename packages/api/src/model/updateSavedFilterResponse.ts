/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import type { UpdateSavedFilterResponsePayload } from './updateSavedFilterResponsePayload';

export interface UpdateSavedFilterResponse {
  name?: string;
  uuid?: string;
  created_at?: string;
  payload?: UpdateSavedFilterResponsePayload;
}
