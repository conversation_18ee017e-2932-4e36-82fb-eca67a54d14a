/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import type { UpdateUserDetails200Avatar } from './updateUserDetails200Avatar';
import type { UpdateUserDetails200Preferences } from './updateUserDetails200Preferences';

export type UpdateUserDetails200 = {
  id: number;
  username: string;
  email: string;
  name: string;
  created_at: string;
  /** @nullable */
  confirmed_at: string | null;
  /** @nullable */
  twitter: string | null;
  /** @nullable */
  facebook: string | null;
  referral_link: string;
  /** @nullable */
  share_link: string | null;
  subscription: boolean;
  avatar: UpdateUserDetails200Avatar;
  preferences: UpdateUserDetails200Preferences;
  user_hash: string;
  ios_user_hash: string;
  android_user_hash: string;
};
