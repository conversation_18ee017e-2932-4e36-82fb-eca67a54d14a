/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */

export type UpdateLorcanaCardInstanceCondition =
  (typeof UpdateLorcanaCardInstanceCondition)[keyof typeof UpdateLorcanaCardInstanceCondition];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const UpdateLorcanaCardInstanceCondition = {
  Near_Mint: 'Near Mint',
  Lightly_Played: 'Lightly Played',
  Moderately_Played: 'Moderately Played',
  Heavily_Played: 'Heavily Played',
  Damaged: 'Damaged',
} as const;
