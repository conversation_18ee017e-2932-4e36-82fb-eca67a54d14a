/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import type { UpdateCardDeckResponseCardDeck } from './updateCardDeckResponseCardDeck';
import type { UpdateCardDeckResponseCard } from './updateCardDeckResponseCard';

export interface UpdateCardDeckResponse {
  card_deck: UpdateCardDeckResponseCardDeck;
  card: UpdateCardDeckResponseCard;
}
