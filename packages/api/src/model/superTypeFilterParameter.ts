/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */

/**
 * As `poke_cards` only have 1 `super_type`, all values are filtered using OR logic
 */
export type SuperTypeFilterParameter = typeof SuperTypeFilterParameter[keyof typeof SuperTypeFilterParameter];


// eslint-disable-next-line @typescript-eslint/no-redeclare
export const SuperTypeFilterParameter = {
  '[object_Object]': [object Object],
} as const;
