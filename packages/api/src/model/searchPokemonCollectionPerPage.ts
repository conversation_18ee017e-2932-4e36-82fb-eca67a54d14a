/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */

export type SearchPokemonCollectionPerPage =
  (typeof SearchPokemonCollectionPerPage)[keyof typeof SearchPokemonCollectionPerPage];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const SearchPokemonCollectionPerPage = {
  NUMBER_100: 100,
  NUMBER_300: 300,
  NUMBER_600: 600,
} as const;
