/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */

export type SearchPokemonCollectionSource =
  (typeof SearchPokemonCollectionSource)[keyof typeof SearchPokemonCollectionSource];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const SearchPokemonCollectionSource = {
  cardbot: 'cardbot',
  app: 'app',
  manual: 'manual',
  import: 'import',
  unknown: 'unknown',
} as const;
