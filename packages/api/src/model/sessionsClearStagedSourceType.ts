/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */

export type SessionsClearStagedSourceType =
  (typeof SessionsClearStagedSourceType)[keyof typeof SessionsClearStagedSourceType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const SessionsClearStagedSourceType = {
  cardbot: 'cardbot',
  app: 'app',
  manual: 'manual',
  import: 'import',
  unknown: 'unknown',
} as const;
