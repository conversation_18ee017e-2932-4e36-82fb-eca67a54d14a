/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */

export type SearchPokemonCollectionQuantity = typeof SearchPokemonCollectionQuantity[keyof typeof SearchPokemonCollectionQuantity];


// eslint-disable-next-line @typescript-eslint/no-redeclare
export const SearchPokemonCollectionQuantity = {
  '[object_Object]': [object Object],
} as const;
