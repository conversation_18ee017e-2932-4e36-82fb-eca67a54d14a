/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import type { SearchYugiohCollectionSortBy } from './searchYugiohCollectionSortBy';
import type { SearchYugiohCollectionQuantity } from './searchYugiohCollectionQuantity';
import type { SearchYugiohCollectionConditionItem } from './searchYugiohCollectionConditionItem';
import type { SearchYugiohCollectionConfidence } from './searchYugiohCollectionConfidence';
import type { SearchYugiohCollectionOrder } from './searchYugiohCollectionOrder';
import type { SearchYugiohCollectionPriceSource } from './searchYugiohCollectionPriceSource';
import type { SearchYugiohCollectionGroupBy } from './searchYugiohCollectionGroupBy';
import type { SearchYugiohCollectionPerPage } from './searchYugiohCollectionPerPage';
import type { SearchYugiohCollectionFieldsSummaryItem } from './searchYugiohCollectionFieldsSummaryItem';
import type { SearchYugiohCollectionFieldsGroupItem } from './searchYugiohCollectionFieldsGroupItem';
import type { SearchYugiohCollectionFieldsCardItem } from './searchYugiohCollectionFieldsCardItem';
import type { SearchYugiohCollectionFieldsElementItem } from './searchYugiohCollectionFieldsElementItem';

export type SearchYugiohCollectionParams = {
  sort_by?: SearchYugiohCollectionSortBy;
  quantity?: SearchYugiohCollectionQuantity;
  input_session?: string;
  condition?: SearchYugiohCollectionConditionItem[];
  confidence?: SearchYugiohCollectionConfidence;
  order?: SearchYugiohCollectionOrder;
  /**
   * when no source is passed, price source defaults to user preference
   */
  price?: {
    source?: SearchYugiohCollectionPriceSource;
    /** @minimum 0 */
    max?: number;
    /** @minimum 0 */
    min?: number;
  };
  name?: string;
  query?: string;
  group_by?: SearchYugiohCollectionGroupBy;
  per_page?: SearchYugiohCollectionPerPage;
  page?: number;
  /**
   * Request specific fields to be included in the response. All fields are returned by default, but it's recommended that you use this parameter to specify only the fields you need to reduce the size of the response.
   */
  fields?: {
    summary?: SearchYugiohCollectionFieldsSummaryItem[];
    group?: SearchYugiohCollectionFieldsGroupItem[];
    card?: SearchYugiohCollectionFieldsCardItem[];
    element?: SearchYugiohCollectionFieldsElementItem[];
  };
};
