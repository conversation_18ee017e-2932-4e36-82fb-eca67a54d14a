/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import type { UpdateUserAvatarResponseAvatar } from './updateUserAvatarResponseAvatar';

export interface UpdateUserAvatarResponse {
  id: number;
  username: string;
  email: string;
  name: string;
  created_at: string;
  /** @nullable */
  twitter: string | null;
  /** @nullable */
  facebook: string | null;
  referral_link: string;
  avatar: UpdateUserAvatarResponseAvatar;
}
