/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */

export type SearchPokemonCollectionFieldsElementItem =
  (typeof SearchPokemonCollectionFieldsElementItem)[keyof typeof SearchPokemonCollectionFieldsElementItem];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const SearchPokemonCollectionFieldsElementItem = {
  source_type: 'source_type',
  scanned_image_url: 'scanned_image_url',
  price: 'price',
  created_at: 'created_at',
  committed_at: 'committed_at',
} as const;
