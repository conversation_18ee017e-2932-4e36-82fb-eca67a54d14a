/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import type { SearchLorcanaCollectionQuantity } from './searchLorcanaCollectionQuantity';
import type { SearchLorcanaCollectionSource } from './searchLorcanaCollectionSource';
import type { SearchLorcanaCollectionConditionItem } from './searchLorcanaCollectionConditionItem';
import type { SearchLorcanaCollectionSortBy } from './searchLorcanaCollectionSortBy';
import type { SearchLorcanaCollectionConfidence } from './searchLorcanaCollectionConfidence';
import type { SearchLorcanaCollectionGroupBy } from './searchLorcanaCollectionGroupBy';
import type { SearchLorcanaCollectionOrder } from './searchLorcanaCollectionOrder';
import type { SearchLorcanaCollectionPerPage } from './searchLorcanaCollectionPerPage';
import type { SearchLorcanaCollectionTypes } from './searchLorcanaCollectionTypes';
import type { SearchLorcanaCollectionClassifications } from './searchLorcanaCollectionClassifications';
import type { SearchLorcanaCollectionArtist } from './searchLorcanaCollectionArtist';
import type { SearchLorcanaCollectionSetNames } from './searchLorcanaCollectionSetNames';
import type { SearchLorcanaCollectionStrength } from './searchLorcanaCollectionStrength';
import type { SearchLorcanaCollectionLore } from './searchLorcanaCollectionLore';
import type { SearchLorcanaCollectionWillpower } from './searchLorcanaCollectionWillpower';
import type { SearchLorcanaCollectionMoveCost } from './searchLorcanaCollectionMoveCost';
import type { SearchLorcanaCollectionCost } from './searchLorcanaCollectionCost';
import type { SearchLorcanaCollectionPrice } from './searchLorcanaCollectionPrice';
import type { SearchLorcanaCollectionRarityFilterItem } from './searchLorcanaCollectionRarityFilterItem';
import type { SearchLorcanaCollectionFieldsSummaryItem } from './searchLorcanaCollectionFieldsSummaryItem';
import type { SearchLorcanaCollectionFieldsGroupItem } from './searchLorcanaCollectionFieldsGroupItem';
import type { SearchLorcanaCollectionFieldsCardItem } from './searchLorcanaCollectionFieldsCardItem';
import type { SearchLorcanaCollectionFieldsElementItem } from './searchLorcanaCollectionFieldsElementItem';

export type SearchLorcanaCollectionParams = {
  quantity?: SearchLorcanaCollectionQuantity;
  source?: SearchLorcanaCollectionSource;
  input_session?: string;
  condition?: SearchLorcanaCollectionConditionItem[];
  sort_by?: SearchLorcanaCollectionSortBy;
  confidence?: SearchLorcanaCollectionConfidence;
  group_by?: SearchLorcanaCollectionGroupBy;
  order?: SearchLorcanaCollectionOrder;
  name?: string;
  query?: string;
  rules_text?: string;
  flavor_text?: string;
  layout?: string;
  version?: string;
  ink?: string;
  inkwell?: string;
  collector_number?: string;
  per_page?: SearchLorcanaCollectionPerPage;
  page?: number;
  /**
   * Accepted types can be retrieved from the `/types` route.
   */
  types?: SearchLorcanaCollectionTypes;
  classifications?: SearchLorcanaCollectionClassifications;
  artist?: SearchLorcanaCollectionArtist;
  set_names?: SearchLorcanaCollectionSetNames;
  /**
   * matches the first letter (case insensitive) of a lorcana card's name
   */
  starts_with?: string;
  /**
   * matches the first letter (case insensitive) of a lorcana card's set name
   */
  set_starts_with?: string;
  strength?: SearchLorcanaCollectionStrength;
  lore?: SearchLorcanaCollectionLore;
  willpower?: SearchLorcanaCollectionWillpower;
  move_cost?: SearchLorcanaCollectionMoveCost;
  cost?: SearchLorcanaCollectionCost;
  price?: SearchLorcanaCollectionPrice;
  rarity_filter?: SearchLorcanaCollectionRarityFilterItem[];
  /**
   * Request specific fields to be included in the response. All fields are returned by default, but it's recommended that you use this parameter to specify only the fields you need to reduce the size of the response.
   */
  fields?: {
    summary?: SearchLorcanaCollectionFieldsSummaryItem[];
    group?: SearchLorcanaCollectionFieldsGroupItem[];
    card?: SearchLorcanaCollectionFieldsCardItem[];
    element?: SearchLorcanaCollectionFieldsElementItem[];
  };
};
