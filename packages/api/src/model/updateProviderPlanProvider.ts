/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */

export type UpdateProviderPlanProvider =
  (typeof UpdateProviderPlanProvider)[keyof typeof UpdateProviderPlanProvider];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const UpdateProviderPlanProvider = {
  stripe: 'stripe',
  paypal: 'paypal',
  'paypal-v2': 'paypal-v2',
} as const;
