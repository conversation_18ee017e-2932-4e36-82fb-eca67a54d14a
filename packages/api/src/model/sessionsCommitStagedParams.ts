/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import type { SessionsCommitStagedSourceType } from './sessionsCommitStagedSourceType';
import type { SessionsCommitStagedGame } from './sessionsCommitStagedGame';

export type SessionsCommitStagedParams = {
  source_type?: SessionsCommitStagedSourceType;
  /**
   * Selects all sources that include the query string in their name
   */
  source_name?: string;
  game?: SessionsCommitStagedGame;
};
