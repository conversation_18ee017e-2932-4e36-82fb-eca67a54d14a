/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */

export type YugiCardFieldsItem =
  (typeof YugiCardFieldsItem)[keyof typeof YugiCardFieldsItem];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const YugiCardFieldsItem = {
  name: 'name',
  ygoprodeck_id: 'ygoprodeck_id',
  card_type: 'card_type',
  frame_type: 'frame_type',
  description: 'description',
  attack: 'attack',
  defense: 'defense',
  level: 'level',
  race: 'race',
  archetype: 'archetype',
  printings: 'printings',
  card_sets: 'card_sets',
  min_price: 'min_price',
  avg_price: 'avg_price',
  max_price: 'max_price',
} as const;
