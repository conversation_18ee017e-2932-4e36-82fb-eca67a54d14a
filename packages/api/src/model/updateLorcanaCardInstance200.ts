/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import type { UpdateLorcanaCardInstance200CardInstancesItem } from './updateLorcanaCardInstance200CardInstancesItem';
import type { UpdateLorcanaCardInstance200Cards } from './updateLorcanaCardInstance200Cards';

export type UpdateLorcanaCardInstance200 = {
  card_instances: UpdateLorcanaCardInstance200CardInstancesItem[];
  cards: UpdateLorcanaCardInstance200Cards;
};
