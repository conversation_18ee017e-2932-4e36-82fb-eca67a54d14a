/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import type { UpdateUserPrefs200Avatar } from './updateUserPrefs200Avatar';
import type { UpdateUserPrefs200Preferences } from './updateUserPrefs200Preferences';

export type UpdateUserPrefs200 = {
  id: number;
  username: string;
  email: string;
  name: string;
  created_at: string;
  /** @nullable */
  confirmed_at: string | null;
  /** @nullable */
  twitter: string | null;
  /** @nullable */
  facebook: string | null;
  referral_link: string;
  /** @nullable */
  share_link: string | null;
  subscription: boolean;
  avatar: UpdateUserPrefs200Avatar;
  preferences: UpdateUserPrefs200Preferences;
  user_hash: string;
  ios_user_hash: string;
  android_user_hash: string;
};
