/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import type { SearchYugiCardsOrder } from './searchYugiCardsOrder';
import type { SearchYugiCardsGroupBy } from './searchYugiCardsGroupBy';
import type { SearchYugiCardsPerPage } from './searchYugiCardsPerPage';
import type { SearchYugiCardsSetNamesOneOf } from './searchYugiCardsSetNamesOneOf';
import type { SearchYugiCardsSetNamesOneOfThree } from './searchYugiCardsSetNamesOneOfThree';
import type { SearchYugiCardsPriceSource } from './searchYugiCardsPriceSource';
import type { SearchYugiCardsFieldsItem } from './searchYugiCardsFieldsItem';

export type SearchYugiCardsParams = {
  order?: SearchYugiCardsOrder;
  group_by?: SearchYugiCardsGroupBy;
  name?: string;
  per_page?: SearchYugiCardsPerPage;
  query?: string;
  page?: number;
  set_names?: SearchYugiCardsSetNamesOneOf | SearchYugiCardsSetNamesOneOfThree;
  /**
   * when no source is passed, price source defaults to user preference
   */
  price?: {
    source?: SearchYugiCardsPriceSource;
    /** @minimum 0 */
    max?: number;
    /** @minimum 0 */
    min?: number;
  };
  printing_uuid?: string;
  race?: string;
  archetype?: string;
  /**
   * matches the first letter (case insensitive) of a yugi card's name
   */
  starts_with?: string;
  /**
   * Request specific fields to be included in the response. All fields are returned by default, but it's recommended that you use this parameter to specify only the fields you need to reduce the size of the response.
   */
  fields?: SearchYugiCardsFieldsItem[];
};
