/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */

export type SearchLorcanaCollectionFieldsSummaryItem =
  (typeof SearchLorcanaCollectionFieldsSummaryItem)[keyof typeof SearchLorcanaCollectionFieldsSummaryItem];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const SearchLorcanaCollectionFieldsSummaryItem = {
  page_value: 'page_value',
  total_count: 'total_count',
  total_value: 'total_value',
  total_groups: 'total_groups',
} as const;
