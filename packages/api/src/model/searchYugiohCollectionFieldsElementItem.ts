/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */

export type SearchYugiohCollectionFieldsElementItem =
  (typeof SearchYugiohCollectionFieldsElementItem)[keyof typeof SearchYugiohCollectionFieldsElementItem];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const SearchYugiohCollectionFieldsElementItem = {
  source_type: 'source_type',
  input_session_uuid: 'input_session_uuid',
  scanned_image_url: 'scanned_image_url',
  scan_metadata: 'scan_metadata',
  price: 'price',
  condition: 'condition',
  created_at: 'created_at',
  committed_at: 'committed_at',
} as const;
