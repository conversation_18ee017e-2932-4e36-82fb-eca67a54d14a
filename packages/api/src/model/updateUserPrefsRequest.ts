/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import type { UpdateUserPrefsRequestLocalization } from './updateUserPrefsRequestLocalization';
import type { UpdateUserPrefsRequestPrivacy } from './updateUserPrefsRequestPrivacy';
import type { UpdateUserPrefsRequestCollection } from './updateUserPrefsRequestCollection';
import type { UpdateUserPrefsRequestPricing } from './updateUserPrefsRequestPricing';

export interface UpdateUserPrefsRequest {
  localization?: UpdateUserPrefsRequestLocalization;
  privacy?: UpdateUserPrefsRequestPrivacy;
  collection?: UpdateUserPrefsRequestCollection;
  pricing?: UpdateUserPrefsRequestPricing;
}
