/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import type { SearchPokemonCollectionQuantity } from './searchPokemonCollectionQuantity';
import type { SearchPokemonCollectionSource } from './searchPokemonCollectionSource';
import type { SearchPokemonCollectionConditionItem } from './searchPokemonCollectionConditionItem';
import type { SearchPokemonCollectionSortBy } from './searchPokemonCollectionSortBy';
import type { SearchPokemonCollectionConfidence } from './searchPokemonCollectionConfidence';
import type { SearchPokemonCollectionGroupBy } from './searchPokemonCollectionGroupBy';
import type { SearchPokemonCollectionOrder } from './searchPokemonCollectionOrder';
import type { SearchPokemonCollectionPerPage } from './searchPokemonCollectionPerPage';
import type { SearchPokemonCollectionTypes } from './searchPokemonCollectionTypes';
import type { SearchPokemonCollectionSubTypes } from './searchPokemonCollectionSubTypes';
import type { SearchPokemonCollectionSuperType } from './searchPokemonCollectionSuperType';
import type { SearchPokemonCollectionArtist } from './searchPokemonCollectionArtist';
import type { SearchPokemonCollectionSetNames } from './searchPokemonCollectionSetNames';
import type { SearchPokemonCollectionLevel } from './searchPokemonCollectionLevel';
import type { SearchPokemonCollectionHp } from './searchPokemonCollectionHp';
import type { SearchPokemonCollectionRarityFilterItem } from './searchPokemonCollectionRarityFilterItem';
import type { SearchPokemonCollectionFieldsSummaryItem } from './searchPokemonCollectionFieldsSummaryItem';
import type { SearchPokemonCollectionFieldsGroupItem } from './searchPokemonCollectionFieldsGroupItem';
import type { SearchPokemonCollectionFieldsCardItem } from './searchPokemonCollectionFieldsCardItem';
import type { SearchPokemonCollectionFieldsElementItem } from './searchPokemonCollectionFieldsElementItem';

export type SearchPokemonCollectionParams = {
  quantity?: SearchPokemonCollectionQuantity;
  source?: SearchPokemonCollectionSource;
  input_session?: string;
  condition?: SearchPokemonCollectionConditionItem[];
  sort_by?: SearchPokemonCollectionSortBy;
  confidence?: SearchPokemonCollectionConfidence;
  group_by?: SearchPokemonCollectionGroupBy;
  order?: SearchPokemonCollectionOrder;
  name?: string;
  query?: string;
  rules_text?: string;
  flavor_text?: string;
  per_page?: SearchPokemonCollectionPerPage;
  page?: number;
  /**
   * Accepted types can be retrieved from the `/types` route.
   */
  types?: SearchPokemonCollectionTypes;
  sub_types?: SearchPokemonCollectionSubTypes;
  super_type?: SearchPokemonCollectionSuperType;
  artist?: SearchPokemonCollectionArtist;
  set_names?: SearchPokemonCollectionSetNames;
  /**
   * matches the first letter (case insensitive) of a poke card's name
   */
  starts_with?: string;
  /**
   * matches the first letter (case insensitive) of a poke card's set name
   */
  set_starts_with?: string;
  level?: SearchPokemonCollectionLevel;
  hp?: SearchPokemonCollectionHp;
  rarity_filter?: SearchPokemonCollectionRarityFilterItem[];
  /**
   * Request specific fields to be included in the response. All fields are returned by default, but it's recommended that you use this parameter to specify only the fields you need to reduce the size of the response.
   */
  fields?: {
    summary?: SearchPokemonCollectionFieldsSummaryItem[];
    group?: SearchPokemonCollectionFieldsGroupItem[];
    card?: SearchPokemonCollectionFieldsCardItem[];
    element?: SearchPokemonCollectionFieldsElementItem[];
  };
};
