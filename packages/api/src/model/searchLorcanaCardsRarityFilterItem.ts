/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */

export type SearchLorcanaCardsRarityFilterItem =
  (typeof SearchLorcanaCardsRarityFilterItem)[keyof typeof SearchLorcanaCardsRarityFilterItem];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const SearchLorcanaCardsRarityFilterItem = {
  Common: 'Common',
  Uncommon: 'Uncommon',
  Rare: 'Rare',
  Super_rare: 'Super_rare',
  Legendary: 'Legendary',
  Enchanted: 'Enchanted',
  Promo: 'Promo',
} as const;
