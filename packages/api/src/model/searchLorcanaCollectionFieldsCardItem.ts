/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */

export type SearchLorcanaCollectionFieldsCardItem =
  (typeof SearchLorcanaCollectionFieldsCardItem)[keyof typeof SearchLorcanaCollectionFieldsCardItem];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const SearchLorcanaCollectionFieldsCardItem = {
  name: 'name',
  lorcast_id: 'lorcast_id',
  types: 'types',
  collector_number: 'collector_number',
  layout: 'layout',
  cost: 'cost',
  version: 'version',
  ink: 'ink',
  inkwell: 'inkwell',
  move_cost: 'move_cost',
  strength: 'strength',
  willpower: 'willpower',
  lore: 'lore',
  artist: 'artist',
  flavor_text: 'flavor_text',
  rules_text: 'rules_text',
  classifications: 'classifications',
  set_uuid: 'set_uuid',
  set_lorcast_id: 'set_lorcast_id',
  set_name: 'set_name',
  release_date: 'release_date',
  set_code: 'set_code',
  rarity_uuid: 'rarity_uuid',
  rarity: 'rarity',
} as const;
