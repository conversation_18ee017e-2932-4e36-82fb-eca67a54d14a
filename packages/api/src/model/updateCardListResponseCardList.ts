/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import type { UpdateCardListResponseCardListListedCardsItem } from './updateCardListResponseCardListListedCardsItem';
import type { UpdateCardListResponseCardListCards } from './updateCardListResponseCardListCards';

export type UpdateCardListResponseCardList = {
  name?: string;
  id?: number;
  count?: number;
  created_at?: string;
  listed_cards?: UpdateCardListResponseCardListListedCardsItem[];
  cards?: UpdateCardListResponseCardListCards;
};
