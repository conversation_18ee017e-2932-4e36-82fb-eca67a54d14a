/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import type { SearchPokeCardsOrder } from './searchPokeCardsOrder';
import type { SearchPokeCardsGroupBy } from './searchPokeCardsGroupBy';
import type { SearchPokeCardsPerPage } from './searchPokeCardsPerPage';
import type { SearchPokeCardsSetNames } from './searchPokeCardsSetNames';
import type { SearchPokeCardsTypes } from './searchPokeCardsTypes';
import type { SearchPokeCardsSubTypes } from './searchPokeCardsSubTypes';
import type { SearchPokeCardsSuperType } from './searchPokeCardsSuperType';
import type { SearchPokeCardsArtist } from './searchPokeCardsArtist';
import type { SearchPokeCardsRarityFilterItem } from './searchPokeCardsRarityFilterItem';
import type { SearchPokeCardsLevel } from './searchPokeCardsLevel';
import type { SearchPokeCardsHp } from './searchPokeCardsHp';
import type { SearchPokeCardsFieldsItem } from './searchPokeCardsFieldsItem';

export type SearchPokeCardsParams = {
  order?: SearchPokeCardsOrder;
  group_by?: SearchPokeCardsGroupBy;
  per_page?: SearchPokeCardsPerPage;
  page?: number;
  name?: string;
  query?: string;
  set_names?: SearchPokeCardsSetNames;
  /**
   * matches the first letter (case insensitive) of a poke card's name
   */
  starts_with?: string;
  /**
   * matches the first letter (case insensitive) of a poke card's set name
   */
  set_starts_with?: string;
  /**
   * Accepted types can be retrieved from the `/types` route.
   */
  types?: SearchPokeCardsTypes;
  sub_types?: SearchPokeCardsSubTypes;
  super_type?: SearchPokeCardsSuperType;
  artist?: SearchPokeCardsArtist;
  rarity_filter?: SearchPokeCardsRarityFilterItem[];
  level?: SearchPokeCardsLevel;
  hp?: SearchPokeCardsHp;
  rules_text?: string;
  flavor_text?: string;
  /**
   * Request specific fields to be included in the response. All fields are returned by default, but it's recommended that you use this parameter to specify only the fields you need to reduce the size of the response.
   */
  fields?: SearchPokeCardsFieldsItem[];
};
