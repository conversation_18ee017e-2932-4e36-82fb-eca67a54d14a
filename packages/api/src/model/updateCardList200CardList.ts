/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import type { UpdateCardList200CardListListedCardsItem } from './updateCardList200CardListListedCardsItem';
import type { UpdateCardList200CardListCards } from './updateCardList200CardListCards';

export type UpdateCardList200CardList = {
  name?: string;
  id?: number;
  count?: number;
  created_at?: string;
  listed_cards?: UpdateCardList200CardListListedCardsItem[];
  cards?: UpdateCardList200CardListCards;
};
