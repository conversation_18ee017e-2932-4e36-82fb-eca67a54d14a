/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */

export type SearchPokemonCollectionFieldsGroupItem =
  (typeof SearchPokemonCollectionFieldsGroupItem)[keyof typeof SearchPokemonCollectionFieldsGroupItem];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const SearchPokemonCollectionFieldsGroupItem = {
  latest_created: 'latest_created',
  latest_committed: 'latest_committed',
  group_price: 'group_price',
  max_price: 'max_price',
} as const;
