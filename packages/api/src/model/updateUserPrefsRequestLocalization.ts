/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */

export type UpdateUserPrefsRequestLocalization = typeof UpdateUserPrefsRequestLocalization[keyof typeof UpdateUserPrefsRequestLocalization];


// eslint-disable-next-line @typescript-eslint/no-redeclare
export const UpdateUserPrefsRequestLocalization = {
  '[object_Object]': [object Object],
} as const;
