/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import type { UpdatePokeCardUserResponseCardUsersItem } from './updatePokeCardUserResponseCardUsersItem';
import type { UpdatePokeCardUserResponseCards } from './updatePokeCardUserResponseCards';

export interface UpdatePokeCardUserResponse {
  card_users: UpdatePokeCardUserResponseCardUsersItem[];
  cards?: UpdatePokeCardUserResponseCards;
}
