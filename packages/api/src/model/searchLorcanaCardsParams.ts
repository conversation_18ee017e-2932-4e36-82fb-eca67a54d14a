/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import type { SearchLorcanaCardsSortBy } from './searchLorcanaCardsSortBy';
import type { SearchLorcanaCardsGroupBy } from './searchLorcanaCardsGroupBy';
import type { SearchLorcanaCardsOrder } from './searchLorcanaCardsOrder';
import type { SearchLorcanaCardsPerPage } from './searchLorcanaCardsPerPage';
import type { SearchLorcanaCardsTypes } from './searchLorcanaCardsTypes';
import type { SearchLorcanaCardsClassifications } from './searchLorcanaCardsClassifications';
import type { SearchLorcanaCardsArtist } from './searchLorcanaCardsArtist';
import type { SearchLorcanaCardsSetNames } from './searchLorcanaCardsSetNames';
import type { SearchLorcanaCardsStrength } from './searchLorcanaCardsStrength';
import type { SearchLorcanaCardsLore } from './searchLorcanaCardsLore';
import type { SearchLorcanaCardsWillpower } from './searchLorcanaCardsWillpower';
import type { SearchLorcanaCardsMoveCost } from './searchLorcanaCardsMoveCost';
import type { SearchLorcanaCardsCost } from './searchLorcanaCardsCost';
import type { SearchLorcanaCardsPrice } from './searchLorcanaCardsPrice';
import type { SearchLorcanaCardsRarityFilterItem } from './searchLorcanaCardsRarityFilterItem';
import type { SearchLorcanaCardsFieldsItem } from './searchLorcanaCardsFieldsItem';

export type SearchLorcanaCardsParams = {
  sort_by?: SearchLorcanaCardsSortBy;
  group_by?: SearchLorcanaCardsGroupBy;
  order?: SearchLorcanaCardsOrder;
  name?: string;
  query?: string;
  rules_text?: string;
  flavor_text?: string;
  layout?: string;
  version?: string;
  ink?: string;
  inkwell?: string;
  collector_number?: string;
  per_page?: SearchLorcanaCardsPerPage;
  page?: number;
  /**
   * Accepted types can be retrieved from the `/types` route.
   */
  types?: SearchLorcanaCardsTypes;
  classifications?: SearchLorcanaCardsClassifications;
  artist?: SearchLorcanaCardsArtist;
  set_names?: SearchLorcanaCardsSetNames;
  /**
   * matches the first letter (case insensitive) of a lorcana card's name
   */
  starts_with?: string;
  /**
   * matches the first letter (case insensitive) of a lorcana card's set name
   */
  set_starts_with?: string;
  strength?: SearchLorcanaCardsStrength;
  lore?: SearchLorcanaCardsLore;
  willpower?: SearchLorcanaCardsWillpower;
  move_cost?: SearchLorcanaCardsMoveCost;
  cost?: SearchLorcanaCardsCost;
  price?: SearchLorcanaCardsPrice;
  rarity_filter?: SearchLorcanaCardsRarityFilterItem[];
  /**
   * Request specific fields to be included in the response. All fields are returned by default, but it's recommended that you use this parameter to specify only the fields you need to reduce the size of the response.
   */
  fields?: SearchLorcanaCardsFieldsItem[];
};
