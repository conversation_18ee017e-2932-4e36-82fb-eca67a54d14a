/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import type { YugiCardInstanceYugiCardResponseCardInstancesItem } from './yugiCardInstanceYugiCardResponseCardInstancesItem';
import type { YugiCardInstanceYugiCardResponseCards } from './yugiCardInstanceYugiCardResponseCards';
import type { YugiCardInstanceYugiCardResponseSets } from './yugiCardInstanceYugiCardResponseSets';

export interface YugiCardInstanceYugiCardResponse {
  card_instances: YugiCardInstanceYugiCardResponseCardInstancesItem[];
  cards: YugiCardInstanceYugiCardResponseCards;
  sets: YugiCardInstanceYugiCardResponseSets;
}
