/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */

export type SearchLorcanaCardsPerPage =
  (typeof SearchLorcanaCardsPerPage)[keyof typeof SearchLorcanaCardsPerPage];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const SearchLorcanaCardsPerPage = {
  NUMBER_100: 100,
  NUMBER_300: 300,
  NUMBER_600: 600,
} as const;
