/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */

export type SearchPokemonCollectionSortBy =
  (typeof SearchPokemonCollectionSortBy)[keyof typeof SearchPokemonCollectionSortBy];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const SearchPokemonCollectionSortBy = {
  name: 'name',
  cmc: 'cmc',
  set_name: 'set_name',
  release_date: 'release_date',
  power: 'power',
  toughness: 'toughness',
  price: 'price',
  count: 'count',
  'date_(deprecated)': 'date (deprecated)',
  created_at: 'created_at',
  committed_at: 'committed_at',
  collector_number: 'collector_number',
  position: 'position',
  rarity: 'rarity',
  scan_confidence: 'scan_confidence',
} as const;
