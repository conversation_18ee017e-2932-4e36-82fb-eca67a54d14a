/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import type { UpdateUserPrefsBodyLocalization } from './updateUserPrefsBodyLocalization';
import type { UpdateUserPrefsBodyPrivacy } from './updateUserPrefsBodyPrivacy';
import type { UpdateUserPrefsBodyCollection } from './updateUserPrefsBodyCollection';
import type { UpdateUserPrefsBodyPricing } from './updateUserPrefsBodyPricing';

export type UpdateUserPrefsBody = {
  localization?: UpdateUserPrefsBodyLocalization;
  privacy?: UpdateUserPrefsBodyPrivacy;
  collection?: UpdateUserPrefsBodyCollection;
  pricing?: UpdateUserPrefsBodyPricing;
};
