/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import type { UpdateYugiCardInstances200CardInstancesItem } from './updateYugiCardInstances200CardInstancesItem';
import type { UpdateYugiCardInstances200Cards } from './updateYugiCardInstances200Cards';
import type { UpdateYugiCardInstances200Sets } from './updateYugiCardInstances200Sets';

export type UpdateYugiCardInstances200 = {
  card_instances: UpdateYugiCardInstances200CardInstancesItem[];
  cards?: UpdateYugiCardInstances200Cards;
  sets?: UpdateYugiCardInstances200Sets;
};
