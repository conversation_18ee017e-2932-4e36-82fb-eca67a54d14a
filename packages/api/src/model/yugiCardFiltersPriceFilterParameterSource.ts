/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */

export type YugiCardFiltersPriceFilterParameterSource =
  (typeof YugiCardFiltersPriceFilterParameterSource)[keyof typeof YugiCardFiltersPriceFilterParameterSource];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const YugiCardFiltersPriceFilterParameterSource = {
  tcg_player: 'tcg_player',
  card_kingdom: 'card_kingdom',
  card_market: 'card_market',
} as const;
