/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */

export type SearchLorcanaCollectionRarityFilterItem =
  (typeof SearchLorcanaCollectionRarityFilterItem)[keyof typeof SearchLorcanaCollectionRarityFilterItem];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const SearchLorcanaCollectionRarityFilterItem = {
  Common: 'Common',
  Uncommon: 'Uncommon',
  Rare: 'Rare',
  Super_rare: 'Super_rare',
  Legendary: 'Legendary',
  Enchanted: 'Enchanted',
  Promo: 'Promo',
} as const;
