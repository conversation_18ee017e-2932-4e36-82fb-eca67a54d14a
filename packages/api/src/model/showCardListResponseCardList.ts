/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import type { ShowCardListResponseCardListListedCardsItem } from './showCardListResponseCardListListedCardsItem';
import type { ShowCardListResponseCardListCards } from './showCardListResponseCardListCards';

export type ShowCardListResponseCardList = {
  name?: string;
  id?: number;
  count?: number;
  created_at?: string;
  listed_cards?: ShowCardListResponseCardListListedCardsItem[];
  cards?: ShowCardListResponseCardListCards;
};
