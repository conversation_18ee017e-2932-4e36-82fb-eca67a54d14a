/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */

export type SearchLorcanaCardsSortBy =
  (typeof SearchLorcanaCardsSortBy)[keyof typeof SearchLorcanaCardsSortBy];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const SearchLorcanaCardsSortBy = {
  name: 'name',
  set_name: 'set_name',
  release_date: 'release_date',
  collector_number: 'collector_number',
  rarity: 'rarity',
  scan_confidence: 'scan_confidence',
} as const;
