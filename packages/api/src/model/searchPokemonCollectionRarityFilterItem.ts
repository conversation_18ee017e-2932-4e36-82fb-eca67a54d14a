/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */

export type SearchPokemonCollectionRarityFilterItem =
  (typeof SearchPokemonCollectionRarityFilterItem)[keyof typeof SearchPokemonCollectionRarityFilterItem];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const SearchPokemonCollectionRarityFilterItem = {
  Amazing_Rare: 'Amazing Rare',
  Classic_Collection: 'Classic Collection',
  Common: 'Common',
  Double_Rare: 'Double Rare',
  Hyper_Rare: 'Hyper Rare',
  Illustration_Rare: 'Illustration Rare',
  LEGEND: 'LEGEND',
  Promo: 'Promo',
  Radiant_Rare: 'Radiant Rare',
  Rare: 'Rare',
  Rare_ACE: 'Rare ACE',
  Rare_BREAK: 'Rare BREAK',
  Rare_Holo: 'Rare Holo',
  Rare_Holo_EX: 'Rare Holo EX',
  Rare_Holo_GX: 'Rare Holo GX',
  Rare_Holo_LVX: 'Rare Holo LV.X',
  Rare_Holo_Star: 'Rare Holo Star',
  Rare_Holo_V: 'Rare Holo V',
  Rare_Holo_VMAX: 'Rare Holo VMAX',
  Rare_Holo_VSTAR: 'Rare Holo VSTAR',
  Rare_Prime: 'Rare Prime',
  Rare_Prism_Star: 'Rare Prism Star',
  Rare_Rainbow: 'Rare Rainbow',
  Rare_Secret: 'Rare Secret',
  Rare_Shining: 'Rare Shining',
  Rare_Shiny: 'Rare Shiny',
  Rare_Shiny_GX: 'Rare Shiny GX',
  Rare_Ultra: 'Rare Ultra',
  Special_Illustration_Rare: 'Special Illustration Rare',
  Trainer_Gallery_Rare_Holo: 'Trainer Gallery Rare Holo',
  Ultra_Rare: 'Ultra Rare',
  Uncommon: 'Uncommon',
} as const;
