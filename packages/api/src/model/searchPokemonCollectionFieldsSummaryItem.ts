/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */

export type SearchPokemonCollectionFieldsSummaryItem =
  (typeof SearchPokemonCollectionFieldsSummaryItem)[keyof typeof SearchPokemonCollectionFieldsSummaryItem];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const SearchPokemonCollectionFieldsSummaryItem = {
  page_value: 'page_value',
  total_count: 'total_count',
  total_value: 'total_value',
  total_groups: 'total_groups',
} as const;
