/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */

export type SearchPokeCardsFieldsItem =
  (typeof SearchPokeCardsFieldsItem)[keyof typeof SearchPokeCardsFieldsItem];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const SearchPokeCardsFieldsItem = {
  name: 'name',
  super_type: 'super_type',
  collector_number: 'collector_number',
  rarity: 'rarity',
  level: 'level',
  hp: 'hp',
  evolves_from: 'evolves_from',
  evolves_to: 'evolves_to',
  artist: 'artist',
  flavor_text: 'flavor_text',
  regulation_mark: 'regulation_mark',
  types: 'types',
  sub_types: 'sub_types',
  rules: 'rules',
  set_uuid: 'set_uuid',
  set_pokemontcgio_id: 'set_pokemontcgio_id',
  set_name: 'set_name',
  set_release_date: 'set_release_date',
  set_series: 'set_series',
} as const;
