/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import type { UpdateLorcanaCardInstanceResponseCardInstancesItem } from './updateLorcanaCardInstanceResponseCardInstancesItem';
import type { UpdateLorcanaCardInstanceResponseCards } from './updateLorcanaCardInstanceResponseCards';

export interface UpdateLorcanaCardInstanceResponse {
  card_instances: UpdateLorcanaCardInstanceResponseCardInstancesItem[];
  cards: UpdateLorcanaCardInstanceResponseCards;
}
