/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import type { UpdateProviderPlanProvider } from './updateProviderPlanProvider';

export type UpdateProviderPlanParams = {
  provider: UpdateProviderPlanProvider;
  plan_id: string;
  /**
   * name of associated subscription price
   */
  subscription_price: string;
};
