/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */

/**
 * Using `with: []` and `exclude_unselected: true` will exclude all cards which do not have at least one of the selected tags
 */
export type TagsFilterParameter = typeof TagsFilterParameter[keyof typeof TagsFilterParameter];


// eslint-disable-next-line @typescript-eslint/no-redeclare
export const TagsFilterParameter = {
  '[object_Object]': [object Object],
} as const;
