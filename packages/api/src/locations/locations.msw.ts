/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import { HttpResponse, delay, http } from 'msw';

import type {
  LocationsCreate200,
  LocationsIndex200,
  LocationsMerge200,
  LocationsPathCreate200,
  LocationsPathGet200,
  LocationsShow200,
  LocationsUpdate200,
} from '.././model';

export const getLocationsIndexResponseMock = (
  overrideResponse: Partial<LocationsIndex200> = {},
): LocationsIndex200 => ({ locations: {}, ...overrideResponse });

export const getLocationsCreateResponseMock = (
  overrideResponse: Partial<LocationsCreate200> = {},
): LocationsCreate200 => ({ location: {}, ...overrideResponse });

export const getLocationsPathCreateResponseMock = (
  overrideResponse: Partial<LocationsPathCreate200> = {},
): LocationsPathCreate200 => ({ locations: {}, ...overrideResponse });

export const getLocationsMergeResponseMock = (
  overrideResponse: Partial<LocationsMerge200> = {},
): LocationsMerge200 => ({ location: {}, ...overrideResponse });

export const getLocationsShowResponseMock = (
  overrideResponse: Partial<LocationsShow200> = {},
): LocationsShow200 => ({ location: {}, ...overrideResponse });

export const getLocationsUpdateResponseMock = (
  overrideResponse: Partial<LocationsUpdate200> = {},
): LocationsUpdate200 => ({ location: {}, ...overrideResponse });

export const getLocationsPathGetResponseMock = (
  overrideResponse: Partial<LocationsPathGet200> = {},
): LocationsPathGet200 => ({ locations: {}, ...overrideResponse });

export const getLocationsIndexMockHandler = (
  overrideResponse?:
    | LocationsIndex200
    | ((
        info: Parameters<Parameters<typeof http.get>[1]>[0],
      ) => Promise<LocationsIndex200> | LocationsIndex200),
) => {
  return http.get('*/locations', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getLocationsIndexResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getLocationsCreateMockHandler = (
  overrideResponse?:
    | LocationsCreate200
    | ((
        info: Parameters<Parameters<typeof http.post>[1]>[0],
      ) => Promise<LocationsCreate200> | LocationsCreate200),
) => {
  return http.post('*/locations', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getLocationsCreateResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getLocationsPathCreateMockHandler = (
  overrideResponse?:
    | LocationsPathCreate200
    | ((
        info: Parameters<Parameters<typeof http.post>[1]>[0],
      ) => Promise<LocationsPathCreate200> | LocationsPathCreate200),
) => {
  return http.post('*/locations/create_path', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getLocationsPathCreateResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getLocationsMergeMockHandler = (
  overrideResponse?:
    | LocationsMerge200
    | ((
        info: Parameters<Parameters<typeof http.post>[1]>[0],
      ) => Promise<LocationsMerge200> | LocationsMerge200),
) => {
  return http.post('*/locations/merge', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getLocationsMergeResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getLocationsShowMockHandler = (
  overrideResponse?:
    | LocationsShow200
    | ((
        info: Parameters<Parameters<typeof http.get>[1]>[0],
      ) => Promise<LocationsShow200> | LocationsShow200),
) => {
  return http.get('*/locations/:uuid', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getLocationsShowResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getLocationsUpdateMockHandler = (
  overrideResponse?:
    | LocationsUpdate200
    | ((
        info: Parameters<Parameters<typeof http.patch>[1]>[0],
      ) => Promise<LocationsUpdate200> | LocationsUpdate200),
) => {
  return http.patch('*/locations/:uuid', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getLocationsUpdateResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getDeleteLocationUUIDMockHandler = (
  overrideResponse?:
    | void
    | ((
        info: Parameters<Parameters<typeof http.delete>[1]>[0],
      ) => Promise<void> | void),
) => {
  return http.delete('*/locations/:uuid', async (info) => {
    await delay(1000);
    if (typeof overrideResponse === 'function') {
      await overrideResponse(info);
    }
    return new HttpResponse(null, { status: 204 });
  });
};

export const getLocationsPathGetMockHandler = (
  overrideResponse?:
    | LocationsPathGet200
    | ((
        info: Parameters<Parameters<typeof http.get>[1]>[0],
      ) => Promise<LocationsPathGet200> | LocationsPathGet200),
) => {
  return http.get('*/locations/:uuid/path', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getLocationsPathGetResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};
export const getLocationsMock = () => [
  getLocationsIndexMockHandler(),
  getLocationsCreateMockHandler(),
  getLocationsPathCreateMockHandler(),
  getLocationsMergeMockHandler(),
  getLocationsShowMockHandler(),
  getLocationsUpdateMockHandler(),
  getDeleteLocationUUIDMockHandler(),
  getLocationsPathGetMockHandler(),
];
