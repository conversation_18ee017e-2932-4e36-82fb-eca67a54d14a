/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import { useMutation, useQuery } from '@tanstack/react-query';
import type {
  MutationFunction,
  QueryFunction,
  QueryKey,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult,
} from '@tanstack/react-query';

import axios from 'axios';
import type { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios';

import type {
  LocationsCreate200,
  LocationsCreateParams,
  LocationsIndex200,
  LocationsIndexParams,
  LocationsMerge200,
  LocationsMergeParams,
  LocationsPathCreate200,
  LocationsPathCreateParams,
  LocationsPathGet200,
  LocationsShow200,
  LocationsUpdate200,
  LocationsUpdateParams,
} from '.././model';

type AwaitedInput<T> = PromiseLike<T> | T;

type Awaited<O> = O extends AwaitedInput<infer T> ? T : never;

/**
 * Get list of locations for authenticated user
 * @summary Get list of locations
 */
export const locationsIndex = (
  params?: LocationsIndexParams,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<LocationsIndex200>> => {
  return axios.get(`/locations`, {
    ...options,
    params: { ...params, ...options?.params },
  });
};

export const getLocationsIndexQueryKey = (params?: LocationsIndexParams) => {
  return [`/locations`, ...(params ? [params] : [])] as const;
};

export const getLocationsIndexQueryOptions = <
  TData = Awaited<ReturnType<typeof locationsIndex>>,
  TError = AxiosError<unknown>,
>(
  params?: LocationsIndexParams,
  options?: {
    query?: UseQueryOptions<
      Awaited<ReturnType<typeof locationsIndex>>,
      TError,
      TData
    >;
    axios?: AxiosRequestConfig;
  },
) => {
  const { query: queryOptions, axios: axiosOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getLocationsIndexQueryKey(params);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof locationsIndex>>> = ({
    signal,
  }) => locationsIndex(params, { signal, ...axiosOptions });

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof locationsIndex>>,
    TError,
    TData
  > & { queryKey: QueryKey };
};

export type LocationsIndexQueryResult = NonNullable<
  Awaited<ReturnType<typeof locationsIndex>>
>;
export type LocationsIndexQueryError = AxiosError<unknown>;

/**
 * @summary Get list of locations
 */

export function useLocationsIndex<
  TData = Awaited<ReturnType<typeof locationsIndex>>,
  TError = AxiosError<unknown>,
>(
  params?: LocationsIndexParams,
  options?: {
    query?: UseQueryOptions<
      Awaited<ReturnType<typeof locationsIndex>>,
      TError,
      TData
    >;
    axios?: AxiosRequestConfig;
  },
): UseQueryResult<TData, TError> & { queryKey: QueryKey } {
  const queryOptions = getLocationsIndexQueryOptions(params, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: QueryKey;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Create new location for authenticated user
 * @summary Create new location
 */
export const locationsCreate = (
  params: LocationsCreateParams,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<LocationsCreate200>> => {
  return axios.post(`/locations`, undefined, {
    ...options,
    params: { ...params, ...options?.params },
  });
};

export const getLocationsCreateMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof locationsCreate>>,
    TError,
    { params: LocationsCreateParams },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof locationsCreate>>,
  TError,
  { params: LocationsCreateParams },
  TContext
> => {
  const mutationKey = ['locationsCreate'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof locationsCreate>>,
    { params: LocationsCreateParams }
  > = (props) => {
    const { params } = props ?? {};

    return locationsCreate(params, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type LocationsCreateMutationResult = NonNullable<
  Awaited<ReturnType<typeof locationsCreate>>
>;

export type LocationsCreateMutationError = AxiosError<unknown>;

/**
 * @summary Create new location
 */
export const useLocationsCreate = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof locationsCreate>>,
    TError,
    { params: LocationsCreateParams },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof locationsCreate>>,
  TError,
  { params: LocationsCreateParams },
  TContext
> => {
  const mutationOptions = getLocationsCreateMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Create locations in path for authenticated user. If record already exists with a name in the path then a new record is not created.
 * @summary Create locations in path
 */
export const locationsPathCreate = (
  params: LocationsPathCreateParams,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<LocationsPathCreate200>> => {
  return axios.post(`/locations/create_path`, undefined, {
    ...options,
    params: { ...params, ...options?.params },
  });
};

export const getLocationsPathCreateMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof locationsPathCreate>>,
    TError,
    { params: LocationsPathCreateParams },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof locationsPathCreate>>,
  TError,
  { params: LocationsPathCreateParams },
  TContext
> => {
  const mutationKey = ['locationsPathCreate'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof locationsPathCreate>>,
    { params: LocationsPathCreateParams }
  > = (props) => {
    const { params } = props ?? {};

    return locationsPathCreate(params, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type LocationsPathCreateMutationResult = NonNullable<
  Awaited<ReturnType<typeof locationsPathCreate>>
>;

export type LocationsPathCreateMutationError = AxiosError<unknown>;

/**
 * @summary Create locations in path
 */
export const useLocationsPathCreate = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof locationsPathCreate>>,
    TError,
    { params: LocationsPathCreateParams },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof locationsPathCreate>>,
  TError,
  { params: LocationsPathCreateParams },
  TContext
> => {
  const mutationOptions = getLocationsPathCreateMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Merge stacked cards from several locations onto one target location
 * @summary Merge location stacked cards
 */
export const locationsMerge = (
  params: LocationsMergeParams,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<LocationsMerge200>> => {
  return axios.post(`/locations/merge`, undefined, {
    ...options,
    params: { ...params, ...options?.params },
  });
};

export const getLocationsMergeMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof locationsMerge>>,
    TError,
    { params: LocationsMergeParams },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof locationsMerge>>,
  TError,
  { params: LocationsMergeParams },
  TContext
> => {
  const mutationKey = ['locationsMerge'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof locationsMerge>>,
    { params: LocationsMergeParams }
  > = (props) => {
    const { params } = props ?? {};

    return locationsMerge(params, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type LocationsMergeMutationResult = NonNullable<
  Awaited<ReturnType<typeof locationsMerge>>
>;

export type LocationsMergeMutationError = AxiosError<unknown>;

/**
 * @summary Merge location stacked cards
 */
export const useLocationsMerge = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof locationsMerge>>,
    TError,
    { params: LocationsMergeParams },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof locationsMerge>>,
  TError,
  { params: LocationsMergeParams },
  TContext
> => {
  const mutationOptions = getLocationsMergeMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Get specific location for authenticated user
 * @summary Get location
 */
export const locationsShow = (
  uuid: string,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<LocationsShow200>> => {
  return axios.get(`/locations/${uuid}`, options);
};

export const getLocationsShowQueryKey = (uuid: string) => {
  return [`/locations/${uuid}`] as const;
};

export const getLocationsShowQueryOptions = <
  TData = Awaited<ReturnType<typeof locationsShow>>,
  TError = AxiosError<unknown>,
>(
  uuid: string,
  options?: {
    query?: UseQueryOptions<
      Awaited<ReturnType<typeof locationsShow>>,
      TError,
      TData
    >;
    axios?: AxiosRequestConfig;
  },
) => {
  const { query: queryOptions, axios: axiosOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getLocationsShowQueryKey(uuid);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof locationsShow>>> = ({
    signal,
  }) => locationsShow(uuid, { signal, ...axiosOptions });

  return {
    queryKey,
    queryFn,
    enabled: !!uuid,
    ...queryOptions,
  } as UseQueryOptions<
    Awaited<ReturnType<typeof locationsShow>>,
    TError,
    TData
  > & { queryKey: QueryKey };
};

export type LocationsShowQueryResult = NonNullable<
  Awaited<ReturnType<typeof locationsShow>>
>;
export type LocationsShowQueryError = AxiosError<unknown>;

/**
 * @summary Get location
 */

export function useLocationsShow<
  TData = Awaited<ReturnType<typeof locationsShow>>,
  TError = AxiosError<unknown>,
>(
  uuid: string,
  options?: {
    query?: UseQueryOptions<
      Awaited<ReturnType<typeof locationsShow>>,
      TError,
      TData
    >;
    axios?: AxiosRequestConfig;
  },
): UseQueryResult<TData, TError> & { queryKey: QueryKey } {
  const queryOptions = getLocationsShowQueryOptions(uuid, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: QueryKey;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Update name or parent of specific location owned by authenticated user
 * @summary Update location
 */
export const locationsUpdate = (
  uuid: string,
  params?: LocationsUpdateParams,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<LocationsUpdate200>> => {
  return axios.patch(`/locations/${uuid}`, undefined, {
    ...options,
    params: { ...params, ...options?.params },
  });
};

export const getLocationsUpdateMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof locationsUpdate>>,
    TError,
    { uuid: string; params?: LocationsUpdateParams },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof locationsUpdate>>,
  TError,
  { uuid: string; params?: LocationsUpdateParams },
  TContext
> => {
  const mutationKey = ['locationsUpdate'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof locationsUpdate>>,
    { uuid: string; params?: LocationsUpdateParams }
  > = (props) => {
    const { uuid, params } = props ?? {};

    return locationsUpdate(uuid, params, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type LocationsUpdateMutationResult = NonNullable<
  Awaited<ReturnType<typeof locationsUpdate>>
>;

export type LocationsUpdateMutationError = AxiosError<unknown>;

/**
 * @summary Update location
 */
export const useLocationsUpdate = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof locationsUpdate>>,
    TError,
    { uuid: string; params?: LocationsUpdateParams },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof locationsUpdate>>,
  TError,
  { uuid: string; params?: LocationsUpdateParams },
  TContext
> => {
  const mutationOptions = getLocationsUpdateMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Delete a specific location
 * @summary Delete location
 */
export const deleteLocationUUID = (
  uuid: string,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<void>> => {
  return axios.delete(`/locations/${uuid}`, options);
};

export const getDeleteLocationUUIDMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteLocationUUID>>,
    TError,
    { uuid: string },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof deleteLocationUUID>>,
  TError,
  { uuid: string },
  TContext
> => {
  const mutationKey = ['deleteLocationUUID'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof deleteLocationUUID>>,
    { uuid: string }
  > = (props) => {
    const { uuid } = props ?? {};

    return deleteLocationUUID(uuid, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type DeleteLocationUUIDMutationResult = NonNullable<
  Awaited<ReturnType<typeof deleteLocationUUID>>
>;

export type DeleteLocationUUIDMutationError = AxiosError<unknown>;

/**
 * @summary Delete location
 */
export const useDeleteLocationUUID = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteLocationUUID>>,
    TError,
    { uuid: string },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof deleteLocationUUID>>,
  TError,
  { uuid: string },
  TContext
> => {
  const mutationOptions = getDeleteLocationUUIDMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Get records in path between top of the order structure till the target location.
 * @summary Get locations in path
 */
export const locationsPathGet = (
  uuid: string,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<LocationsPathGet200>> => {
  return axios.get(`/locations/${uuid}/path`, options);
};

export const getLocationsPathGetQueryKey = (uuid: string) => {
  return [`/locations/${uuid}/path`] as const;
};

export const getLocationsPathGetQueryOptions = <
  TData = Awaited<ReturnType<typeof locationsPathGet>>,
  TError = AxiosError<unknown>,
>(
  uuid: string,
  options?: {
    query?: UseQueryOptions<
      Awaited<ReturnType<typeof locationsPathGet>>,
      TError,
      TData
    >;
    axios?: AxiosRequestConfig;
  },
) => {
  const { query: queryOptions, axios: axiosOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getLocationsPathGetQueryKey(uuid);

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof locationsPathGet>>
  > = ({ signal }) => locationsPathGet(uuid, { signal, ...axiosOptions });

  return {
    queryKey,
    queryFn,
    enabled: !!uuid,
    ...queryOptions,
  } as UseQueryOptions<
    Awaited<ReturnType<typeof locationsPathGet>>,
    TError,
    TData
  > & { queryKey: QueryKey };
};

export type LocationsPathGetQueryResult = NonNullable<
  Awaited<ReturnType<typeof locationsPathGet>>
>;
export type LocationsPathGetQueryError = AxiosError<unknown>;

/**
 * @summary Get locations in path
 */

export function useLocationsPathGet<
  TData = Awaited<ReturnType<typeof locationsPathGet>>,
  TError = AxiosError<unknown>,
>(
  uuid: string,
  options?: {
    query?: UseQueryOptions<
      Awaited<ReturnType<typeof locationsPathGet>>,
      TError,
      TData
    >;
    axios?: AxiosRequestConfig;
  },
): UseQueryResult<TData, TError> & { queryKey: QueryKey } {
  const queryOptions = getLocationsPathGetQueryOptions(uuid, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: QueryKey;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}
