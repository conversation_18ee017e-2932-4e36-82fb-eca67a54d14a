/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import { faker } from '@faker-js/faker';

import { HttpResponse, delay, http } from 'msw';

import type {
  CollectionCountBy200,
  CountCardusersForName200,
  GetCollectionTotal200,
  ListCardIdsForJsonIds200,
  LookupCollectionByNameOrJsonId200,
  SearchCollection1200,
  SearchLorcanaCollection200,
  SearchPokemonCollection200,
  SearchYugiohCollection200,
} from '.././model';

export const getGetCollectionTotalResponseMock = (
  overrideResponse: Partial<GetCollectionTotal200> = {},
): GetCollectionTotal200 => ({
  count: faker.number.int({ min: undefined, max: undefined }),
  value: faker.number.int({ min: undefined, max: undefined }),
  staged_count: faker.helpers.arrayElement([
    faker.number.int({ min: undefined, max: undefined }),
    undefined,
  ]),
  staged_value: faker.helpers.arrayElement([
    faker.number.int({ min: undefined, max: undefined }),
    undefined,
  ]),
  ...overrideResponse,
});

export const getLookupCollectionByNameOrJsonIdResponseMock = (
  overrideResponse: Partial<LookupCollectionByNameOrJsonId200> = {},
): LookupCollectionByNameOrJsonId200 => ({
  card_ids: Array.from(
    { length: faker.number.int({ min: 1, max: 10 }) },
    (_, i) => i + 1,
  ).map(() => faker.number.int({ min: undefined, max: undefined })),
  ...overrideResponse,
});

export const getSearchCollection1ResponseMock =
  (): SearchCollection1200 => ({});

export const getListCardIdsForJsonIdsResponseMock = (
  overrideResponse: Partial<ListCardIdsForJsonIds200> = {},
): ListCardIdsForJsonIds200 => ({
  card_maps: Array.from(
    { length: faker.number.int({ min: 1, max: 10 }) },
    (_, i) => i + 1,
  ).map(() => ({})),
  ...overrideResponse,
});

export const getCountCardusersForNameResponseMock = (
  overrideResponse: Partial<CountCardusersForName200> = {},
): CountCardusersForName200 => ({ count_maps: {}, ...overrideResponse });

export const getCollectionCountByResponseMock = (
  overrideResponse: Partial<CollectionCountBy200> = {},
): CollectionCountBy200 => ({ count_by: {}, ...overrideResponse });

export const getSearchPokemonCollectionResponseMock =
  (): SearchPokemonCollection200 => ({});

export const getSearchYugiohCollectionResponseMock =
  (): SearchYugiohCollection200 => ({});

export const getSearchLorcanaCollectionResponseMock =
  (): SearchLorcanaCollection200 => ({});

export const getGetCollectionTotalMockHandler = (
  overrideResponse?:
    | GetCollectionTotal200
    | ((
        info: Parameters<Parameters<typeof http.get>[1]>[0],
      ) => Promise<GetCollectionTotal200> | GetCollectionTotal200),
) => {
  return http.get('*/collection/total', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getGetCollectionTotalResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getLookupCollectionByNameOrJsonIdMockHandler = (
  overrideResponse?:
    | LookupCollectionByNameOrJsonId200
    | ((
        info: Parameters<Parameters<typeof http.post>[1]>[0],
      ) =>
        | Promise<LookupCollectionByNameOrJsonId200>
        | LookupCollectionByNameOrJsonId200),
) => {
  return http.post('*/collection/lookup', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getLookupCollectionByNameOrJsonIdResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getSearchCollection1MockHandler = (
  overrideResponse?:
    | SearchCollection1200
    | ((
        info: Parameters<Parameters<typeof http.post>[1]>[0],
      ) => Promise<SearchCollection1200> | SearchCollection1200),
) => {
  return http.post('*/collection/search', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getSearchCollection1ResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getListCardIdsForJsonIdsMockHandler = (
  overrideResponse?:
    | ListCardIdsForJsonIds200
    | ((
        info: Parameters<Parameters<typeof http.post>[1]>[0],
      ) => Promise<ListCardIdsForJsonIds200> | ListCardIdsForJsonIds200),
) => {
  return http.post('*/collection/list', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getListCardIdsForJsonIdsResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getCountCardusersForNameMockHandler = (
  overrideResponse?:
    | CountCardusersForName200
    | ((
        info: Parameters<Parameters<typeof http.post>[1]>[0],
      ) => Promise<CountCardusersForName200> | CountCardusersForName200),
) => {
  return http.post('*/collection/count', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getCountCardusersForNameResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getCollectionCountByMockHandler = (
  overrideResponse?:
    | CollectionCountBy200
    | ((
        info: Parameters<Parameters<typeof http.post>[1]>[0],
      ) => Promise<CollectionCountBy200> | CollectionCountBy200),
) => {
  return http.post('*/collection/count_by', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getCollectionCountByResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getSearchPokemonCollectionMockHandler = (
  overrideResponse?:
    | SearchPokemonCollection200
    | ((
        info: Parameters<Parameters<typeof http.post>[1]>[0],
      ) => Promise<SearchPokemonCollection200> | SearchPokemonCollection200),
) => {
  return http.post('*/collection/pokemon/search', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getSearchPokemonCollectionResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getSearchYugiohCollectionMockHandler = (
  overrideResponse?:
    | SearchYugiohCollection200
    | ((
        info: Parameters<Parameters<typeof http.post>[1]>[0],
      ) => Promise<SearchYugiohCollection200> | SearchYugiohCollection200),
) => {
  return http.post('*/collection/yugioh/search', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getSearchYugiohCollectionResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getSearchLorcanaCollectionMockHandler = (
  overrideResponse?:
    | SearchLorcanaCollection200
    | ((
        info: Parameters<Parameters<typeof http.post>[1]>[0],
      ) => Promise<SearchLorcanaCollection200> | SearchLorcanaCollection200),
) => {
  return http.post('*/collection/lorcana/search', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getSearchLorcanaCollectionResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};
export const getCollectionMock = () => [
  getGetCollectionTotalMockHandler(),
  getLookupCollectionByNameOrJsonIdMockHandler(),
  getSearchCollection1MockHandler(),
  getListCardIdsForJsonIdsMockHandler(),
  getCountCardusersForNameMockHandler(),
  getCollectionCountByMockHandler(),
  getSearchPokemonCollectionMockHandler(),
  getSearchYugiohCollectionMockHandler(),
  getSearchLorcanaCollectionMockHandler(),
];
