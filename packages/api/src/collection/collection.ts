/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import { useMutation, useQuery } from '@tanstack/react-query';
import type {
  MutationFunction,
  QueryFunction,
  QueryKey,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult,
} from '@tanstack/react-query';

import axios from 'axios';
import type { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios';

import type {
  CollectionCountBy200,
  CollectionCountByParams,
  CountCardusersForName200,
  CountCardusersForNameBody,
  GetCollectionTotal200,
  GetCollectionTotalParams,
  ListCardIdsForJsonIds200,
  ListCardIdsForJsonIdsBody,
  LookupCollectionByNameOrJsonId200,
  LookupCollectionByNameOrJsonIdBody,
  SearchCollection1200,
  SearchCollection1Params,
  SearchLorcanaCollection200,
  SearchLorcanaCollectionParams,
  SearchPokemonCollection200,
  SearchPokemonCollectionParams,
  SearchYugiohCollection200,
  SearchYugiohCollectionParams,
} from '.././model';

type AwaitedInput<T> = PromiseLike<T> | T;

type Awaited<O> = O extends AwaitedInput<infer T> ? T : never;

/**
 * Gets the total card count and value of the collection or current staged cards
 * @summary Get Collection Total
 */
export const getCollectionTotal = (
  params: GetCollectionTotalParams,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<GetCollectionTotal200>> => {
  return axios.get(`/collection/total`, {
    ...options,
    params: { ...params, ...options?.params },
  });
};

export const getGetCollectionTotalQueryKey = (
  params: GetCollectionTotalParams,
) => {
  return [`/collection/total`, ...(params ? [params] : [])] as const;
};

export const getGetCollectionTotalQueryOptions = <
  TData = Awaited<ReturnType<typeof getCollectionTotal>>,
  TError = AxiosError<unknown>,
>(
  params: GetCollectionTotalParams,
  options?: {
    query?: UseQueryOptions<
      Awaited<ReturnType<typeof getCollectionTotal>>,
      TError,
      TData
    >;
    axios?: AxiosRequestConfig;
  },
) => {
  const { query: queryOptions, axios: axiosOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ?? getGetCollectionTotalQueryKey(params);

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof getCollectionTotal>>
  > = ({ signal }) => getCollectionTotal(params, { signal, ...axiosOptions });

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getCollectionTotal>>,
    TError,
    TData
  > & { queryKey: QueryKey };
};

export type GetCollectionTotalQueryResult = NonNullable<
  Awaited<ReturnType<typeof getCollectionTotal>>
>;
export type GetCollectionTotalQueryError = AxiosError<unknown>;

/**
 * @summary Get Collection Total
 */

export function useGetCollectionTotal<
  TData = Awaited<ReturnType<typeof getCollectionTotal>>,
  TError = AxiosError<unknown>,
>(
  params: GetCollectionTotalParams,
  options?: {
    query?: UseQueryOptions<
      Awaited<ReturnType<typeof getCollectionTotal>>,
      TError,
      TData
    >;
    axios?: AxiosRequestConfig;
  },
): UseQueryResult<TData, TError> & { queryKey: QueryKey } {
  const queryOptions = getGetCollectionTotalQueryOptions(params, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: QueryKey;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Expands a group of cards owned by the user, either by printing or by name. Either of the parameters `json_id` or `name` must be present
+ json_ids (optional, object) - array of json_ids
+ names (optional, object) - array of card names
 * @deprecated
 * @summary Lookup Collection by Name or Json ID
 */
export const lookupCollectionByNameOrJsonId = (
  lookupCollectionByNameOrJsonIdBody: LookupCollectionByNameOrJsonIdBody,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<LookupCollectionByNameOrJsonId200>> => {
  return axios.post(
    `/collection/lookup`,
    lookupCollectionByNameOrJsonIdBody,
    options,
  );
};

export const getLookupCollectionByNameOrJsonIdMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof lookupCollectionByNameOrJsonId>>,
    TError,
    { data: LookupCollectionByNameOrJsonIdBody },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof lookupCollectionByNameOrJsonId>>,
  TError,
  { data: LookupCollectionByNameOrJsonIdBody },
  TContext
> => {
  const mutationKey = ['lookupCollectionByNameOrJsonId'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof lookupCollectionByNameOrJsonId>>,
    { data: LookupCollectionByNameOrJsonIdBody }
  > = (props) => {
    const { data } = props ?? {};

    return lookupCollectionByNameOrJsonId(data, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type LookupCollectionByNameOrJsonIdMutationResult = NonNullable<
  Awaited<ReturnType<typeof lookupCollectionByNameOrJsonId>>
>;
export type LookupCollectionByNameOrJsonIdMutationBody =
  LookupCollectionByNameOrJsonIdBody;
export type LookupCollectionByNameOrJsonIdMutationError = AxiosError<unknown>;

/**
 * @deprecated
 * @summary Lookup Collection by Name or Json ID
 */
export const useLookupCollectionByNameOrJsonId = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof lookupCollectionByNameOrJsonId>>,
    TError,
    { data: LookupCollectionByNameOrJsonIdBody },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof lookupCollectionByNameOrJsonId>>,
  TError,
  { data: LookupCollectionByNameOrJsonIdBody },
  TContext
> => {
  const mutationOptions =
    getLookupCollectionByNameOrJsonIdMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * New API with support for searching a collection. Same as `/api/v3/collection` with extra sorting features as well as support for new postgres json syntax
 * @summary Search Collection
 */
export const searchCollection1 = (
  params?: SearchCollection1Params,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<SearchCollection1200>> => {
  return axios.post(`/collection/search`, undefined, {
    ...options,
    params: { ...params, ...options?.params },
  });
};

export const getSearchCollection1MutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof searchCollection1>>,
    TError,
    { params?: SearchCollection1Params },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof searchCollection1>>,
  TError,
  { params?: SearchCollection1Params },
  TContext
> => {
  const mutationKey = ['searchCollection1'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof searchCollection1>>,
    { params?: SearchCollection1Params }
  > = (props) => {
    const { params } = props ?? {};

    return searchCollection1(params, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type SearchCollection1MutationResult = NonNullable<
  Awaited<ReturnType<typeof searchCollection1>>
>;

export type SearchCollection1MutationError = AxiosError<unknown>;

/**
 * @summary Search Collection
 */
export const useSearchCollection1 = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof searchCollection1>>,
    TError,
    { params?: SearchCollection1Params },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof searchCollection1>>,
  TError,
  { params?: SearchCollection1Params },
  TContext
> => {
  const mutationOptions = getSearchCollection1MutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Expands a group of cards owned by the user by printing. This API
is mainly for apps to use when deleting cards
+ json_ids (object) - array of json_id of the cards
+ foil (boolean, optional) - restrict cards with foil if required
 * @summary List Card IDs for Json IDs
 */
export const listCardIdsForJsonIds = (
  listCardIdsForJsonIdsBody: ListCardIdsForJsonIdsBody,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<ListCardIdsForJsonIds200>> => {
  return axios.post(`/collection/list`, listCardIdsForJsonIdsBody, options);
};

export const getListCardIdsForJsonIdsMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof listCardIdsForJsonIds>>,
    TError,
    { data: ListCardIdsForJsonIdsBody },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof listCardIdsForJsonIds>>,
  TError,
  { data: ListCardIdsForJsonIdsBody },
  TContext
> => {
  const mutationKey = ['listCardIdsForJsonIds'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof listCardIdsForJsonIds>>,
    { data: ListCardIdsForJsonIdsBody }
  > = (props) => {
    const { data } = props ?? {};

    return listCardIdsForJsonIds(data, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type ListCardIdsForJsonIdsMutationResult = NonNullable<
  Awaited<ReturnType<typeof listCardIdsForJsonIds>>
>;
export type ListCardIdsForJsonIdsMutationBody = ListCardIdsForJsonIdsBody;
export type ListCardIdsForJsonIdsMutationError = AxiosError<unknown>;

/**
 * @summary List Card IDs for Json IDs
 */
export const useListCardIdsForJsonIds = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof listCardIdsForJsonIds>>,
    TError,
    { data: ListCardIdsForJsonIdsBody },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof listCardIdsForJsonIds>>,
  TError,
  { data: ListCardIdsForJsonIdsBody },
  TContext
> => {
  const mutationOptions = getListCardIdsForJsonIdsMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Shows number of CardUsers owned for a given list of names. This API
is mainly for apps to use when scanning in Search Mode.
+ names (object) - array of names of the cards to match (must be exact)
+ foil (boolean, optional) - restrict cards with foil if required
 * @deprecated
 * @summary Count CardUsers for Name
 */
export const countCardusersForName = (
  countCardusersForNameBody: CountCardusersForNameBody,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<CountCardusersForName200>> => {
  return axios.post(`/collection/count`, countCardusersForNameBody, options);
};

export const getCountCardusersForNameMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof countCardusersForName>>,
    TError,
    { data: CountCardusersForNameBody },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof countCardusersForName>>,
  TError,
  { data: CountCardusersForNameBody },
  TContext
> => {
  const mutationKey = ['countCardusersForName'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof countCardusersForName>>,
    { data: CountCardusersForNameBody }
  > = (props) => {
    const { data } = props ?? {};

    return countCardusersForName(data, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type CountCardusersForNameMutationResult = NonNullable<
  Awaited<ReturnType<typeof countCardusersForName>>
>;
export type CountCardusersForNameMutationBody = CountCardusersForNameBody;
export type CountCardusersForNameMutationError = AxiosError<unknown>;

/**
 * @deprecated
 * @summary Count CardUsers for Name
 */
export const useCountCardusersForName = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof countCardusersForName>>,
    TError,
    { data: CountCardusersForNameBody },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof countCardusersForName>>,
  TError,
  { data: CountCardusersForNameBody },
  TContext
> => {
  const mutationOptions = getCountCardusersForNameMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Shows number of CardUsers owned by a given category. Note that the keys in the response will change depending on the 'count_by' param provided
 * @summary Count CardUsers by printing, card_set, or name
 */
export const collectionCountBy = (
  params: CollectionCountByParams,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<CollectionCountBy200>> => {
  return axios.post(`/collection/count_by`, undefined, {
    ...options,
    params: { ...params, ...options?.params },
  });
};

export const getCollectionCountByMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof collectionCountBy>>,
    TError,
    { params: CollectionCountByParams },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof collectionCountBy>>,
  TError,
  { params: CollectionCountByParams },
  TContext
> => {
  const mutationKey = ['collectionCountBy'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof collectionCountBy>>,
    { params: CollectionCountByParams }
  > = (props) => {
    const { params } = props ?? {};

    return collectionCountBy(params, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type CollectionCountByMutationResult = NonNullable<
  Awaited<ReturnType<typeof collectionCountBy>>
>;

export type CollectionCountByMutationError = AxiosError<unknown>;

/**
 * @summary Count CardUsers by printing, card_set, or name
 */
export const useCollectionCountBy = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof collectionCountBy>>,
    TError,
    { params: CollectionCountByParams },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof collectionCountBy>>,
  TError,
  { params: CollectionCountByParams },
  TContext
> => {
  const mutationOptions = getCollectionCountByMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Search the collection for pokemon cards. Similar to search, but with different parameters and response payload.
 * @summary Search Pokemon Collection
 */
export const searchPokemonCollection = (
  params?: SearchPokemonCollectionParams,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<SearchPokemonCollection200>> => {
  return axios.post(`/collection/pokemon/search`, undefined, {
    ...options,
    params: { ...params, ...options?.params },
  });
};

export const getSearchPokemonCollectionMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof searchPokemonCollection>>,
    TError,
    { params?: SearchPokemonCollectionParams },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof searchPokemonCollection>>,
  TError,
  { params?: SearchPokemonCollectionParams },
  TContext
> => {
  const mutationKey = ['searchPokemonCollection'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof searchPokemonCollection>>,
    { params?: SearchPokemonCollectionParams }
  > = (props) => {
    const { params } = props ?? {};

    return searchPokemonCollection(params, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type SearchPokemonCollectionMutationResult = NonNullable<
  Awaited<ReturnType<typeof searchPokemonCollection>>
>;

export type SearchPokemonCollectionMutationError = AxiosError<unknown>;

/**
 * @summary Search Pokemon Collection
 */
export const useSearchPokemonCollection = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof searchPokemonCollection>>,
    TError,
    { params?: SearchPokemonCollectionParams },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof searchPokemonCollection>>,
  TError,
  { params?: SearchPokemonCollectionParams },
  TContext
> => {
  const mutationOptions = getSearchPokemonCollectionMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Search the collection for yugioh cards. Similar to search, but with different parameters and response payload.
 * @summary Search Yugioh Collection
 */
export const searchYugiohCollection = (
  params?: SearchYugiohCollectionParams,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<SearchYugiohCollection200>> => {
  return axios.post(`/collection/yugioh/search`, undefined, {
    ...options,
    params: { ...params, ...options?.params },
  });
};

export const getSearchYugiohCollectionMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof searchYugiohCollection>>,
    TError,
    { params?: SearchYugiohCollectionParams },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof searchYugiohCollection>>,
  TError,
  { params?: SearchYugiohCollectionParams },
  TContext
> => {
  const mutationKey = ['searchYugiohCollection'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof searchYugiohCollection>>,
    { params?: SearchYugiohCollectionParams }
  > = (props) => {
    const { params } = props ?? {};

    return searchYugiohCollection(params, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type SearchYugiohCollectionMutationResult = NonNullable<
  Awaited<ReturnType<typeof searchYugiohCollection>>
>;

export type SearchYugiohCollectionMutationError = AxiosError<unknown>;

/**
 * @summary Search Yugioh Collection
 */
export const useSearchYugiohCollection = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof searchYugiohCollection>>,
    TError,
    { params?: SearchYugiohCollectionParams },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof searchYugiohCollection>>,
  TError,
  { params?: SearchYugiohCollectionParams },
  TContext
> => {
  const mutationOptions = getSearchYugiohCollectionMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Search the collection for Lorcana cards. Similar to search, but with different parameters and response payload.
 * @summary Search Lorcana Collection
 */
export const searchLorcanaCollection = (
  params?: SearchLorcanaCollectionParams,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<SearchLorcanaCollection200>> => {
  return axios.post(`/collection/lorcana/search`, undefined, {
    ...options,
    params: { ...params, ...options?.params },
  });
};

export const getSearchLorcanaCollectionMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof searchLorcanaCollection>>,
    TError,
    { params?: SearchLorcanaCollectionParams },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof searchLorcanaCollection>>,
  TError,
  { params?: SearchLorcanaCollectionParams },
  TContext
> => {
  const mutationKey = ['searchLorcanaCollection'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof searchLorcanaCollection>>,
    { params?: SearchLorcanaCollectionParams }
  > = (props) => {
    const { params } = props ?? {};

    return searchLorcanaCollection(params, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type SearchLorcanaCollectionMutationResult = NonNullable<
  Awaited<ReturnType<typeof searchLorcanaCollection>>
>;

export type SearchLorcanaCollectionMutationError = AxiosError<unknown>;

/**
 * @summary Search Lorcana Collection
 */
export const useSearchLorcanaCollection = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof searchLorcanaCollection>>,
    TError,
    { params?: SearchLorcanaCollectionParams },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof searchLorcanaCollection>>,
  TError,
  { params?: SearchLorcanaCollectionParams },
  TContext
> => {
  const mutationOptions = getSearchLorcanaCollectionMutationOptions(options);

  return useMutation(mutationOptions);
};
