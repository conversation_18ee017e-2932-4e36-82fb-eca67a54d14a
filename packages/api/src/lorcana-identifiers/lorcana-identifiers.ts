/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import { useQuery } from '@tanstack/react-query';
import type {
  QueryFunction,
  QueryKey,
  UseQueryOptions,
  UseQueryResult,
} from '@tanstack/react-query';

import axios from 'axios';
import type { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios';

import type { GetAllLorcanaCardIdentifiers200 } from '.././model';

type AwaitedInput<T> = PromiseLike<T> | T;

type Awaited<O> = O extends AwaitedInput<infer T> ? T : never;

/**
 * Get all lorcana card identifiers
 * @summary Get all lorcana card identifiers
 */
export const getAllLorcanaCardIdentifiers = (
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<GetAllLorcanaCardIdentifiers200>> => {
  return axios.get(`/lorcana_card_identifiers`, options);
};

export const getGetAllLorcanaCardIdentifiersQueryKey = () => {
  return [`/lorcana_card_identifiers`] as const;
};

export const getGetAllLorcanaCardIdentifiersQueryOptions = <
  TData = Awaited<ReturnType<typeof getAllLorcanaCardIdentifiers>>,
  TError = AxiosError<unknown>,
>(options?: {
  query?: UseQueryOptions<
    Awaited<ReturnType<typeof getAllLorcanaCardIdentifiers>>,
    TError,
    TData
  >;
  axios?: AxiosRequestConfig;
}) => {
  const { query: queryOptions, axios: axiosOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ?? getGetAllLorcanaCardIdentifiersQueryKey();

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof getAllLorcanaCardIdentifiers>>
  > = ({ signal }) => getAllLorcanaCardIdentifiers({ signal, ...axiosOptions });

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getAllLorcanaCardIdentifiers>>,
    TError,
    TData
  > & { queryKey: QueryKey };
};

export type GetAllLorcanaCardIdentifiersQueryResult = NonNullable<
  Awaited<ReturnType<typeof getAllLorcanaCardIdentifiers>>
>;
export type GetAllLorcanaCardIdentifiersQueryError = AxiosError<unknown>;

/**
 * @summary Get all lorcana card identifiers
 */

export function useGetAllLorcanaCardIdentifiers<
  TData = Awaited<ReturnType<typeof getAllLorcanaCardIdentifiers>>,
  TError = AxiosError<unknown>,
>(options?: {
  query?: UseQueryOptions<
    Awaited<ReturnType<typeof getAllLorcanaCardIdentifiers>>,
    TError,
    TData
  >;
  axios?: AxiosRequestConfig;
}): UseQueryResult<TData, TError> & { queryKey: QueryKey } {
  const queryOptions = getGetAllLorcanaCardIdentifiersQueryOptions(options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: QueryKey;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}
