/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import { faker } from '@faker-js/faker';

import { HttpResponse, delay, http } from 'msw';

import type { GetAllLorcanaCardIdentifiers200 } from '.././model';

export const getGetAllLorcanaCardIdentifiersResponseMock = (
  overrideResponse: Partial<GetAllLorcanaCardIdentifiers200> = {},
): GetAllLorcanaCardIdentifiers200 => ({
  card_identifiers: faker.helpers.arrayElement([
    Array.from(
      { length: faker.number.int({ min: 1, max: 10 }) },
      (_, i) => i + 1,
    ).map(() => ({})),
    undefined,
  ]),
  ...overrideResponse,
});

export const getGetAllLorcanaCardIdentifiersMockHandler = (
  overrideResponse?:
    | GetAllLorcanaCardIdentifiers200
    | ((
        info: Parameters<Parameters<typeof http.get>[1]>[0],
      ) =>
        | Promise<GetAllLorcanaCardIdentifiers200>
        | GetAllLorcanaCardIdentifiers200),
) => {
  return http.get('*/lorcana_card_identifiers', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getGetAllLorcanaCardIdentifiersResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};
export const getLorcanaIdentifiersMock = () => [
  getGetAllLorcanaCardIdentifiersMockHandler(),
];
