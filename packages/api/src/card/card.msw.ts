/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import { faker } from '@faker-js/faker';

import { HttpResponse, delay, http } from 'msw';

import type {
  GetAllCards200,
  GetArtists200,
  GetCardsLanguages200,
  GetCardsPrintings200,
  GetDetailsForCard200,
  GetDetailsForCardJsonId200,
  GetSubTypes200,
  GetSuperTypes200,
  GetTypes200,
  SearchCards200,
} from '.././model';

export const getGetAllCardsResponseMock = (
  overrideResponse: Partial<GetAllCards200> = {},
): GetAllCards200 => ({
  cards: Array.from(
    { length: faker.number.int({ min: 1, max: 10 }) },
    (_, i) => i + 1,
  ).map(() => ({})),
  ...overrideResponse,
});

export const getGetCardsPrintingsResponseMock = (
  overrideResponse: Partial<GetCardsPrintings200> = {},
): GetCardsPrintings200 => ({ printings: {}, ...overrideResponse });

export const getGetCardsLanguagesResponseMock = (
  overrideResponse: Partial<GetCardsLanguages200> = {},
): GetCardsLanguages200 => ({ languages: {}, ...overrideResponse });

export const getGetDetailsForCardResponseMock = (
  overrideResponse: Partial<GetDetailsForCard200> = {},
): GetDetailsForCard200 => ({
  id: faker.number.int({ min: undefined, max: undefined }),
  name: faker.string.alpha(20),
  description: faker.string.alpha(20),
  power: faker.string.alpha(20),
  toughness: faker.string.alpha(20),
  created_at: faker.string.alpha(20),
  updated_at: faker.string.alpha(20),
  search_name: faker.string.alpha(20),
  mana_cost: faker.string.alpha(20),
  converted_mana_cost: faker.string.alpha(20),
  card_set_name: faker.string.alpha(20),
  card_type: faker.string.alpha(20),
  sub_type: faker.string.alpha(20),
  loyalty: faker.helpers.arrayElement([faker.string.alpha(20), null]),
  rarity: faker.string.alpha(20),
  card_set_id: faker.string.alpha(20),
  multiverse_id: faker.number.int({ min: undefined, max: undefined }),
  card_set_code: faker.string.alpha(20),
  legalities: faker.string.alpha(20),
  super_type: faker.string.alpha(20),
  types: faker.string.alpha(20),
  colors: faker.string.alpha(20),
  flavor: faker.string.alpha(20),
  artist: faker.string.alpha(20),
  number: faker.string.alpha(20),
  ozguild_id: faker.string.alpha(20),
  price: faker.number.int({ min: undefined, max: undefined }),
  json_id: faker.string.alpha(20),
  has_image: faker.datatype.boolean(),
  ...overrideResponse,
});

export const getGetDetailsForCardJsonIdResponseMock = (
  overrideResponse: Partial<GetDetailsForCardJsonId200> = {},
): GetDetailsForCardJsonId200 => ({
  id: faker.number.int({ min: undefined, max: undefined }),
  name: faker.string.alpha(20),
  description: faker.string.alpha(20),
  power: faker.string.alpha(20),
  toughness: faker.string.alpha(20),
  created_at: faker.string.alpha(20),
  updated_at: faker.string.alpha(20),
  search_name: faker.string.alpha(20),
  mana_cost: faker.string.alpha(20),
  converted_mana_cost: faker.string.alpha(20),
  card_set_name: faker.string.alpha(20),
  card_type: faker.string.alpha(20),
  sub_type: faker.string.alpha(20),
  loyalty: faker.helpers.arrayElement([faker.string.alpha(20), null]),
  rarity: faker.string.alpha(20),
  card_set_id: faker.string.alpha(20),
  multiverse_id: faker.number.int({ min: undefined, max: undefined }),
  card_set_code: faker.string.alpha(20),
  legalities: faker.string.alpha(20),
  super_type: faker.string.alpha(20),
  types: faker.string.alpha(20),
  colors: faker.string.alpha(20),
  flavor: faker.string.alpha(20),
  artist: faker.string.alpha(20),
  number: faker.string.alpha(20),
  ozguild_id: faker.string.alpha(20),
  price: faker.number.int({ min: undefined, max: undefined }),
  json_id: faker.string.alpha(20),
  has_image: faker.datatype.boolean(),
  ...overrideResponse,
});

export const getSearchCardsResponseMock = (): SearchCards200 => ({});

export const getGetArtistsResponseMock = (
  overrideResponse: Partial<GetArtists200> = {},
): GetArtists200 => ({
  artists: Array.from(
    { length: faker.number.int({ min: 1, max: 10 }) },
    (_, i) => i + 1,
  ).map(() => faker.string.alpha(20)),
  ...overrideResponse,
});

export const getGetSuperTypesResponseMock = (
  overrideResponse: Partial<GetSuperTypes200> = {},
): GetSuperTypes200 => ({
  super_types: Array.from(
    { length: faker.number.int({ min: 1, max: 10 }) },
    (_, i) => i + 1,
  ).map(() => faker.string.alpha(20)),
  ...overrideResponse,
});

export const getGetSubTypesResponseMock = (
  overrideResponse: Partial<GetSubTypes200> = {},
): GetSubTypes200 => ({
  sub_types: Array.from(
    { length: faker.number.int({ min: 1, max: 10 }) },
    (_, i) => i + 1,
  ).map(() => faker.string.alpha(20)),
  ...overrideResponse,
});

export const getGetTypesResponseMock = (
  overrideResponse: Partial<GetTypes200> = {},
): GetTypes200 => ({
  types: Array.from(
    { length: faker.number.int({ min: 1, max: 10 }) },
    (_, i) => i + 1,
  ).map(() => faker.string.alpha(20)),
  ...overrideResponse,
});

export const getGetAllCardsMockHandler = (
  overrideResponse?:
    | GetAllCards200
    | ((
        info: Parameters<Parameters<typeof http.get>[1]>[0],
      ) => Promise<GetAllCards200> | GetAllCards200),
) => {
  return http.get('*/cards', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getGetAllCardsResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getGetCardsPrintingsMockHandler = (
  overrideResponse?:
    | GetCardsPrintings200
    | ((
        info: Parameters<Parameters<typeof http.post>[1]>[0],
      ) => Promise<GetCardsPrintings200> | GetCardsPrintings200),
) => {
  return http.post('*/cards/printings', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getGetCardsPrintingsResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getGetCardsLanguagesMockHandler = (
  overrideResponse?:
    | GetCardsLanguages200
    | ((
        info: Parameters<Parameters<typeof http.post>[1]>[0],
      ) => Promise<GetCardsLanguages200> | GetCardsLanguages200),
) => {
  return http.post('*/cards/languages', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getGetCardsLanguagesResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getGetDetailsForCardMockHandler = (
  overrideResponse?:
    | GetDetailsForCard200
    | ((
        info: Parameters<Parameters<typeof http.get>[1]>[0],
      ) => Promise<GetDetailsForCard200> | GetDetailsForCard200),
) => {
  return http.get('*/cards/:id', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getGetDetailsForCardResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getGetDetailsForCardJsonIdMockHandler = (
  overrideResponse?:
    | GetDetailsForCardJsonId200
    | ((
        info: Parameters<Parameters<typeof http.get>[1]>[0],
      ) => Promise<GetDetailsForCardJsonId200> | GetDetailsForCardJsonId200),
) => {
  return http.get('*/cards/expand', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getGetDetailsForCardJsonIdResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getSearchCardsMockHandler = (
  overrideResponse?:
    | SearchCards200
    | ((
        info: Parameters<Parameters<typeof http.post>[1]>[0],
      ) => Promise<SearchCards200> | SearchCards200),
) => {
  return http.post('*/cards/search', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getSearchCardsResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getGetArtistsMockHandler = (
  overrideResponse?:
    | GetArtists200
    | ((
        info: Parameters<Parameters<typeof http.get>[1]>[0],
      ) => Promise<GetArtists200> | GetArtists200),
) => {
  return http.get('*/cards/artists', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getGetArtistsResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getGetSuperTypesMockHandler = (
  overrideResponse?:
    | GetSuperTypes200
    | ((
        info: Parameters<Parameters<typeof http.get>[1]>[0],
      ) => Promise<GetSuperTypes200> | GetSuperTypes200),
) => {
  return http.get('*/cards/super_types', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getGetSuperTypesResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getGetSubTypesMockHandler = (
  overrideResponse?:
    | GetSubTypes200
    | ((
        info: Parameters<Parameters<typeof http.get>[1]>[0],
      ) => Promise<GetSubTypes200> | GetSubTypes200),
) => {
  return http.get('*/cards/sub_types', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getGetSubTypesResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getGetTypesMockHandler = (
  overrideResponse?:
    | GetTypes200
    | ((
        info: Parameters<Parameters<typeof http.get>[1]>[0],
      ) => Promise<GetTypes200> | GetTypes200),
) => {
  return http.get('*/cards/types', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getGetTypesResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};
export const getCardMock = () => [
  getGetAllCardsMockHandler(),
  getGetCardsPrintingsMockHandler(),
  getGetCardsLanguagesMockHandler(),
  getGetDetailsForCardMockHandler(),
  getGetDetailsForCardJsonIdMockHandler(),
  getSearchCardsMockHandler(),
  getGetArtistsMockHandler(),
  getGetSuperTypesMockHandler(),
  getGetSubTypesMockHandler(),
  getGetTypesMockHandler(),
];
