/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import { useMutation, useQuery } from '@tanstack/react-query';
import type {
  MutationFunction,
  QueryFunction,
  QueryKey,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult,
} from '@tanstack/react-query';

import axios from 'axios';
import type { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios';

import type {
  GetAllCards200,
  GetAllCardsParams,
  GetArtists200,
  GetCardsLanguages200,
  GetCardsLanguagesBody,
  GetCardsPrintings200,
  GetCardsPrintingsBody,
  GetDetailsForCard200,
  GetDetailsForCardJsonId200,
  GetSubTypes200,
  GetSuperTypes200,
  GetTypes200,
  SearchCards200,
  SearchCardsParams,
} from '.././model';

type AwaitedInput<T> = PromiseLike<T> | T;

type Awaited<O> = O extends AwaitedInput<infer T> ? T : never;

/**
 * Search all cards. This API is both public and private, if the user is logged in an owned count is returned, if they are not\nlogged in the owned count fields are not present.
 * @summary Search all cards
 */
export const getAllCards = (
  params?: GetAllCardsParams,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<GetAllCards200>> => {
  return axios.get(`/cards`, {
    ...options,
    params: { ...params, ...options?.params },
  });
};

export const getGetAllCardsQueryKey = (params?: GetAllCardsParams) => {
  return [`/cards`, ...(params ? [params] : [])] as const;
};

export const getGetAllCardsQueryOptions = <
  TData = Awaited<ReturnType<typeof getAllCards>>,
  TError = AxiosError<unknown>,
>(
  params?: GetAllCardsParams,
  options?: {
    query?: UseQueryOptions<
      Awaited<ReturnType<typeof getAllCards>>,
      TError,
      TData
    >;
    axios?: AxiosRequestConfig;
  },
) => {
  const { query: queryOptions, axios: axiosOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetAllCardsQueryKey(params);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getAllCards>>> = ({
    signal,
  }) => getAllCards(params, { signal, ...axiosOptions });

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getAllCards>>,
    TError,
    TData
  > & { queryKey: QueryKey };
};

export type GetAllCardsQueryResult = NonNullable<
  Awaited<ReturnType<typeof getAllCards>>
>;
export type GetAllCardsQueryError = AxiosError<unknown>;

/**
 * @summary Search all cards
 */

export function useGetAllCards<
  TData = Awaited<ReturnType<typeof getAllCards>>,
  TError = AxiosError<unknown>,
>(
  params?: GetAllCardsParams,
  options?: {
    query?: UseQueryOptions<
      Awaited<ReturnType<typeof getAllCards>>,
      TError,
      TData
    >;
    axios?: AxiosRequestConfig;
  },
): UseQueryResult<TData, TError> & { queryKey: QueryKey } {
  const queryOptions = getGetAllCardsQueryOptions(params, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: QueryKey;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * + Duplicate JSON ids are ignored 
+ Response is sorted by release date 
+ Response is empty when there are no shared printings 
+ Invalid ids respond with 'not found'
 * @summary Get printing details for cards by JSON ids
 */
export const getCardsPrintings = (
  getCardsPrintingsBody: GetCardsPrintingsBody,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<GetCardsPrintings200>> => {
  return axios.post(`/cards/printings`, getCardsPrintingsBody, options);
};

export const getGetCardsPrintingsMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof getCardsPrintings>>,
    TError,
    { data: GetCardsPrintingsBody },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof getCardsPrintings>>,
  TError,
  { data: GetCardsPrintingsBody },
  TContext
> => {
  const mutationKey = ['getCardsPrintings'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof getCardsPrintings>>,
    { data: GetCardsPrintingsBody }
  > = (props) => {
    const { data } = props ?? {};

    return getCardsPrintings(data, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type GetCardsPrintingsMutationResult = NonNullable<
  Awaited<ReturnType<typeof getCardsPrintings>>
>;
export type GetCardsPrintingsMutationBody = GetCardsPrintingsBody;
export type GetCardsPrintingsMutationError = AxiosError<unknown>;

/**
 * @summary Get printing details for cards by JSON ids
 */
export const useGetCardsPrintings = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof getCardsPrintings>>,
    TError,
    { data: GetCardsPrintingsBody },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof getCardsPrintings>>,
  TError,
  { data: GetCardsPrintingsBody },
  TContext
> => {
  const mutationOptions = getGetCardsPrintingsMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * + Invalid ids respond with 'not found'
 * @summary Get language details for cards by JSON ids
 */
export const getCardsLanguages = (
  getCardsLanguagesBody: GetCardsLanguagesBody,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<GetCardsLanguages200>> => {
  return axios.post(`/cards/languages`, getCardsLanguagesBody, options);
};

export const getGetCardsLanguagesMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof getCardsLanguages>>,
    TError,
    { data: GetCardsLanguagesBody },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof getCardsLanguages>>,
  TError,
  { data: GetCardsLanguagesBody },
  TContext
> => {
  const mutationKey = ['getCardsLanguages'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof getCardsLanguages>>,
    { data: GetCardsLanguagesBody }
  > = (props) => {
    const { data } = props ?? {};

    return getCardsLanguages(data, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type GetCardsLanguagesMutationResult = NonNullable<
  Awaited<ReturnType<typeof getCardsLanguages>>
>;
export type GetCardsLanguagesMutationBody = GetCardsLanguagesBody;
export type GetCardsLanguagesMutationError = AxiosError<unknown>;

/**
 * @summary Get language details for cards by JSON ids
 */
export const useGetCardsLanguages = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof getCardsLanguages>>,
    TError,
    { data: GetCardsLanguagesBody },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof getCardsLanguages>>,
  TError,
  { data: GetCardsLanguagesBody },
  TContext
> => {
  const mutationOptions = getGetCardsLanguagesMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * + id (string) - json_id of the card
 * @summary Get Details for Card
 */
export const getDetailsForCard = (
  id: string,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<GetDetailsForCard200>> => {
  return axios.get(`/cards/${id}`, options);
};

export const getGetDetailsForCardQueryKey = (id: string) => {
  return [`/cards/${id}`] as const;
};

export const getGetDetailsForCardQueryOptions = <
  TData = Awaited<ReturnType<typeof getDetailsForCard>>,
  TError = AxiosError<unknown>,
>(
  id: string,
  options?: {
    query?: UseQueryOptions<
      Awaited<ReturnType<typeof getDetailsForCard>>,
      TError,
      TData
    >;
    axios?: AxiosRequestConfig;
  },
) => {
  const { query: queryOptions, axios: axiosOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetDetailsForCardQueryKey(id);

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof getDetailsForCard>>
  > = ({ signal }) => getDetailsForCard(id, { signal, ...axiosOptions });

  return {
    queryKey,
    queryFn,
    enabled: !!id,
    ...queryOptions,
  } as UseQueryOptions<
    Awaited<ReturnType<typeof getDetailsForCard>>,
    TError,
    TData
  > & { queryKey: QueryKey };
};

export type GetDetailsForCardQueryResult = NonNullable<
  Awaited<ReturnType<typeof getDetailsForCard>>
>;
export type GetDetailsForCardQueryError = AxiosError<unknown>;

/**
 * @summary Get Details for Card
 */

export function useGetDetailsForCard<
  TData = Awaited<ReturnType<typeof getDetailsForCard>>,
  TError = AxiosError<unknown>,
>(
  id: string,
  options?: {
    query?: UseQueryOptions<
      Awaited<ReturnType<typeof getDetailsForCard>>,
      TError,
      TData
    >;
    axios?: AxiosRequestConfig;
  },
): UseQueryResult<TData, TError> & { queryKey: QueryKey } {
  const queryOptions = getGetDetailsForCardQueryOptions(id, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: QueryKey;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary Get Details for Card By JSON ID
 */
export const getDetailsForCardJsonId = (
  getDetailsForCardJsonIdBody: string[],
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<GetDetailsForCardJsonId200>> => {
  return axios.get(`/cards/expand`, options);
};

export const getGetDetailsForCardJsonIdQueryKey = (
  getDetailsForCardJsonIdBody: string[],
) => {
  return [`/cards/expand`, getDetailsForCardJsonIdBody] as const;
};

export const getGetDetailsForCardJsonIdQueryOptions = <
  TData = Awaited<ReturnType<typeof getDetailsForCardJsonId>>,
  TError = AxiosError<unknown>,
>(
  getDetailsForCardJsonIdBody: string[],
  options?: {
    query?: UseQueryOptions<
      Awaited<ReturnType<typeof getDetailsForCardJsonId>>,
      TError,
      TData
    >;
    axios?: AxiosRequestConfig;
  },
) => {
  const { query: queryOptions, axios: axiosOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ??
    getGetDetailsForCardJsonIdQueryKey(getDetailsForCardJsonIdBody);

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof getDetailsForCardJsonId>>
  > = ({ signal }) =>
    getDetailsForCardJsonId(getDetailsForCardJsonIdBody, {
      signal,
      ...axiosOptions,
    });

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getDetailsForCardJsonId>>,
    TError,
    TData
  > & { queryKey: QueryKey };
};

export type GetDetailsForCardJsonIdQueryResult = NonNullable<
  Awaited<ReturnType<typeof getDetailsForCardJsonId>>
>;
export type GetDetailsForCardJsonIdQueryError = AxiosError<unknown>;

/**
 * @summary Get Details for Card By JSON ID
 */

export function useGetDetailsForCardJsonId<
  TData = Awaited<ReturnType<typeof getDetailsForCardJsonId>>,
  TError = AxiosError<unknown>,
>(
  getDetailsForCardJsonIdBody: string[],
  options?: {
    query?: UseQueryOptions<
      Awaited<ReturnType<typeof getDetailsForCardJsonId>>,
      TError,
      TData
    >;
    axios?: AxiosRequestConfig;
  },
): UseQueryResult<TData, TError> & { queryKey: QueryKey } {
  const queryOptions = getGetDetailsForCardJsonIdQueryOptions(
    getDetailsForCardJsonIdBody,
    options,
  );

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: QueryKey;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Searching cards
 * @summary Search cards
 */
export const searchCards = (
  params?: SearchCardsParams,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<SearchCards200>> => {
  return axios.post(`/cards/search`, undefined, {
    ...options,
    params: { ...params, ...options?.params },
  });
};

export const getSearchCardsMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof searchCards>>,
    TError,
    { params?: SearchCardsParams },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof searchCards>>,
  TError,
  { params?: SearchCardsParams },
  TContext
> => {
  const mutationKey = ['searchCards'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof searchCards>>,
    { params?: SearchCardsParams }
  > = (props) => {
    const { params } = props ?? {};

    return searchCards(params, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type SearchCardsMutationResult = NonNullable<
  Awaited<ReturnType<typeof searchCards>>
>;

export type SearchCardsMutationError = AxiosError<unknown>;

/**
 * @summary Search cards
 */
export const useSearchCards = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof searchCards>>,
    TError,
    { params?: SearchCardsParams },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof searchCards>>,
  TError,
  { params?: SearchCardsParams },
  TContext
> => {
  const mutationOptions = getSearchCardsMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Get all artists
 * @summary Get all artists
 */
export const getArtists = (
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<GetArtists200>> => {
  return axios.get(`/cards/artists`, options);
};

export const getGetArtistsQueryKey = () => {
  return [`/cards/artists`] as const;
};

export const getGetArtistsQueryOptions = <
  TData = Awaited<ReturnType<typeof getArtists>>,
  TError = AxiosError<unknown>,
>(options?: {
  query?: UseQueryOptions<
    Awaited<ReturnType<typeof getArtists>>,
    TError,
    TData
  >;
  axios?: AxiosRequestConfig;
}) => {
  const { query: queryOptions, axios: axiosOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetArtistsQueryKey();

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getArtists>>> = ({
    signal,
  }) => getArtists({ signal, ...axiosOptions });

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getArtists>>,
    TError,
    TData
  > & { queryKey: QueryKey };
};

export type GetArtistsQueryResult = NonNullable<
  Awaited<ReturnType<typeof getArtists>>
>;
export type GetArtistsQueryError = AxiosError<unknown>;

/**
 * @summary Get all artists
 */

export function useGetArtists<
  TData = Awaited<ReturnType<typeof getArtists>>,
  TError = AxiosError<unknown>,
>(options?: {
  query?: UseQueryOptions<
    Awaited<ReturnType<typeof getArtists>>,
    TError,
    TData
  >;
  axios?: AxiosRequestConfig;
}): UseQueryResult<TData, TError> & { queryKey: QueryKey } {
  const queryOptions = getGetArtistsQueryOptions(options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: QueryKey;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Get all super_types
 * @summary Get all super_types
 */
export const getSuperTypes = (
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<GetSuperTypes200>> => {
  return axios.get(`/cards/super_types`, options);
};

export const getGetSuperTypesQueryKey = () => {
  return [`/cards/super_types`] as const;
};

export const getGetSuperTypesQueryOptions = <
  TData = Awaited<ReturnType<typeof getSuperTypes>>,
  TError = AxiosError<unknown>,
>(options?: {
  query?: UseQueryOptions<
    Awaited<ReturnType<typeof getSuperTypes>>,
    TError,
    TData
  >;
  axios?: AxiosRequestConfig;
}) => {
  const { query: queryOptions, axios: axiosOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetSuperTypesQueryKey();

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getSuperTypes>>> = ({
    signal,
  }) => getSuperTypes({ signal, ...axiosOptions });

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getSuperTypes>>,
    TError,
    TData
  > & { queryKey: QueryKey };
};

export type GetSuperTypesQueryResult = NonNullable<
  Awaited<ReturnType<typeof getSuperTypes>>
>;
export type GetSuperTypesQueryError = AxiosError<unknown>;

/**
 * @summary Get all super_types
 */

export function useGetSuperTypes<
  TData = Awaited<ReturnType<typeof getSuperTypes>>,
  TError = AxiosError<unknown>,
>(options?: {
  query?: UseQueryOptions<
    Awaited<ReturnType<typeof getSuperTypes>>,
    TError,
    TData
  >;
  axios?: AxiosRequestConfig;
}): UseQueryResult<TData, TError> & { queryKey: QueryKey } {
  const queryOptions = getGetSuperTypesQueryOptions(options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: QueryKey;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Get all sub_types
 * @summary Get all sub_types
 */
export const getSubTypes = (
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<GetSubTypes200>> => {
  return axios.get(`/cards/sub_types`, options);
};

export const getGetSubTypesQueryKey = () => {
  return [`/cards/sub_types`] as const;
};

export const getGetSubTypesQueryOptions = <
  TData = Awaited<ReturnType<typeof getSubTypes>>,
  TError = AxiosError<unknown>,
>(options?: {
  query?: UseQueryOptions<
    Awaited<ReturnType<typeof getSubTypes>>,
    TError,
    TData
  >;
  axios?: AxiosRequestConfig;
}) => {
  const { query: queryOptions, axios: axiosOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetSubTypesQueryKey();

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getSubTypes>>> = ({
    signal,
  }) => getSubTypes({ signal, ...axiosOptions });

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getSubTypes>>,
    TError,
    TData
  > & { queryKey: QueryKey };
};

export type GetSubTypesQueryResult = NonNullable<
  Awaited<ReturnType<typeof getSubTypes>>
>;
export type GetSubTypesQueryError = AxiosError<unknown>;

/**
 * @summary Get all sub_types
 */

export function useGetSubTypes<
  TData = Awaited<ReturnType<typeof getSubTypes>>,
  TError = AxiosError<unknown>,
>(options?: {
  query?: UseQueryOptions<
    Awaited<ReturnType<typeof getSubTypes>>,
    TError,
    TData
  >;
  axios?: AxiosRequestConfig;
}): UseQueryResult<TData, TError> & { queryKey: QueryKey } {
  const queryOptions = getGetSubTypesQueryOptions(options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: QueryKey;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Get all types
 * @summary Get all types
 */
export const getTypes = (
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<GetTypes200>> => {
  return axios.get(`/cards/types`, options);
};

export const getGetTypesQueryKey = () => {
  return [`/cards/types`] as const;
};

export const getGetTypesQueryOptions = <
  TData = Awaited<ReturnType<typeof getTypes>>,
  TError = AxiosError<unknown>,
>(options?: {
  query?: UseQueryOptions<Awaited<ReturnType<typeof getTypes>>, TError, TData>;
  axios?: AxiosRequestConfig;
}) => {
  const { query: queryOptions, axios: axiosOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetTypesQueryKey();

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getTypes>>> = ({
    signal,
  }) => getTypes({ signal, ...axiosOptions });

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getTypes>>,
    TError,
    TData
  > & { queryKey: QueryKey };
};

export type GetTypesQueryResult = NonNullable<
  Awaited<ReturnType<typeof getTypes>>
>;
export type GetTypesQueryError = AxiosError<unknown>;

/**
 * @summary Get all types
 */

export function useGetTypes<
  TData = Awaited<ReturnType<typeof getTypes>>,
  TError = AxiosError<unknown>,
>(options?: {
  query?: UseQueryOptions<Awaited<ReturnType<typeof getTypes>>, TError, TData>;
  axios?: AxiosRequestConfig;
}): UseQueryResult<TData, TError> & { queryKey: QueryKey } {
  const queryOptions = getGetTypesQueryOptions(options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: QueryKey;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}
