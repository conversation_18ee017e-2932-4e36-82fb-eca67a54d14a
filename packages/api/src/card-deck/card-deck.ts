/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import { useMutation } from '@tanstack/react-query';
import type {
  MutationFunction,
  UseMutationOptions,
  UseMutationResult,
} from '@tanstack/react-query';

import axios from 'axios';
import type { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios';

import type {
  AddCardToDeck200,
  AddCardToDeckBody,
  AddLandsToDeck200,
  AddLandsToDeckBody,
  AddSelectionOfCardUsersToDeck200,
  AddSelectionOfCardUsersToDeckBody,
  UpdateCardDeck200,
  UpdateCardDeckBody,
} from '.././model';

type AwaitedInput<T> = PromiseLike<T> | T;

type Awaited<O> = O extends AwaitedInput<infer T> ? T : never;

/**
 * Add a card to the deck
+ deck_board_id (integer) - id of the deck board
+ json_id (string) - json_id of the card to add
 * @deprecated
 * @summary Add Card To Deck
 */
export const addCardToDeck = (
  uuid: string,
  addCardToDeckBody: AddCardToDeckBody,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<AddCardToDeck200>> => {
  return axios.post(`/decks/${uuid}/card_decks`, addCardToDeckBody, options);
};

export const getAddCardToDeckMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof addCardToDeck>>,
    TError,
    { uuid: string; data: AddCardToDeckBody },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof addCardToDeck>>,
  TError,
  { uuid: string; data: AddCardToDeckBody },
  TContext
> => {
  const mutationKey = ['addCardToDeck'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof addCardToDeck>>,
    { uuid: string; data: AddCardToDeckBody }
  > = (props) => {
    const { uuid, data } = props ?? {};

    return addCardToDeck(uuid, data, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type AddCardToDeckMutationResult = NonNullable<
  Awaited<ReturnType<typeof addCardToDeck>>
>;
export type AddCardToDeckMutationBody = AddCardToDeckBody;
export type AddCardToDeckMutationError = AxiosError<unknown>;

/**
 * @deprecated
 * @summary Add Card To Deck
 */
export const useAddCardToDeck = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof addCardToDeck>>,
    TError,
    { uuid: string; data: AddCardToDeckBody },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof addCardToDeck>>,
  TError,
  { uuid: string; data: AddCardToDeckBody },
  TContext
> => {
  const mutationOptions = getAddCardToDeckMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Add a selection of card_users to a deck and auto link them
+ card_ids (array) - array of card_user_ids
 * @summary Add Selection of Card Users to Deck
 */
export const addSelectionOfCardUsersToDeck = (
  uuid: string,
  addSelectionOfCardUsersToDeckBody: AddSelectionOfCardUsersToDeckBody,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<AddSelectionOfCardUsersToDeck200>> => {
  return axios.post(
    `/decks/${uuid}/card_decks/selection`,
    addSelectionOfCardUsersToDeckBody,
    options,
  );
};

export const getAddSelectionOfCardUsersToDeckMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof addSelectionOfCardUsersToDeck>>,
    TError,
    { uuid: string; data: AddSelectionOfCardUsersToDeckBody },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof addSelectionOfCardUsersToDeck>>,
  TError,
  { uuid: string; data: AddSelectionOfCardUsersToDeckBody },
  TContext
> => {
  const mutationKey = ['addSelectionOfCardUsersToDeck'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof addSelectionOfCardUsersToDeck>>,
    { uuid: string; data: AddSelectionOfCardUsersToDeckBody }
  > = (props) => {
    const { uuid, data } = props ?? {};

    return addSelectionOfCardUsersToDeck(uuid, data, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type AddSelectionOfCardUsersToDeckMutationResult = NonNullable<
  Awaited<ReturnType<typeof addSelectionOfCardUsersToDeck>>
>;
export type AddSelectionOfCardUsersToDeckMutationBody =
  AddSelectionOfCardUsersToDeckBody;
export type AddSelectionOfCardUsersToDeckMutationError = AxiosError<unknown>;

/**
 * @summary Add Selection of Card Users to Deck
 */
export const useAddSelectionOfCardUsersToDeck = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof addSelectionOfCardUsersToDeck>>,
    TError,
    { uuid: string; data: AddSelectionOfCardUsersToDeckBody },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof addSelectionOfCardUsersToDeck>>,
  TError,
  { uuid: string; data: AddSelectionOfCardUsersToDeckBody },
  TContext
> => {
  const mutationOptions =
    getAddSelectionOfCardUsersToDeckMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Add the latest land of a selected color to a deck
+ color (string) - color of the land required
 * @summary Add lands to deck
 */
export const addLandsToDeck = (
  uuid: string,
  addLandsToDeckBody: AddLandsToDeckBody,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<AddLandsToDeck200>> => {
  return axios.post(
    `/decks/${uuid}/card_decks/lands`,
    addLandsToDeckBody,
    options,
  );
};

export const getAddLandsToDeckMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof addLandsToDeck>>,
    TError,
    { uuid: string; data: AddLandsToDeckBody },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof addLandsToDeck>>,
  TError,
  { uuid: string; data: AddLandsToDeckBody },
  TContext
> => {
  const mutationKey = ['addLandsToDeck'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof addLandsToDeck>>,
    { uuid: string; data: AddLandsToDeckBody }
  > = (props) => {
    const { uuid, data } = props ?? {};

    return addLandsToDeck(uuid, data, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type AddLandsToDeckMutationResult = NonNullable<
  Awaited<ReturnType<typeof addLandsToDeck>>
>;
export type AddLandsToDeckMutationBody = AddLandsToDeckBody;
export type AddLandsToDeckMutationError = AxiosError<unknown>;

/**
 * @summary Add lands to deck
 */
export const useAddLandsToDeck = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof addLandsToDeck>>,
    TError,
    { uuid: string; data: AddLandsToDeckBody },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof addLandsToDeck>>,
  TError,
  { uuid: string; data: AddLandsToDeckBody },
  TContext
> => {
  const mutationOptions = getAddLandsToDeckMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Remove card from deck
 * @summary Remove Card from Deck
 */
export const removeCardFromDeck = (
  uuid: string,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<void>> => {
  return axios.delete(`/decks/${uuid}/card_decks/:id`, options);
};

export const getRemoveCardFromDeckMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof removeCardFromDeck>>,
    TError,
    { uuid: string },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof removeCardFromDeck>>,
  TError,
  { uuid: string },
  TContext
> => {
  const mutationKey = ['removeCardFromDeck'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof removeCardFromDeck>>,
    { uuid: string }
  > = (props) => {
    const { uuid } = props ?? {};

    return removeCardFromDeck(uuid, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type RemoveCardFromDeckMutationResult = NonNullable<
  Awaited<ReturnType<typeof removeCardFromDeck>>
>;

export type RemoveCardFromDeckMutationError = AxiosError<unknown>;

/**
 * @summary Remove Card from Deck
 */
export const useRemoveCardFromDeck = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof removeCardFromDeck>>,
    TError,
    { uuid: string },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof removeCardFromDeck>>,
  TError,
  { uuid: string },
  TContext
> => {
  const mutationOptions = getRemoveCardFromDeckMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * + deck_board_id (integer) - id of the deck board
+ json_id (string) - json_id of the card to add
 * @summary Update Card Deck
 */
export const updateCardDeck = (
  uuid: string,
  updateCardDeckBody: UpdateCardDeckBody,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<UpdateCardDeck200>> => {
  return axios.patch(
    `/decks/${uuid}/card_decks/:id`,
    updateCardDeckBody,
    options,
  );
};

export const getUpdateCardDeckMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof updateCardDeck>>,
    TError,
    { uuid: string; data: UpdateCardDeckBody },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof updateCardDeck>>,
  TError,
  { uuid: string; data: UpdateCardDeckBody },
  TContext
> => {
  const mutationKey = ['updateCardDeck'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof updateCardDeck>>,
    { uuid: string; data: UpdateCardDeckBody }
  > = (props) => {
    const { uuid, data } = props ?? {};

    return updateCardDeck(uuid, data, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type UpdateCardDeckMutationResult = NonNullable<
  Awaited<ReturnType<typeof updateCardDeck>>
>;
export type UpdateCardDeckMutationBody = UpdateCardDeckBody;
export type UpdateCardDeckMutationError = AxiosError<unknown>;

/**
 * @summary Update Card Deck
 */
export const useUpdateCardDeck = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof updateCardDeck>>,
    TError,
    { uuid: string; data: UpdateCardDeckBody },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof updateCardDeck>>,
  TError,
  { uuid: string; data: UpdateCardDeckBody },
  TContext
> => {
  const mutationOptions = getUpdateCardDeckMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * @summary Link Card User to Card Deck
 */
export const linkCardUserToCardDeck = (
  uuid: string,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<void>> => {
  return axios.patch(`/decks/${uuid}/card_decks/:id/link`, undefined, options);
};

export const getLinkCardUserToCardDeckMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof linkCardUserToCardDeck>>,
    TError,
    { uuid: string },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof linkCardUserToCardDeck>>,
  TError,
  { uuid: string },
  TContext
> => {
  const mutationKey = ['linkCardUserToCardDeck'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof linkCardUserToCardDeck>>,
    { uuid: string }
  > = (props) => {
    const { uuid } = props ?? {};

    return linkCardUserToCardDeck(uuid, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type LinkCardUserToCardDeckMutationResult = NonNullable<
  Awaited<ReturnType<typeof linkCardUserToCardDeck>>
>;

export type LinkCardUserToCardDeckMutationError = AxiosError<unknown>;

/**
 * @summary Link Card User to Card Deck
 */
export const useLinkCardUserToCardDeck = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof linkCardUserToCardDeck>>,
    TError,
    { uuid: string },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof linkCardUserToCardDeck>>,
  TError,
  { uuid: string },
  TContext
> => {
  const mutationOptions = getLinkCardUserToCardDeckMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * @summary Unlink Card User from Card Deck
 */
export const unlinkCardUserFromCardDeck = (
  uuid: string,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<void>> => {
  return axios.delete(`/decks/${uuid}/card_decks/linkable`, options);
};

export const getUnlinkCardUserFromCardDeckMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof unlinkCardUserFromCardDeck>>,
    TError,
    { uuid: string },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof unlinkCardUserFromCardDeck>>,
  TError,
  { uuid: string },
  TContext
> => {
  const mutationKey = ['unlinkCardUserFromCardDeck'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof unlinkCardUserFromCardDeck>>,
    { uuid: string }
  > = (props) => {
    const { uuid } = props ?? {};

    return unlinkCardUserFromCardDeck(uuid, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type UnlinkCardUserFromCardDeckMutationResult = NonNullable<
  Awaited<ReturnType<typeof unlinkCardUserFromCardDeck>>
>;

export type UnlinkCardUserFromCardDeckMutationError = AxiosError<unknown>;

/**
 * @summary Unlink Card User from Card Deck
 */
export const useUnlinkCardUserFromCardDeck = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof unlinkCardUserFromCardDeck>>,
    TError,
    { uuid: string },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof unlinkCardUserFromCardDeck>>,
  TError,
  { uuid: string },
  TContext
> => {
  const mutationOptions = getUnlinkCardUserFromCardDeckMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Remove all cards in deck
 * @summary Remove all Cards from Deck
 */
export const removeAllCardsFromDeck = (
  uuid: string,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<void>> => {
  return axios.delete(`/decks/${uuid}/card_decks/remove_all`, options);
};

export const getRemoveAllCardsFromDeckMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof removeAllCardsFromDeck>>,
    TError,
    { uuid: string },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof removeAllCardsFromDeck>>,
  TError,
  { uuid: string },
  TContext
> => {
  const mutationKey = ['removeAllCardsFromDeck'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof removeAllCardsFromDeck>>,
    { uuid: string }
  > = (props) => {
    const { uuid } = props ?? {};

    return removeAllCardsFromDeck(uuid, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type RemoveAllCardsFromDeckMutationResult = NonNullable<
  Awaited<ReturnType<typeof removeAllCardsFromDeck>>
>;

export type RemoveAllCardsFromDeckMutationError = AxiosError<unknown>;

/**
 * @summary Remove all Cards from Deck
 */
export const useRemoveAllCardsFromDeck = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof removeAllCardsFromDeck>>,
    TError,
    { uuid: string },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof removeAllCardsFromDeck>>,
  TError,
  { uuid: string },
  TContext
> => {
  const mutationOptions = getRemoveAllCardsFromDeckMutationOptions(options);

  return useMutation(mutationOptions);
};
