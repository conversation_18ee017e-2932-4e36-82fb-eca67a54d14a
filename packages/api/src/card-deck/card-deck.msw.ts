/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import { HttpResponse, delay, http } from 'msw';

import type {
  AddCardToDeck200,
  AddLandsToDeck200,
  AddSelectionOfCardUsersToDeck200,
  UpdateCardDeck200,
} from '.././model';

export const getAddCardToDeckResponseMock = (
  overrideResponse: Partial<AddCardToDeck200> = {},
): AddCardToDeck200 => ({ card_deck: {}, card: {}, ...overrideResponse });

export const getAddSelectionOfCardUsersToDeckResponseMock = (
  overrideResponse: Partial<AddSelectionOfCardUsersToDeck200> = {},
): AddSelectionOfCardUsersToDeck200 => ({
  card_deck: {},
  card: {},
  ...overrideResponse,
});

export const getAddLandsToDeckResponseMock = (
  overrideResponse: Partial<AddLandsToDeck200> = {},
): AddLandsToDeck200 => ({ card_deck: {}, card: {}, ...overrideResponse });

export const getUpdateCardDeckResponseMock = (
  overrideResponse: Partial<UpdateCardDeck200> = {},
): UpdateCardDeck200 => ({ card_deck: {}, card: {}, ...overrideResponse });

export const getAddCardToDeckMockHandler = (
  overrideResponse?:
    | AddCardToDeck200
    | ((
        info: Parameters<Parameters<typeof http.post>[1]>[0],
      ) => Promise<AddCardToDeck200> | AddCardToDeck200),
) => {
  return http.post('*/decks/:uuid/card_decks', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getAddCardToDeckResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getAddSelectionOfCardUsersToDeckMockHandler = (
  overrideResponse?:
    | AddSelectionOfCardUsersToDeck200
    | ((
        info: Parameters<Parameters<typeof http.post>[1]>[0],
      ) =>
        | Promise<AddSelectionOfCardUsersToDeck200>
        | AddSelectionOfCardUsersToDeck200),
) => {
  return http.post('*/decks/:uuid/card_decks/selection', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getAddSelectionOfCardUsersToDeckResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getAddLandsToDeckMockHandler = (
  overrideResponse?:
    | AddLandsToDeck200
    | ((
        info: Parameters<Parameters<typeof http.post>[1]>[0],
      ) => Promise<AddLandsToDeck200> | AddLandsToDeck200),
) => {
  return http.post('*/decks/:uuid/card_decks/lands', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getAddLandsToDeckResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getRemoveCardFromDeckMockHandler = (
  overrideResponse?:
    | void
    | ((
        info: Parameters<Parameters<typeof http.delete>[1]>[0],
      ) => Promise<void> | void),
) => {
  return http.delete('*/decks/:uuid/card_decks/:id', async (info) => {
    await delay(1000);
    if (typeof overrideResponse === 'function') {
      await overrideResponse(info);
    }
    return new HttpResponse(null, { status: 204 });
  });
};

export const getUpdateCardDeckMockHandler = (
  overrideResponse?:
    | UpdateCardDeck200
    | ((
        info: Parameters<Parameters<typeof http.patch>[1]>[0],
      ) => Promise<UpdateCardDeck200> | UpdateCardDeck200),
) => {
  return http.patch('*/decks/:uuid/card_decks/:id', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getUpdateCardDeckResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getLinkCardUserToCardDeckMockHandler = (
  overrideResponse?:
    | void
    | ((
        info: Parameters<Parameters<typeof http.patch>[1]>[0],
      ) => Promise<void> | void),
) => {
  return http.patch('*/decks/:uuid/card_decks/:id/link', async (info) => {
    await delay(1000);
    if (typeof overrideResponse === 'function') {
      await overrideResponse(info);
    }
    return new HttpResponse(null, { status: 200 });
  });
};

export const getUnlinkCardUserFromCardDeckMockHandler = (
  overrideResponse?:
    | void
    | ((
        info: Parameters<Parameters<typeof http.delete>[1]>[0],
      ) => Promise<void> | void),
) => {
  return http.delete('*/decks/:uuid/card_decks/linkable', async (info) => {
    await delay(1000);
    if (typeof overrideResponse === 'function') {
      await overrideResponse(info);
    }
    return new HttpResponse(null, { status: 200 });
  });
};

export const getRemoveAllCardsFromDeckMockHandler = (
  overrideResponse?:
    | void
    | ((
        info: Parameters<Parameters<typeof http.delete>[1]>[0],
      ) => Promise<void> | void),
) => {
  return http.delete('*/decks/:uuid/card_decks/remove_all', async (info) => {
    await delay(1000);
    if (typeof overrideResponse === 'function') {
      await overrideResponse(info);
    }
    return new HttpResponse(null, { status: 204 });
  });
};
export const getCardDeckMock = () => [
  getAddCardToDeckMockHandler(),
  getAddSelectionOfCardUsersToDeckMockHandler(),
  getAddLandsToDeckMockHandler(),
  getRemoveCardFromDeckMockHandler(),
  getUpdateCardDeckMockHandler(),
  getLinkCardUserToCardDeckMockHandler(),
  getUnlinkCardUserFromCardDeckMockHandler(),
  getRemoveAllCardsFromDeckMockHandler(),
];
