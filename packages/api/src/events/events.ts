/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import { useQuery } from '@tanstack/react-query';
import type {
  QueryFunction,
  QueryKey,
  UseQueryOptions,
  UseQueryResult,
} from '@tanstack/react-query';

import axios from 'axios';
import type { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios';

import type { GetAllEvents200 } from '.././model';

type AwaitedInput<T> = PromiseLike<T> | T;

type Awaited<O> = O extends AwaitedInput<infer T> ? T : never;

/**
 * Get all the events for the current logged in user
 * @summary Get All Events
 */
export const getAllEvents = (
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<GetAllEvents200>> => {
  return axios.get(`/events`, options);
};

export const getGetAllEventsQueryKey = () => {
  return [`/events`] as const;
};

export const getGetAllEventsQueryOptions = <
  TData = Awaited<ReturnType<typeof getAllEvents>>,
  TError = AxiosError<unknown>,
>(options?: {
  query?: UseQueryOptions<
    Awaited<ReturnType<typeof getAllEvents>>,
    TError,
    TData
  >;
  axios?: AxiosRequestConfig;
}) => {
  const { query: queryOptions, axios: axiosOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetAllEventsQueryKey();

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getAllEvents>>> = ({
    signal,
  }) => getAllEvents({ signal, ...axiosOptions });

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getAllEvents>>,
    TError,
    TData
  > & { queryKey: QueryKey };
};

export type GetAllEventsQueryResult = NonNullable<
  Awaited<ReturnType<typeof getAllEvents>>
>;
export type GetAllEventsQueryError = AxiosError<unknown>;

/**
 * @summary Get All Events
 */

export function useGetAllEvents<
  TData = Awaited<ReturnType<typeof getAllEvents>>,
  TError = AxiosError<unknown>,
>(options?: {
  query?: UseQueryOptions<
    Awaited<ReturnType<typeof getAllEvents>>,
    TError,
    TData
  >;
  axios?: AxiosRequestConfig;
}): UseQueryResult<TData, TError> & { queryKey: QueryKey } {
  const queryOptions = getGetAllEventsQueryOptions(options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: QueryKey;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}
