/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import { useQuery } from '@tanstack/react-query';
import type {
  QueryFunction,
  QueryKey,
  UseQueryOptions,
  UseQueryResult,
} from '@tanstack/react-query';

import axios from 'axios';
import type { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios';

import type { GetAllLorcanaFinishes200 } from '.././model';

type AwaitedInput<T> = PromiseLike<T> | T;

type Awaited<O> = O extends AwaitedInput<infer T> ? T : never;

/**
 * Get all lorcana finishes
 * @summary Get all lorcana finishes
 */
export const getAllLorcanaFinishes = (
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<GetAllLorcanaFinishes200>> => {
  return axios.get(`/lorcana_finishes`, options);
};

export const getGetAllLorcanaFinishesQueryKey = () => {
  return [`/lorcana_finishes`] as const;
};

export const getGetAllLorcanaFinishesQueryOptions = <
  TData = Awaited<ReturnType<typeof getAllLorcanaFinishes>>,
  TError = AxiosError<unknown>,
>(options?: {
  query?: UseQueryOptions<
    Awaited<ReturnType<typeof getAllLorcanaFinishes>>,
    TError,
    TData
  >;
  axios?: AxiosRequestConfig;
}) => {
  const { query: queryOptions, axios: axiosOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetAllLorcanaFinishesQueryKey();

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof getAllLorcanaFinishes>>
  > = ({ signal }) => getAllLorcanaFinishes({ signal, ...axiosOptions });

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getAllLorcanaFinishes>>,
    TError,
    TData
  > & { queryKey: QueryKey };
};

export type GetAllLorcanaFinishesQueryResult = NonNullable<
  Awaited<ReturnType<typeof getAllLorcanaFinishes>>
>;
export type GetAllLorcanaFinishesQueryError = AxiosError<unknown>;

/**
 * @summary Get all lorcana finishes
 */

export function useGetAllLorcanaFinishes<
  TData = Awaited<ReturnType<typeof getAllLorcanaFinishes>>,
  TError = AxiosError<unknown>,
>(options?: {
  query?: UseQueryOptions<
    Awaited<ReturnType<typeof getAllLorcanaFinishes>>,
    TError,
    TData
  >;
  axios?: AxiosRequestConfig;
}): UseQueryResult<TData, TError> & { queryKey: QueryKey } {
  const queryOptions = getGetAllLorcanaFinishesQueryOptions(options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: QueryKey;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}
