/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import { faker } from '@faker-js/faker';

import { HttpResponse, delay, http } from 'msw';

import type { GetAllLorcanaFinishes200 } from '.././model';

export const getGetAllLorcanaFinishesResponseMock = (
  overrideResponse: Partial<GetAllLorcanaFinishes200> = {},
): GetAllLorcanaFinishes200 => ({
  finishes: faker.helpers.arrayElement([
    Array.from(
      { length: faker.number.int({ min: 1, max: 10 }) },
      (_, i) => i + 1,
    ).map(() => ({})),
    undefined,
  ]),
  ...overrideResponse,
});

export const getGetAllLorcanaFinishesMockHandler = (
  overrideResponse?:
    | GetAllLorcanaFinishes200
    | ((
        info: Parameters<Parameters<typeof http.get>[1]>[0],
      ) => Promise<GetAllLorcanaFinishes200> | GetAllLorcanaFinishes200),
) => {
  return http.get('*/lorcana_finishes', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getGetAllLorcanaFinishesResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};
export const getLorcanaFinishesMock = () => [
  getGetAllLorcanaFinishesMockHandler(),
];
