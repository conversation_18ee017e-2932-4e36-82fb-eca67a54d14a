/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import { useQuery } from '@tanstack/react-query';
import type {
  QueryFunction,
  QueryKey,
  UseQueryOptions,
  UseQueryResult,
} from '@tanstack/react-query';

import axios from 'axios';
import type { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios';

import type { GetBanners200 } from '.././model';

type AwaitedInput<T> = PromiseLike<T> | T;

type Awaited<O> = O extends AwaitedInput<infer T> ? T : never;

/**
 * @summary Get Banner Contents
 */
export const getBanners = (
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<GetBanners200>> => {
  return axios.get(`/banners/latest`, options);
};

export const getGetBannersQueryKey = () => {
  return [`/banners/latest`] as const;
};

export const getGetBannersQueryOptions = <
  TData = Awaited<ReturnType<typeof getBanners>>,
  TError = AxiosError<unknown>,
>(options?: {
  query?: UseQueryOptions<
    Awaited<ReturnType<typeof getBanners>>,
    TError,
    TData
  >;
  axios?: AxiosRequestConfig;
}) => {
  const { query: queryOptions, axios: axiosOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetBannersQueryKey();

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getBanners>>> = ({
    signal,
  }) => getBanners({ signal, ...axiosOptions });

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getBanners>>,
    TError,
    TData
  > & { queryKey: QueryKey };
};

export type GetBannersQueryResult = NonNullable<
  Awaited<ReturnType<typeof getBanners>>
>;
export type GetBannersQueryError = AxiosError<unknown>;

/**
 * @summary Get Banner Contents
 */

export function useGetBanners<
  TData = Awaited<ReturnType<typeof getBanners>>,
  TError = AxiosError<unknown>,
>(options?: {
  query?: UseQueryOptions<
    Awaited<ReturnType<typeof getBanners>>,
    TError,
    TData
  >;
  axios?: AxiosRequestConfig;
}): UseQueryResult<TData, TError> & { queryKey: QueryKey } {
  const queryOptions = getGetBannersQueryOptions(options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: QueryKey;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}
