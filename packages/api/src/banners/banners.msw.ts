/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import { faker } from '@faker-js/faker';

import { HttpResponse, delay, http } from 'msw';

import type { GetBanners200 } from '.././model';

export const getGetBannersResponseMock = (
  overrideResponse: Partial<GetBanners200> = {},
): GetBanners200 => ({
  id: faker.number.int({ min: undefined, max: undefined }),
  message: faker.string.alpha(20),
  button: faker.string.alpha(20),
  url: faker.string.alpha(20),
  token: faker.string.alpha(20),
  published: faker.string.alpha(20),
  created_at: faker.string.alpha(20),
  updated_at: faker.string.alpha(20),
  ...overrideResponse,
});

export const getGetBannersMockHandler = (
  overrideResponse?:
    | GetBanners200
    | ((
        info: Parameters<Parameters<typeof http.get>[1]>[0],
      ) => Promise<GetBanners200> | GetBanners200),
) => {
  return http.get('*/banners/latest', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getGetBannersResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};
export const getBannersMock = () => [getGetBannersMockHandler()];
