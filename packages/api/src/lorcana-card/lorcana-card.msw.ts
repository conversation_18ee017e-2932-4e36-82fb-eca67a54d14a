/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import { faker } from '@faker-js/faker';

import { HttpResponse, delay, http } from 'msw';

import type {
  GetAllLorcanaCard200,
  GetLorcanaCardsPrintings200,
  GetLorcanaVersion200,
  SearchLorcanaCards200,
} from '.././model';

export const getGetAllLorcanaCardResponseMock = (
  overrideResponse: Partial<GetAllLorcanaCard200> = {},
): GetAllLorcanaCard200 => ({
  cards: Array.from(
    { length: faker.number.int({ min: 1, max: 10 }) },
    (_, i) => i + 1,
  ).map(() => ({})),
  ...overrideResponse,
});

export const getSearchLorcanaCardsResponseMock = (
  overrideResponse: Partial<SearchLorcanaCards200> = {},
): SearchLorcanaCards200 => ({
  cards: Array.from(
    { length: faker.number.int({ min: 1, max: 10 }) },
    (_, i) => i + 1,
  ).map(() => ({})),
  ...overrideResponse,
});

export const getGetLorcanaCardsPrintingsResponseMock = (
  overrideResponse: Partial<GetLorcanaCardsPrintings200> = {},
): GetLorcanaCardsPrintings200 => ({ printings: {}, ...overrideResponse });

export const getGetLorcanaVersionResponseMock = (
  overrideResponse: Partial<GetLorcanaVersion200> = {},
): GetLorcanaVersion200 => ({
  version: faker.string.alpha(20),
  ...overrideResponse,
});

export const getGetAllLorcanaCardMockHandler = (
  overrideResponse?:
    | GetAllLorcanaCard200
    | ((
        info: Parameters<Parameters<typeof http.get>[1]>[0],
      ) => Promise<GetAllLorcanaCard200> | GetAllLorcanaCard200),
) => {
  return http.get('*/lorcana_cards', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getGetAllLorcanaCardResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getSearchLorcanaCardsMockHandler = (
  overrideResponse?:
    | SearchLorcanaCards200
    | ((
        info: Parameters<Parameters<typeof http.get>[1]>[0],
      ) => Promise<SearchLorcanaCards200> | SearchLorcanaCards200),
) => {
  return http.get('*/lorcana_cards/search', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getSearchLorcanaCardsResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getGetLorcanaCardsPrintingsMockHandler = (
  overrideResponse?:
    | GetLorcanaCardsPrintings200
    | ((
        info: Parameters<Parameters<typeof http.post>[1]>[0],
      ) => Promise<GetLorcanaCardsPrintings200> | GetLorcanaCardsPrintings200),
) => {
  return http.post('*/lorcana_cards/printings', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getGetLorcanaCardsPrintingsResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getGetLorcanaVersionMockHandler = (
  overrideResponse?:
    | GetLorcanaVersion200
    | ((
        info: Parameters<Parameters<typeof http.get>[1]>[0],
      ) => Promise<GetLorcanaVersion200> | GetLorcanaVersion200),
) => {
  return http.get('*/lorcana_cards/version', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getGetLorcanaVersionResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getUpdateLorcanaVersionMockHandler = (
  overrideResponse?:
    | void
    | ((
        info: Parameters<Parameters<typeof http.get>[1]>[0],
      ) => Promise<void> | void),
) => {
  return http.get('*/lorcana_cards/update_version', async (info) => {
    await delay(1000);
    if (typeof overrideResponse === 'function') {
      await overrideResponse(info);
    }
    return new HttpResponse(null, { status: 200 });
  });
};
export const getLorcanaCardMock = () => [
  getGetAllLorcanaCardMockHandler(),
  getSearchLorcanaCardsMockHandler(),
  getGetLorcanaCardsPrintingsMockHandler(),
  getGetLorcanaVersionMockHandler(),
  getUpdateLorcanaVersionMockHandler(),
];
