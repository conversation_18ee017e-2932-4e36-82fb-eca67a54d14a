/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import { useMutation, useQuery } from '@tanstack/react-query';
import type {
  MutationFunction,
  QueryFunction,
  QueryKey,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult,
} from '@tanstack/react-query';

import axios from 'axios';
import type { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios';

import type {
  GetAllLorcanaCard200,
  GetLorcanaCardsPrintings200,
  GetLorcanaCardsPrintingsBody,
  GetLorcanaVersion200,
  SearchLorcanaCards200,
  SearchLorcanaCardsParams,
  UpdateLorcanaVersionParams,
} from '.././model';

type AwaitedInput<T> = PromiseLike<T> | T;

type Awaited<O> = O extends AwaitedInput<infer T> ? T : never;

/**
 * Get all lorcana cards
 * @summary Get All Lorcana Cards
 */
export const getAllLorcanaCard = (
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<GetAllLorcanaCard200>> => {
  return axios.get(`/lorcana_cards`, options);
};

export const getGetAllLorcanaCardQueryKey = () => {
  return [`/lorcana_cards`] as const;
};

export const getGetAllLorcanaCardQueryOptions = <
  TData = Awaited<ReturnType<typeof getAllLorcanaCard>>,
  TError = AxiosError<unknown>,
>(options?: {
  query?: UseQueryOptions<
    Awaited<ReturnType<typeof getAllLorcanaCard>>,
    TError,
    TData
  >;
  axios?: AxiosRequestConfig;
}) => {
  const { query: queryOptions, axios: axiosOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetAllLorcanaCardQueryKey();

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof getAllLorcanaCard>>
  > = ({ signal }) => getAllLorcanaCard({ signal, ...axiosOptions });

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getAllLorcanaCard>>,
    TError,
    TData
  > & { queryKey: QueryKey };
};

export type GetAllLorcanaCardQueryResult = NonNullable<
  Awaited<ReturnType<typeof getAllLorcanaCard>>
>;
export type GetAllLorcanaCardQueryError = AxiosError<unknown>;

/**
 * @summary Get All Lorcana Cards
 */

export function useGetAllLorcanaCard<
  TData = Awaited<ReturnType<typeof getAllLorcanaCard>>,
  TError = AxiosError<unknown>,
>(options?: {
  query?: UseQueryOptions<
    Awaited<ReturnType<typeof getAllLorcanaCard>>,
    TError,
    TData
  >;
  axios?: AxiosRequestConfig;
}): UseQueryResult<TData, TError> & { queryKey: QueryKey } {
  const queryOptions = getGetAllLorcanaCardQueryOptions(options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: QueryKey;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Search all `lorcana cards`. This API is both public and private, if the user is logged in an owned count is returned, if they are not logged in, the owned count fields are not present.
 * @summary Search all cards
 */
export const searchLorcanaCards = (
  params?: SearchLorcanaCardsParams,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<SearchLorcanaCards200>> => {
  return axios.get(`/lorcana_cards/search`, {
    ...options,
    params: { ...params, ...options?.params },
  });
};

export const getSearchLorcanaCardsQueryKey = (
  params?: SearchLorcanaCardsParams,
) => {
  return [`/lorcana_cards/search`, ...(params ? [params] : [])] as const;
};

export const getSearchLorcanaCardsQueryOptions = <
  TData = Awaited<ReturnType<typeof searchLorcanaCards>>,
  TError = AxiosError<unknown>,
>(
  params?: SearchLorcanaCardsParams,
  options?: {
    query?: UseQueryOptions<
      Awaited<ReturnType<typeof searchLorcanaCards>>,
      TError,
      TData
    >;
    axios?: AxiosRequestConfig;
  },
) => {
  const { query: queryOptions, axios: axiosOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ?? getSearchLorcanaCardsQueryKey(params);

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof searchLorcanaCards>>
  > = ({ signal }) => searchLorcanaCards(params, { signal, ...axiosOptions });

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof searchLorcanaCards>>,
    TError,
    TData
  > & { queryKey: QueryKey };
};

export type SearchLorcanaCardsQueryResult = NonNullable<
  Awaited<ReturnType<typeof searchLorcanaCards>>
>;
export type SearchLorcanaCardsQueryError = AxiosError<unknown>;

/**
 * @summary Search all cards
 */

export function useSearchLorcanaCards<
  TData = Awaited<ReturnType<typeof searchLorcanaCards>>,
  TError = AxiosError<unknown>,
>(
  params?: SearchLorcanaCardsParams,
  options?: {
    query?: UseQueryOptions<
      Awaited<ReturnType<typeof searchLorcanaCards>>,
      TError,
      TData
    >;
    axios?: AxiosRequestConfig;
  },
): UseQueryResult<TData, TError> & { queryKey: QueryKey } {
  const queryOptions = getSearchLorcanaCardsQueryOptions(params, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: QueryKey;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * + Duplicate UUIDs are ignored 
+ Response is sorted by release date 
+ Response is empty when there are no shared printings 
+ Invalid ids respond with 'not found'
 * @summary Get printing details for lorcana cards by UUIDs
 */
export const getLorcanaCardsPrintings = (
  getLorcanaCardsPrintingsBody: GetLorcanaCardsPrintingsBody,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<GetLorcanaCardsPrintings200>> => {
  return axios.post(
    `/lorcana_cards/printings`,
    getLorcanaCardsPrintingsBody,
    options,
  );
};

export const getGetLorcanaCardsPrintingsMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof getLorcanaCardsPrintings>>,
    TError,
    { data: GetLorcanaCardsPrintingsBody },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof getLorcanaCardsPrintings>>,
  TError,
  { data: GetLorcanaCardsPrintingsBody },
  TContext
> => {
  const mutationKey = ['getLorcanaCardsPrintings'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof getLorcanaCardsPrintings>>,
    { data: GetLorcanaCardsPrintingsBody }
  > = (props) => {
    const { data } = props ?? {};

    return getLorcanaCardsPrintings(data, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type GetLorcanaCardsPrintingsMutationResult = NonNullable<
  Awaited<ReturnType<typeof getLorcanaCardsPrintings>>
>;
export type GetLorcanaCardsPrintingsMutationBody = GetLorcanaCardsPrintingsBody;
export type GetLorcanaCardsPrintingsMutationError = AxiosError<unknown>;

/**
 * @summary Get printing details for lorcana cards by UUIDs
 */
export const useGetLorcanaCardsPrintings = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof getLorcanaCardsPrintings>>,
    TError,
    { data: GetLorcanaCardsPrintingsBody },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof getLorcanaCardsPrintings>>,
  TError,
  { data: GetLorcanaCardsPrintingsBody },
  TContext
> => {
  const mutationOptions = getGetLorcanaCardsPrintingsMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Get current version
 * @summary Get current version
 */
export const getLorcanaVersion = (
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<GetLorcanaVersion200>> => {
  return axios.get(`/lorcana_cards/version`, options);
};

export const getGetLorcanaVersionQueryKey = () => {
  return [`/lorcana_cards/version`] as const;
};

export const getGetLorcanaVersionQueryOptions = <
  TData = Awaited<ReturnType<typeof getLorcanaVersion>>,
  TError = AxiosError<unknown>,
>(options?: {
  query?: UseQueryOptions<
    Awaited<ReturnType<typeof getLorcanaVersion>>,
    TError,
    TData
  >;
  axios?: AxiosRequestConfig;
}) => {
  const { query: queryOptions, axios: axiosOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetLorcanaVersionQueryKey();

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof getLorcanaVersion>>
  > = ({ signal }) => getLorcanaVersion({ signal, ...axiosOptions });

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getLorcanaVersion>>,
    TError,
    TData
  > & { queryKey: QueryKey };
};

export type GetLorcanaVersionQueryResult = NonNullable<
  Awaited<ReturnType<typeof getLorcanaVersion>>
>;
export type GetLorcanaVersionQueryError = AxiosError<unknown>;

/**
 * @summary Get current version
 */

export function useGetLorcanaVersion<
  TData = Awaited<ReturnType<typeof getLorcanaVersion>>,
  TError = AxiosError<unknown>,
>(options?: {
  query?: UseQueryOptions<
    Awaited<ReturnType<typeof getLorcanaVersion>>,
    TError,
    TData
  >;
  axios?: AxiosRequestConfig;
}): UseQueryResult<TData, TError> & { queryKey: QueryKey } {
  const queryOptions = getGetLorcanaVersionQueryOptions(options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: QueryKey;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Update current version
 * @summary Update current version
 */
export const updateLorcanaVersion = (
  params: UpdateLorcanaVersionParams,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<void>> => {
  return axios.get(`/lorcana_cards/update_version`, {
    ...options,
    params: { ...params, ...options?.params },
  });
};

export const getUpdateLorcanaVersionQueryKey = (
  params: UpdateLorcanaVersionParams,
) => {
  return [
    `/lorcana_cards/update_version`,
    ...(params ? [params] : []),
  ] as const;
};

export const getUpdateLorcanaVersionQueryOptions = <
  TData = Awaited<ReturnType<typeof updateLorcanaVersion>>,
  TError = AxiosError<unknown>,
>(
  params: UpdateLorcanaVersionParams,
  options?: {
    query?: UseQueryOptions<
      Awaited<ReturnType<typeof updateLorcanaVersion>>,
      TError,
      TData
    >;
    axios?: AxiosRequestConfig;
  },
) => {
  const { query: queryOptions, axios: axiosOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ?? getUpdateLorcanaVersionQueryKey(params);

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof updateLorcanaVersion>>
  > = ({ signal }) => updateLorcanaVersion(params, { signal, ...axiosOptions });

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof updateLorcanaVersion>>,
    TError,
    TData
  > & { queryKey: QueryKey };
};

export type UpdateLorcanaVersionQueryResult = NonNullable<
  Awaited<ReturnType<typeof updateLorcanaVersion>>
>;
export type UpdateLorcanaVersionQueryError = AxiosError<unknown>;

/**
 * @summary Update current version
 */

export function useUpdateLorcanaVersion<
  TData = Awaited<ReturnType<typeof updateLorcanaVersion>>,
  TError = AxiosError<unknown>,
>(
  params: UpdateLorcanaVersionParams,
  options?: {
    query?: UseQueryOptions<
      Awaited<ReturnType<typeof updateLorcanaVersion>>,
      TError,
      TData
    >;
    axios?: AxiosRequestConfig;
  },
): UseQueryResult<TData, TError> & { queryKey: QueryKey } {
  const queryOptions = getUpdateLorcanaVersionQueryOptions(params, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: QueryKey;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}
