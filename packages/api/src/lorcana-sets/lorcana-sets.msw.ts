/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import { faker } from '@faker-js/faker';

import { HttpResponse, delay, http } from 'msw';

import type { GetAllLorcanaSets200 } from '.././model';

export const getGetAllLorcanaSetsResponseMock = (
  overrideResponse: Partial<GetAllLorcanaSets200> = {},
): GetAllLorcanaSets200 => ({
  card_sets: faker.helpers.arrayElement([
    Array.from(
      { length: faker.number.int({ min: 1, max: 10 }) },
      (_, i) => i + 1,
    ).map(() => ({})),
    undefined,
  ]),
  ...overrideResponse,
});

export const getGetAllLorcanaSetsMockHandler = (
  overrideResponse?:
    | GetAllLorcanaSets200
    | ((
        info: Parameters<Parameters<typeof http.get>[1]>[0],
      ) => Promise<GetAllLorcanaSets200> | GetAllLorcanaSets200),
) => {
  return http.get('*/lorcana_sets', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getGetAllLorcanaSetsResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};
export const getLorcanaSetsMock = () => [getGetAllLorcanaSetsMockHandler()];
