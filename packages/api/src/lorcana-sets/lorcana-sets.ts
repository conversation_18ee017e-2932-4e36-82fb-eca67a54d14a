/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import { useQuery } from '@tanstack/react-query';
import type {
  QueryFunction,
  QueryKey,
  UseQueryOptions,
  UseQueryResult,
} from '@tanstack/react-query';

import axios from 'axios';
import type { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios';

import type { GetAllLorcanaSets200, GetAllLorcanaSetsParams } from '.././model';

type AwaitedInput<T> = PromiseLike<T> | T;

type Awaited<O> = O extends AwaitedInput<infer T> ? T : never;

/**
 * Get all Lorcana Sets
 * @summary Get All Lorcana Sets
 */
export const getAllLorcanaSets = (
  params?: GetAllLorcanaSetsParams,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<GetAllLorcanaSets200>> => {
  return axios.get(`/lorcana_sets`, {
    ...options,
    params: { ...params, ...options?.params },
  });
};

export const getGetAllLorcanaSetsQueryKey = (
  params?: GetAllLorcanaSetsParams,
) => {
  return [`/lorcana_sets`, ...(params ? [params] : [])] as const;
};

export const getGetAllLorcanaSetsQueryOptions = <
  TData = Awaited<ReturnType<typeof getAllLorcanaSets>>,
  TError = AxiosError<unknown>,
>(
  params?: GetAllLorcanaSetsParams,
  options?: {
    query?: UseQueryOptions<
      Awaited<ReturnType<typeof getAllLorcanaSets>>,
      TError,
      TData
    >;
    axios?: AxiosRequestConfig;
  },
) => {
  const { query: queryOptions, axios: axiosOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ?? getGetAllLorcanaSetsQueryKey(params);

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof getAllLorcanaSets>>
  > = ({ signal }) => getAllLorcanaSets(params, { signal, ...axiosOptions });

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getAllLorcanaSets>>,
    TError,
    TData
  > & { queryKey: QueryKey };
};

export type GetAllLorcanaSetsQueryResult = NonNullable<
  Awaited<ReturnType<typeof getAllLorcanaSets>>
>;
export type GetAllLorcanaSetsQueryError = AxiosError<unknown>;

/**
 * @summary Get All Lorcana Sets
 */

export function useGetAllLorcanaSets<
  TData = Awaited<ReturnType<typeof getAllLorcanaSets>>,
  TError = AxiosError<unknown>,
>(
  params?: GetAllLorcanaSetsParams,
  options?: {
    query?: UseQueryOptions<
      Awaited<ReturnType<typeof getAllLorcanaSets>>,
      TError,
      TData
    >;
    axios?: AxiosRequestConfig;
  },
): UseQueryResult<TData, TError> & { queryKey: QueryKey } {
  const queryOptions = getGetAllLorcanaSetsQueryOptions(params, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: QueryKey;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}
