/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import { useQuery } from '@tanstack/react-query';
import type {
  QueryFunction,
  QueryKey,
  UseQueryOptions,
  UseQueryResult,
} from '@tanstack/react-query';

import axios from 'axios';
import type { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios';

import type { GetAllLorcanaSkus200 } from '.././model';

type AwaitedInput<T> = PromiseLike<T> | T;

type Awaited<O> = O extends AwaitedInput<infer T> ? T : never;

/**
 * Get all lorcana skus
 * @summary Get all lorcana skus
 */
export const getAllLorcanaSkus = (
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<GetAllLorcanaSkus200>> => {
  return axios.get(`/lorcana_skus`, options);
};

export const getGetAllLorcanaSkusQueryKey = () => {
  return [`/lorcana_skus`] as const;
};

export const getGetAllLorcanaSkusQueryOptions = <
  TData = Awaited<ReturnType<typeof getAllLorcanaSkus>>,
  TError = AxiosError<unknown>,
>(options?: {
  query?: UseQueryOptions<
    Awaited<ReturnType<typeof getAllLorcanaSkus>>,
    TError,
    TData
  >;
  axios?: AxiosRequestConfig;
}) => {
  const { query: queryOptions, axios: axiosOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetAllLorcanaSkusQueryKey();

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof getAllLorcanaSkus>>
  > = ({ signal }) => getAllLorcanaSkus({ signal, ...axiosOptions });

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getAllLorcanaSkus>>,
    TError,
    TData
  > & { queryKey: QueryKey };
};

export type GetAllLorcanaSkusQueryResult = NonNullable<
  Awaited<ReturnType<typeof getAllLorcanaSkus>>
>;
export type GetAllLorcanaSkusQueryError = AxiosError<unknown>;

/**
 * @summary Get all lorcana skus
 */

export function useGetAllLorcanaSkus<
  TData = Awaited<ReturnType<typeof getAllLorcanaSkus>>,
  TError = AxiosError<unknown>,
>(options?: {
  query?: UseQueryOptions<
    Awaited<ReturnType<typeof getAllLorcanaSkus>>,
    TError,
    TData
  >;
  axios?: AxiosRequestConfig;
}): UseQueryResult<TData, TError> & { queryKey: QueryKey } {
  const queryOptions = getGetAllLorcanaSkusQueryOptions(options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: QueryKey;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}
