/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import { useQuery } from '@tanstack/react-query';
import type {
  QueryFunction,
  QueryKey,
  UseQueryOptions,
  UseQueryResult,
} from '@tanstack/react-query';

import axios from 'axios';
import type { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios';

import type {
  GetImportExportCSVExportParams,
  GetImportExportServices200,
} from '.././model';

type AwaitedInput<T> = PromiseLike<T> | T;

type Awaited<O> = O extends AwaitedInput<infer T> ? T : never;

/**
 * Show full range and availability of import and export formats. Availability is based on user's roles and active subscriptions
 * @summary Show Import/Export Service Manifest
 */
export const getImportExportServices = (
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<GetImportExportServices200>> => {
  return axios.get(`/import_export/manifest`, options);
};

export const getGetImportExportServicesQueryKey = () => {
  return [`/import_export/manifest`] as const;
};

export const getGetImportExportServicesQueryOptions = <
  TData = Awaited<ReturnType<typeof getImportExportServices>>,
  TError = AxiosError<unknown>,
>(options?: {
  query?: UseQueryOptions<
    Awaited<ReturnType<typeof getImportExportServices>>,
    TError,
    TData
  >;
  axios?: AxiosRequestConfig;
}) => {
  const { query: queryOptions, axios: axiosOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ?? getGetImportExportServicesQueryKey();

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof getImportExportServices>>
  > = ({ signal }) => getImportExportServices({ signal, ...axiosOptions });

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getImportExportServices>>,
    TError,
    TData
  > & { queryKey: QueryKey };
};

export type GetImportExportServicesQueryResult = NonNullable<
  Awaited<ReturnType<typeof getImportExportServices>>
>;
export type GetImportExportServicesQueryError = AxiosError<unknown>;

/**
 * @summary Show Import/Export Service Manifest
 */

export function useGetImportExportServices<
  TData = Awaited<ReturnType<typeof getImportExportServices>>,
  TError = AxiosError<unknown>,
>(options?: {
  query?: UseQueryOptions<
    Awaited<ReturnType<typeof getImportExportServices>>,
    TError,
    TData
  >;
  axios?: AxiosRequestConfig;
}): UseQueryResult<TData, TError> & { queryKey: QueryKey } {
  const queryOptions = getGetImportExportServicesQueryOptions(options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: QueryKey;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Queues a CSV export job. A link to the resulting CSV is emailed to the user
 * @summary Export collection
 */
export const getImportExportCSVExport = (
  params?: GetImportExportCSVExportParams,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<void>> => {
  return axios.get(`/import_export/export`, {
    ...options,
    params: { ...params, ...options?.params },
  });
};

export const getGetImportExportCSVExportQueryKey = (
  params?: GetImportExportCSVExportParams,
) => {
  return [`/import_export/export`, ...(params ? [params] : [])] as const;
};

export const getGetImportExportCSVExportQueryOptions = <
  TData = Awaited<ReturnType<typeof getImportExportCSVExport>>,
  TError = AxiosError<unknown>,
>(
  params?: GetImportExportCSVExportParams,
  options?: {
    query?: UseQueryOptions<
      Awaited<ReturnType<typeof getImportExportCSVExport>>,
      TError,
      TData
    >;
    axios?: AxiosRequestConfig;
  },
) => {
  const { query: queryOptions, axios: axiosOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ?? getGetImportExportCSVExportQueryKey(params);

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof getImportExportCSVExport>>
  > = ({ signal }) =>
    getImportExportCSVExport(params, { signal, ...axiosOptions });

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getImportExportCSVExport>>,
    TError,
    TData
  > & { queryKey: QueryKey };
};

export type GetImportExportCSVExportQueryResult = NonNullable<
  Awaited<ReturnType<typeof getImportExportCSVExport>>
>;
export type GetImportExportCSVExportQueryError = AxiosError<unknown>;

/**
 * @summary Export collection
 */

export function useGetImportExportCSVExport<
  TData = Awaited<ReturnType<typeof getImportExportCSVExport>>,
  TError = AxiosError<unknown>,
>(
  params?: GetImportExportCSVExportParams,
  options?: {
    query?: UseQueryOptions<
      Awaited<ReturnType<typeof getImportExportCSVExport>>,
      TError,
      TData
    >;
    axios?: AxiosRequestConfig;
  },
): UseQueryResult<TData, TError> & { queryKey: QueryKey } {
  const queryOptions = getGetImportExportCSVExportQueryOptions(params, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: QueryKey;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}
