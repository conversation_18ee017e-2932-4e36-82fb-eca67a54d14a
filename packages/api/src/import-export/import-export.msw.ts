/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import { faker } from '@faker-js/faker';

import { HttpResponse, delay, http } from 'msw';

import type { GetImportExportServices200 } from '.././model';

export const getGetImportExportServicesResponseMock = (
  overrideResponse: Partial<GetImportExportServices200> = {},
): GetImportExportServices200 => ({
  services: faker.helpers.arrayElement([
    Array.from(
      { length: faker.number.int({ min: 1, max: 10 }) },
      (_, i) => i + 1,
    ).map(() => ({
      type: faker.helpers.arrayElement([faker.string.alpha(20), undefined]),
      import: faker.helpers.arrayElement([
        {
          formats: faker.helpers.arrayElement([
            Array.from(
              { length: faker.number.int({ min: 1, max: 10 }) },
              (_, i) => i + 1,
            ).map(() => ({
              type: faker.helpers.arrayElement([
                faker.string.alpha(20),
                undefined,
              ]),
              instructions: faker.helpers.arrayElement([
                Array.from(
                  { length: faker.number.int({ min: 1, max: 10 }) },
                  (_, i) => i + 1,
                ).map(() => faker.string.alpha(20)),
                undefined,
              ]),
            })),
            undefined,
          ]),
          available: faker.helpers.arrayElement([
            faker.datatype.boolean(),
            undefined,
          ]),
          games: faker.helpers.arrayElement([
            Array.from(
              { length: faker.number.int({ min: 1, max: 10 }) },
              (_, i) => i + 1,
            ).map(() => faker.string.alpha(20)),
            undefined,
          ]),
        },
        undefined,
      ]),
      export: faker.helpers.arrayElement([
        {
          formats: faker.helpers.arrayElement([
            Array.from(
              { length: faker.number.int({ min: 1, max: 10 }) },
              (_, i) => i + 1,
            ).map(() => ({
              type: faker.helpers.arrayElement([
                faker.string.alpha(20),
                undefined,
              ]),
              instructions: faker.helpers.arrayElement([
                Array.from(
                  { length: faker.number.int({ min: 1, max: 10 }) },
                  (_, i) => i + 1,
                ).map(() => faker.string.alpha(20)),
                undefined,
              ]),
            })),
            undefined,
          ]),
          available: faker.helpers.arrayElement([
            faker.datatype.boolean(),
            undefined,
          ]),
          games: faker.helpers.arrayElement([
            Array.from(
              { length: faker.number.int({ min: 1, max: 10 }) },
              (_, i) => i + 1,
            ).map(() => faker.string.alpha(20)),
            undefined,
          ]),
        },
        undefined,
      ]),
      export_staged: faker.helpers.arrayElement([
        {
          formats: faker.helpers.arrayElement([
            Array.from(
              { length: faker.number.int({ min: 1, max: 10 }) },
              (_, i) => i + 1,
            ).map(() => ({
              type: faker.helpers.arrayElement([
                faker.string.alpha(20),
                undefined,
              ]),
              instructions: faker.helpers.arrayElement([
                Array.from(
                  { length: faker.number.int({ min: 1, max: 10 }) },
                  (_, i) => i + 1,
                ).map(() => faker.string.alpha(20)),
                undefined,
              ]),
            })),
            undefined,
          ]),
          available: faker.helpers.arrayElement([
            faker.datatype.boolean(),
            undefined,
          ]),
          games: faker.helpers.arrayElement([
            Array.from(
              { length: faker.number.int({ min: 1, max: 10 }) },
              (_, i) => i + 1,
            ).map(() => faker.string.alpha(20)),
            undefined,
          ]),
        },
        undefined,
      ]),
    })),
    undefined,
  ]),
  ...overrideResponse,
});

export const getGetImportExportServicesMockHandler = (
  overrideResponse?:
    | GetImportExportServices200
    | ((
        info: Parameters<Parameters<typeof http.get>[1]>[0],
      ) => Promise<GetImportExportServices200> | GetImportExportServices200),
) => {
  return http.get('*/import_export/manifest', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getGetImportExportServicesResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getGetImportExportCSVExportMockHandler = (
  overrideResponse?:
    | void
    | ((
        info: Parameters<Parameters<typeof http.get>[1]>[0],
      ) => Promise<void> | void),
) => {
  return http.get('*/import_export/export', async (info) => {
    await delay(1000);
    if (typeof overrideResponse === 'function') {
      await overrideResponse(info);
    }
    return new HttpResponse(null, { status: 202 });
  });
};
export const getImportExportMock = () => [
  getGetImportExportServicesMockHandler(),
  getGetImportExportCSVExportMockHandler(),
];
