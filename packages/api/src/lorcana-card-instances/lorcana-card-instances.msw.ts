/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import { faker } from '@faker-js/faker';

import { HttpResponse, delay, http } from 'msw';

import type {
  AddLorcanaCardInstance201,
  PatchLorcanaCardUsersLorcanaCard200,
  PostLorcanaCardInstancesScannedImage201,
  UpdateLorcanaCardInstance200,
} from '.././model';

export const getAddLorcanaCardInstanceResponseMock = (
  overrideResponse: Partial<AddLorcanaCardInstance201> = {},
): AddLorcanaCardInstance201 => ({
  lorcana_card_instances: {},
  ...overrideResponse,
});

export const getUpdateLorcanaCardInstanceResponseMock = (
  overrideResponse: Partial<UpdateLorcanaCardInstance200> = {},
): UpdateLorcanaCardInstance200 => ({
  card_instances: Array.from(
    { length: faker.number.int({ min: 1, max: 10 }) },
    (_, i) => i + 1,
  ).map(() => ({})),
  cards: {},
  ...overrideResponse,
});

export const getPatchLorcanaCardUsersLorcanaCardResponseMock = (
  overrideResponse: Partial<PatchLorcanaCardUsersLorcanaCard200> = {},
): PatchLorcanaCardUsersLorcanaCard200 => ({
  card_instances: Array.from(
    { length: faker.number.int({ min: 1, max: 10 }) },
    (_, i) => i + 1,
  ).map(() => ({})),
  ...overrideResponse,
});

export const getPostLorcanaCardInstancesScannedImageResponseMock = (
  overrideResponse: Partial<PostLorcanaCardInstancesScannedImage201> = {},
): PostLorcanaCardInstancesScannedImage201 => ({
  scanned_image: faker.string.alpha(20),
  ...overrideResponse,
});

export const getAddLorcanaCardInstanceMockHandler = (
  overrideResponse?:
    | AddLorcanaCardInstance201
    | ((
        info: Parameters<Parameters<typeof http.post>[1]>[0],
      ) => Promise<AddLorcanaCardInstance201> | AddLorcanaCardInstance201),
) => {
  return http.post('*/lorcana_card_instances/add', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getAddLorcanaCardInstanceResponseMock(),
      ),
      { status: 201, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getUpdateLorcanaCardInstanceMockHandler = (
  overrideResponse?:
    | UpdateLorcanaCardInstance200
    | ((
        info: Parameters<Parameters<typeof http.patch>[1]>[0],
      ) =>
        | Promise<UpdateLorcanaCardInstance200>
        | UpdateLorcanaCardInstance200),
) => {
  return http.patch('*/lorcana_card_instances', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getUpdateLorcanaCardInstanceResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getDeleteLorcanaCardInstanceMockHandler = (
  overrideResponse?:
    | void
    | ((
        info: Parameters<Parameters<typeof http.delete>[1]>[0],
      ) => Promise<void> | void),
) => {
  return http.delete('*/lorcana_card_instances', async (info) => {
    await delay(1000);
    if (typeof overrideResponse === 'function') {
      await overrideResponse(info);
    }
    return new HttpResponse(null, { status: 200 });
  });
};

export const getPatchLorcanaCardUsersLorcanaCardMockHandler = (
  overrideResponse?:
    | PatchLorcanaCardUsersLorcanaCard200
    | ((
        info: Parameters<Parameters<typeof http.patch>[1]>[0],
      ) =>
        | Promise<PatchLorcanaCardUsersLorcanaCard200>
        | PatchLorcanaCardUsersLorcanaCard200),
) => {
  return http.patch('*/lorcana_card_instances/lorcana_card', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getPatchLorcanaCardUsersLorcanaCardResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getPostLorcanaCardInstancesScannedImageMockHandler = (
  overrideResponse?:
    | PostLorcanaCardInstancesScannedImage201
    | ((
        info: Parameters<Parameters<typeof http.post>[1]>[0],
      ) =>
        | Promise<PostLorcanaCardInstancesScannedImage201>
        | PostLorcanaCardInstancesScannedImage201),
) => {
  return http.post(
    '*/lorcana_card_instances/:uuid/scanned_image',
    async (info) => {
      await delay(1000);

      return new HttpResponse(
        JSON.stringify(
          overrideResponse !== undefined
            ? typeof overrideResponse === 'function'
              ? await overrideResponse(info)
              : overrideResponse
            : getPostLorcanaCardInstancesScannedImageResponseMock(),
        ),
        { status: 201, headers: { 'Content-Type': 'application/json' } },
      );
    },
  );
};
export const getLorcanaCardInstancesMock = () => [
  getAddLorcanaCardInstanceMockHandler(),
  getUpdateLorcanaCardInstanceMockHandler(),
  getDeleteLorcanaCardInstanceMockHandler(),
  getPatchLorcanaCardUsersLorcanaCardMockHandler(),
  getPostLorcanaCardInstancesScannedImageMockHandler(),
];
