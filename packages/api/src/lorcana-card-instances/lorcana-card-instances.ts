/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import { useMutation } from '@tanstack/react-query';
import type {
  MutationFunction,
  UseMutationOptions,
  UseMutationResult,
} from '@tanstack/react-query';

import axios from 'axios';
import type { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios';

import type {
  AddLorcanaCardInstance201,
  AddLorcanaCardInstanceParams,
  DeleteLorcanaCardInstanceParams,
  PatchLorcanaCardUsersLorcanaCard200,
  PatchLorcanaCardUsersLorcanaCardParams,
  PostLorcanaCardInstancesScannedImage201,
  PostLorcanaCardInstancesScannedImageParams,
  UpdateLorcanaCardInstance200,
  UpdateLorcanaCardInstanceParams,
} from '.././model';

type AwaitedInput<T> = PromiseLike<T> | T;

type Awaited<O> = O extends AwaitedInput<infer T> ? T : never;

/**
 * Add a lorcana card instance
 * @summary Add a lorcana card instance
 */
export const addLorcanaCardInstance = (
  params: AddLorcanaCardInstanceParams,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<AddLorcanaCardInstance201>> => {
  return axios.post(`/lorcana_card_instances/add`, undefined, {
    ...options,
    params: { ...params, ...options?.params },
  });
};

export const getAddLorcanaCardInstanceMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof addLorcanaCardInstance>>,
    TError,
    { params: AddLorcanaCardInstanceParams },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof addLorcanaCardInstance>>,
  TError,
  { params: AddLorcanaCardInstanceParams },
  TContext
> => {
  const mutationKey = ['addLorcanaCardInstance'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof addLorcanaCardInstance>>,
    { params: AddLorcanaCardInstanceParams }
  > = (props) => {
    const { params } = props ?? {};

    return addLorcanaCardInstance(params, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type AddLorcanaCardInstanceMutationResult = NonNullable<
  Awaited<ReturnType<typeof addLorcanaCardInstance>>
>;

export type AddLorcanaCardInstanceMutationError = AxiosError<unknown>;

/**
 * @summary Add a lorcana card instance
 */
export const useAddLorcanaCardInstance = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof addLorcanaCardInstance>>,
    TError,
    { params: AddLorcanaCardInstanceParams },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof addLorcanaCardInstance>>,
  TError,
  { params: AddLorcanaCardInstanceParams },
  TContext
> => {
  const mutationOptions = getAddLorcanaCardInstanceMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Updates a lorcana card instance's attributes
 * @summary Update a lorcana card instance
 */
export const updateLorcanaCardInstance = (
  params: UpdateLorcanaCardInstanceParams,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<UpdateLorcanaCardInstance200>> => {
  return axios.patch(`/lorcana_card_instances`, undefined, {
    ...options,
    params: { ...params, ...options?.params },
  });
};

export const getUpdateLorcanaCardInstanceMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof updateLorcanaCardInstance>>,
    TError,
    { params: UpdateLorcanaCardInstanceParams },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof updateLorcanaCardInstance>>,
  TError,
  { params: UpdateLorcanaCardInstanceParams },
  TContext
> => {
  const mutationKey = ['updateLorcanaCardInstance'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof updateLorcanaCardInstance>>,
    { params: UpdateLorcanaCardInstanceParams }
  > = (props) => {
    const { params } = props ?? {};

    return updateLorcanaCardInstance(params, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type UpdateLorcanaCardInstanceMutationResult = NonNullable<
  Awaited<ReturnType<typeof updateLorcanaCardInstance>>
>;

export type UpdateLorcanaCardInstanceMutationError = AxiosError<unknown>;

/**
 * @summary Update a lorcana card instance
 */
export const useUpdateLorcanaCardInstance = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof updateLorcanaCardInstance>>,
    TError,
    { params: UpdateLorcanaCardInstanceParams },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof updateLorcanaCardInstance>>,
  TError,
  { params: UpdateLorcanaCardInstanceParams },
  TContext
> => {
  const mutationOptions = getUpdateLorcanaCardInstanceMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Bulk deletes lorcana card instances for the currently authenticated user. On request success all the lorcana cards are deleted from the user. On failure the request is rolled back
 * @summary Delete Lorcana Card Instances
 */
export const deleteLorcanaCardInstance = (
  params: DeleteLorcanaCardInstanceParams,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<void>> => {
  return axios.delete(`/lorcana_card_instances`, {
    ...options,
    params: { ...params, ...options?.params },
  });
};

export const getDeleteLorcanaCardInstanceMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteLorcanaCardInstance>>,
    TError,
    { params: DeleteLorcanaCardInstanceParams },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof deleteLorcanaCardInstance>>,
  TError,
  { params: DeleteLorcanaCardInstanceParams },
  TContext
> => {
  const mutationKey = ['deleteLorcanaCardInstance'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof deleteLorcanaCardInstance>>,
    { params: DeleteLorcanaCardInstanceParams }
  > = (props) => {
    const { params } = props ?? {};

    return deleteLorcanaCardInstance(params, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type DeleteLorcanaCardInstanceMutationResult = NonNullable<
  Awaited<ReturnType<typeof deleteLorcanaCardInstance>>
>;

export type DeleteLorcanaCardInstanceMutationError = AxiosError<unknown>;

/**
 * @summary Delete Lorcana Card Instances
 */
export const useDeleteLorcanaCardInstance = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteLorcanaCardInstance>>,
    TError,
    { params: DeleteLorcanaCardInstanceParams },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof deleteLorcanaCardInstance>>,
  TError,
  { params: DeleteLorcanaCardInstanceParams },
  TContext
> => {
  const mutationOptions = getDeleteLorcanaCardInstanceMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Update lorcana_card for lorcana_card_instances
 * @summary Update lorcana_card
 */
export const patchLorcanaCardUsersLorcanaCard = (
  params: PatchLorcanaCardUsersLorcanaCardParams,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<PatchLorcanaCardUsersLorcanaCard200>> => {
  return axios.patch(`/lorcana_card_instances/lorcana_card`, undefined, {
    ...options,
    params: { ...params, ...options?.params },
  });
};

export const getPatchLorcanaCardUsersLorcanaCardMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchLorcanaCardUsersLorcanaCard>>,
    TError,
    { params: PatchLorcanaCardUsersLorcanaCardParams },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof patchLorcanaCardUsersLorcanaCard>>,
  TError,
  { params: PatchLorcanaCardUsersLorcanaCardParams },
  TContext
> => {
  const mutationKey = ['patchLorcanaCardUsersLorcanaCard'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof patchLorcanaCardUsersLorcanaCard>>,
    { params: PatchLorcanaCardUsersLorcanaCardParams }
  > = (props) => {
    const { params } = props ?? {};

    return patchLorcanaCardUsersLorcanaCard(params, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type PatchLorcanaCardUsersLorcanaCardMutationResult = NonNullable<
  Awaited<ReturnType<typeof patchLorcanaCardUsersLorcanaCard>>
>;

export type PatchLorcanaCardUsersLorcanaCardMutationError = AxiosError<unknown>;

/**
 * @summary Update lorcana_card
 */
export const usePatchLorcanaCardUsersLorcanaCard = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchLorcanaCardUsersLorcanaCard>>,
    TError,
    { params: PatchLorcanaCardUsersLorcanaCardParams },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof patchLorcanaCardUsersLorcanaCard>>,
  TError,
  { params: PatchLorcanaCardUsersLorcanaCardParams },
  TContext
> => {
  const mutationOptions =
    getPatchLorcanaCardUsersLorcanaCardMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Uploads a scanned image and attaches it to an existing lorcana card instance
 * @summary Upload a scanned image
 */
export const postLorcanaCardInstancesScannedImage = (
  uuid: number,
  params: PostLorcanaCardInstancesScannedImageParams,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<PostLorcanaCardInstancesScannedImage201>> => {
  return axios.post(
    `/lorcana_card_instances/${uuid}/scanned_image`,
    undefined,
    {
      ...options,
      params: { ...params, ...options?.params },
    },
  );
};

export const getPostLorcanaCardInstancesScannedImageMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postLorcanaCardInstancesScannedImage>>,
    TError,
    { uuid: number; params: PostLorcanaCardInstancesScannedImageParams },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof postLorcanaCardInstancesScannedImage>>,
  TError,
  { uuid: number; params: PostLorcanaCardInstancesScannedImageParams },
  TContext
> => {
  const mutationKey = ['postLorcanaCardInstancesScannedImage'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof postLorcanaCardInstancesScannedImage>>,
    { uuid: number; params: PostLorcanaCardInstancesScannedImageParams }
  > = (props) => {
    const { uuid, params } = props ?? {};

    return postLorcanaCardInstancesScannedImage(uuid, params, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type PostLorcanaCardInstancesScannedImageMutationResult = NonNullable<
  Awaited<ReturnType<typeof postLorcanaCardInstancesScannedImage>>
>;

export type PostLorcanaCardInstancesScannedImageMutationError =
  AxiosError<unknown>;

/**
 * @summary Upload a scanned image
 */
export const usePostLorcanaCardInstancesScannedImage = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postLorcanaCardInstancesScannedImage>>,
    TError,
    { uuid: number; params: PostLorcanaCardInstancesScannedImageParams },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof postLorcanaCardInstancesScannedImage>>,
  TError,
  { uuid: number; params: PostLorcanaCardInstancesScannedImageParams },
  TContext
> => {
  const mutationOptions =
    getPostLorcanaCardInstancesScannedImageMutationOptions(options);

  return useMutation(mutationOptions);
};
