/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import { useMutation } from '@tanstack/react-query';
import type {
  MutationFunction,
  UseMutationOptions,
  UseMutationResult,
} from '@tanstack/react-query';

import axios from 'axios';
import type { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios';

import type { PostCouponsBody } from '.././model';

type AwaitedInput<T> = PromiseLike<T> | T;

type Awaited<O> = O extends AwaitedInput<infer T> ? T : never;

/**
 * @summary Batch Create Coupons
 */
export const postCoupons = (
  postCouponsBody: PostCouponsBody,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<string[]>> => {
  return axios.post(`/coupons`, postCouponsBody, options);
};

export const getPostCouponsMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postCoupons>>,
    TError,
    { data: PostCouponsBody },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof postCoupons>>,
  TError,
  { data: PostCouponsBody },
  TContext
> => {
  const mutationKey = ['postCoupons'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof postCoupons>>,
    { data: PostCouponsBody }
  > = (props) => {
    const { data } = props ?? {};

    return postCoupons(data, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type PostCouponsMutationResult = NonNullable<
  Awaited<ReturnType<typeof postCoupons>>
>;
export type PostCouponsMutationBody = PostCouponsBody;
export type PostCouponsMutationError = AxiosError<unknown>;

/**
 * @summary Batch Create Coupons
 */
export const usePostCoupons = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postCoupons>>,
    TError,
    { data: PostCouponsBody },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof postCoupons>>,
  TError,
  { data: PostCouponsBody },
  TContext
> => {
  const mutationOptions = getPostCouponsMutationOptions(options);

  return useMutation(mutationOptions);
};
