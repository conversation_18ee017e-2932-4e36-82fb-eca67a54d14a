/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import { faker } from '@faker-js/faker';

import { HttpResponse, delay, http } from 'msw';

export const getPostCouponsResponseMock = (): string[] =>
  Array.from({ length: faker.number.int({ min: 1, max: 10 }) }, () =>
    faker.word.sample(),
  );

export const getPostCouponsMockHandler = (
  overrideResponse?:
    | string[]
    | ((
        info: Parameters<Parameters<typeof http.post>[1]>[0],
      ) => Promise<string[]> | string[]),
) => {
  return http.post('*/coupons', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getPostCouponsResponseMock(),
      ),
      { status: 201, headers: { 'Content-Type': 'application/json' } },
    );
  });
};
export const getCouponMock = () => [getPostCouponsMockHandler()];
