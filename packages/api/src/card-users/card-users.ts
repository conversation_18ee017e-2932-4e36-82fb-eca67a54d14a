/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import { useMutation, useQuery } from '@tanstack/react-query';
import type {
  MutationFunction,
  QueryFunction,
  QueryKey,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult,
} from '@tanstack/react-query';

import axios from 'axios';
import type { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios';

import type {
  AddCardsForUser201,
  AddCardsForUserBody,
  AddCardsForUserParams,
  CommitStagedCards200,
  CommitStagedCards500,
  CreateCardsForUser201,
  CreateCardsForUserBody,
  CreateCardsForUserParams,
  DeleteCardBody,
  ExportCollectionParams,
  GetCardUserResources200,
  GetCardUserResourcesBody,
  ImportToCollectionParams,
  PatchCardUsers201,
  PatchCardUsersCard200,
  PatchCardUsersCardParams,
  PatchCardUsersParams,
  PostCardUsersScannedImage201,
  UpdateFoilOnCard200,
  UpdateFoilOnCardBody,
  UpdateLanguageOnCard200,
  UpdateLanguageOnCardBody,
  UpdateQualityOnCard200,
  UpdateQualityOnCardBody,
} from '.././model';

type AwaitedInput<T> = PromiseLike<T> | T;

type Awaited<O> = O extends AwaitedInput<infer T> ? T : never;

/**
 * Bulk creates cards for the currently authenticated user. On request
success all the cards are created for the user. On failure the request is rollbacked. If no input_source param is passed, a legacy input_source with type 'unknown' is created
 * @summary Create Cards For User
 */
export const createCardsForUser = (
  createCardsForUserBody: CreateCardsForUserBody,
  params: CreateCardsForUserParams,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<CreateCardsForUser201>> => {
  return axios.post(`/card_users`, createCardsForUserBody, {
    ...options,
    params: { ...params, ...options?.params },
  });
};

export const getCreateCardsForUserMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof createCardsForUser>>,
    TError,
    { data: CreateCardsForUserBody; params: CreateCardsForUserParams },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof createCardsForUser>>,
  TError,
  { data: CreateCardsForUserBody; params: CreateCardsForUserParams },
  TContext
> => {
  const mutationKey = ['createCardsForUser'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof createCardsForUser>>,
    { data: CreateCardsForUserBody; params: CreateCardsForUserParams }
  > = (props) => {
    const { data, params } = props ?? {};

    return createCardsForUser(data, params, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type CreateCardsForUserMutationResult = NonNullable<
  Awaited<ReturnType<typeof createCardsForUser>>
>;
export type CreateCardsForUserMutationBody = CreateCardsForUserBody;
export type CreateCardsForUserMutationError = AxiosError<unknown>;

/**
 * @summary Create Cards For User
 */
export const useCreateCardsForUser = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof createCardsForUser>>,
    TError,
    { data: CreateCardsForUserBody; params: CreateCardsForUserParams },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof createCardsForUser>>,
  TError,
  { data: CreateCardsForUserBody; params: CreateCardsForUserParams },
  TContext
> => {
  const mutationOptions = getCreateCardsForUserMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Bulk updates card users
 * @summary Update Card Users
 */
export const patchCardUsers = (
  params: PatchCardUsersParams,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<PatchCardUsers201>> => {
  return axios.patch(`/card_users`, undefined, {
    ...options,
    params: { ...params, ...options?.params },
  });
};

export const getPatchCardUsersMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchCardUsers>>,
    TError,
    { params: PatchCardUsersParams },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof patchCardUsers>>,
  TError,
  { params: PatchCardUsersParams },
  TContext
> => {
  const mutationKey = ['patchCardUsers'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof patchCardUsers>>,
    { params: PatchCardUsersParams }
  > = (props) => {
    const { params } = props ?? {};

    return patchCardUsers(params, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type PatchCardUsersMutationResult = NonNullable<
  Awaited<ReturnType<typeof patchCardUsers>>
>;

export type PatchCardUsersMutationError = AxiosError<unknown>;

/**
 * @summary Update Card Users
 */
export const usePatchCardUsers = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchCardUsers>>,
    TError,
    { params: PatchCardUsersParams },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof patchCardUsers>>,
  TError,
  { params: PatchCardUsersParams },
  TContext
> => {
  const mutationOptions = getPatchCardUsersMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Bulk deletes card for the currently authenticated user. On request
success all the cards are deleted from the user. On failure the request is rollbacked.
+ card_ids (object) - array of ids of card users to be deleted
 * @summary Delete Card
 */
export const deleteCard = (
  deleteCardBody: DeleteCardBody,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<void>> => {
  return axios.delete(`/card_users`, { data: deleteCardBody, ...options });
};

export const getDeleteCardMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteCard>>,
    TError,
    { data: DeleteCardBody },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof deleteCard>>,
  TError,
  { data: DeleteCardBody },
  TContext
> => {
  const mutationKey = ['deleteCard'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof deleteCard>>,
    { data: DeleteCardBody }
  > = (props) => {
    const { data } = props ?? {};

    return deleteCard(data, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type DeleteCardMutationResult = NonNullable<
  Awaited<ReturnType<typeof deleteCard>>
>;
export type DeleteCardMutationBody = DeleteCardBody;
export type DeleteCardMutationError = AxiosError<unknown>;

/**
 * @summary Delete Card
 */
export const useDeleteCard = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteCard>>,
    TError,
    { data: DeleteCardBody },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof deleteCard>>,
  TError,
  { data: DeleteCardBody },
  TContext
> => {
  const mutationOptions = getDeleteCardMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Bulk creates cards for the currently authenticated user. On request
success all the cards are created for the user. On failure the request is rollbacked. If no input_source param is passed, a legacy input_source with type 'unknown' is created
 * @summary Create Cards For User
 */
export const addCardsForUser = (
  addCardsForUserBody: AddCardsForUserBody,
  params: AddCardsForUserParams,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<AddCardsForUser201>> => {
  return axios.post(`/card_users/add`, addCardsForUserBody, {
    ...options,
    params: { ...params, ...options?.params },
  });
};

export const getAddCardsForUserMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof addCardsForUser>>,
    TError,
    { data: AddCardsForUserBody; params: AddCardsForUserParams },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof addCardsForUser>>,
  TError,
  { data: AddCardsForUserBody; params: AddCardsForUserParams },
  TContext
> => {
  const mutationKey = ['addCardsForUser'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof addCardsForUser>>,
    { data: AddCardsForUserBody; params: AddCardsForUserParams }
  > = (props) => {
    const { data, params } = props ?? {};

    return addCardsForUser(data, params, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type AddCardsForUserMutationResult = NonNullable<
  Awaited<ReturnType<typeof addCardsForUser>>
>;
export type AddCardsForUserMutationBody = AddCardsForUserBody;
export type AddCardsForUserMutationError = AxiosError<unknown>;

/**
 * @summary Create Cards For User
 */
export const useAddCardsForUser = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof addCardsForUser>>,
    TError,
    { data: AddCardsForUserBody; params: AddCardsForUserParams },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof addCardsForUser>>,
  TError,
  { data: AddCardsForUserBody; params: AddCardsForUserParams },
  TContext
> => {
  const mutationOptions = getAddCardsForUserMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Queues a CSV export job. A link to the resulting CSV is emailed to the user
 * @summary Export Collection
 */
export const exportCollection = (
  params?: ExportCollectionParams,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<void>> => {
  return axios.get(`/card_users/export`, {
    ...options,
    params: { ...params, ...options?.params },
  });
};

export const getExportCollectionQueryKey = (
  params?: ExportCollectionParams,
) => {
  return [`/card_users/export`, ...(params ? [params] : [])] as const;
};

export const getExportCollectionQueryOptions = <
  TData = Awaited<ReturnType<typeof exportCollection>>,
  TError = AxiosError<unknown>,
>(
  params?: ExportCollectionParams,
  options?: {
    query?: UseQueryOptions<
      Awaited<ReturnType<typeof exportCollection>>,
      TError,
      TData
    >;
    axios?: AxiosRequestConfig;
  },
) => {
  const { query: queryOptions, axios: axiosOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ?? getExportCollectionQueryKey(params);

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof exportCollection>>
  > = ({ signal }) => exportCollection(params, { signal, ...axiosOptions });

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof exportCollection>>,
    TError,
    TData
  > & { queryKey: QueryKey };
};

export type ExportCollectionQueryResult = NonNullable<
  Awaited<ReturnType<typeof exportCollection>>
>;
export type ExportCollectionQueryError = AxiosError<unknown>;

/**
 * @summary Export Collection
 */

export function useExportCollection<
  TData = Awaited<ReturnType<typeof exportCollection>>,
  TError = AxiosError<unknown>,
>(
  params?: ExportCollectionParams,
  options?: {
    query?: UseQueryOptions<
      Awaited<ReturnType<typeof exportCollection>>,
      TError,
      TData
    >;
    axios?: AxiosRequestConfig;
  },
): UseQueryResult<TData, TError> & { queryKey: QueryKey } {
  const queryOptions = getExportCollectionQueryOptions(params, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: QueryKey;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Upload the CSV to the server. A job is queued to asynchronously add the cards to the users collection. A resulting email will be sent to the user on completion.
 * @summary Import to Collection
 */
export const importToCollection = (
  params: ImportToCollectionParams,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<void>> => {
  return axios.post(`/card_users/import`, undefined, {
    ...options,
    params: { ...params, ...options?.params },
  });
};

export const getImportToCollectionMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof importToCollection>>,
    TError,
    { params: ImportToCollectionParams },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof importToCollection>>,
  TError,
  { params: ImportToCollectionParams },
  TContext
> => {
  const mutationKey = ['importToCollection'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof importToCollection>>,
    { params: ImportToCollectionParams }
  > = (props) => {
    const { params } = props ?? {};

    return importToCollection(params, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type ImportToCollectionMutationResult = NonNullable<
  Awaited<ReturnType<typeof importToCollection>>
>;

export type ImportToCollectionMutationError = AxiosError<unknown>;

/**
 * @summary Import to Collection
 */
export const useImportToCollection = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof importToCollection>>,
    TError,
    { params: ImportToCollectionParams },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof importToCollection>>,
  TError,
  { params: ImportToCollectionParams },
  TContext
> => {
  const mutationOptions = getImportToCollectionMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Updates the foil attribute on the card
 * @summary Update Foil on Card
 */
export const updateFoilOnCard = (
  updateFoilOnCardBody: UpdateFoilOnCardBody,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<UpdateFoilOnCard200>> => {
  return axios.patch(`/card_users/foil`, updateFoilOnCardBody, options);
};

export const getUpdateFoilOnCardMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof updateFoilOnCard>>,
    TError,
    { data: UpdateFoilOnCardBody },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof updateFoilOnCard>>,
  TError,
  { data: UpdateFoilOnCardBody },
  TContext
> => {
  const mutationKey = ['updateFoilOnCard'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof updateFoilOnCard>>,
    { data: UpdateFoilOnCardBody }
  > = (props) => {
    const { data } = props ?? {};

    return updateFoilOnCard(data, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type UpdateFoilOnCardMutationResult = NonNullable<
  Awaited<ReturnType<typeof updateFoilOnCard>>
>;
export type UpdateFoilOnCardMutationBody = UpdateFoilOnCardBody;
export type UpdateFoilOnCardMutationError = AxiosError<unknown>;

/**
 * @summary Update Foil on Card
 */
export const useUpdateFoilOnCard = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof updateFoilOnCard>>,
    TError,
    { data: UpdateFoilOnCardBody },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof updateFoilOnCard>>,
  TError,
  { data: UpdateFoilOnCardBody },
  TContext
> => {
  const mutationOptions = getUpdateFoilOnCardMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Updates the quality attribute on the card, the default quality is an empty string.
 * @summary Update Quality on Card
 */
export const updateQualityOnCard = (
  updateQualityOnCardBody: UpdateQualityOnCardBody,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<UpdateQualityOnCard200>> => {
  return axios.patch(`/card_users/quality`, updateQualityOnCardBody, options);
};

export const getUpdateQualityOnCardMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof updateQualityOnCard>>,
    TError,
    { data: UpdateQualityOnCardBody },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof updateQualityOnCard>>,
  TError,
  { data: UpdateQualityOnCardBody },
  TContext
> => {
  const mutationKey = ['updateQualityOnCard'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof updateQualityOnCard>>,
    { data: UpdateQualityOnCardBody }
  > = (props) => {
    const { data } = props ?? {};

    return updateQualityOnCard(data, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type UpdateQualityOnCardMutationResult = NonNullable<
  Awaited<ReturnType<typeof updateQualityOnCard>>
>;
export type UpdateQualityOnCardMutationBody = UpdateQualityOnCardBody;
export type UpdateQualityOnCardMutationError = AxiosError<unknown>;

/**
 * @summary Update Quality on Card
 */
export const useUpdateQualityOnCard = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof updateQualityOnCard>>,
    TError,
    { data: UpdateQualityOnCardBody },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof updateQualityOnCard>>,
  TError,
  { data: UpdateQualityOnCardBody },
  TContext
> => {
  const mutationOptions = getUpdateQualityOnCardMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Updates the language attribute on the card
 * @summary Update Language On Cards
 */
export const updateLanguageOnCard = (
  updateLanguageOnCardBody: UpdateLanguageOnCardBody,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<UpdateLanguageOnCard200>> => {
  return axios.patch(`/card_users/language`, updateLanguageOnCardBody, options);
};

export const getUpdateLanguageOnCardMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof updateLanguageOnCard>>,
    TError,
    { data: UpdateLanguageOnCardBody },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof updateLanguageOnCard>>,
  TError,
  { data: UpdateLanguageOnCardBody },
  TContext
> => {
  const mutationKey = ['updateLanguageOnCard'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof updateLanguageOnCard>>,
    { data: UpdateLanguageOnCardBody }
  > = (props) => {
    const { data } = props ?? {};

    return updateLanguageOnCard(data, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type UpdateLanguageOnCardMutationResult = NonNullable<
  Awaited<ReturnType<typeof updateLanguageOnCard>>
>;
export type UpdateLanguageOnCardMutationBody = UpdateLanguageOnCardBody;
export type UpdateLanguageOnCardMutationError = AxiosError<unknown>;

/**
 * @summary Update Language On Cards
 */
export const useUpdateLanguageOnCard = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof updateLanguageOnCard>>,
    TError,
    { data: UpdateLanguageOnCardBody },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof updateLanguageOnCard>>,
  TError,
  { data: UpdateLanguageOnCardBody },
  TContext
> => {
  const mutationOptions = getUpdateLanguageOnCardMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Commit the cards that have been staged on the build page, and finalize the valus. Returns the ids of all the cards added
 * @summary Commit Staged Cards
 */
export const commitStagedCards = (
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<CommitStagedCards200>> => {
  return axios.post(`/card_users/commit`, undefined, options);
};

export const getCommitStagedCardsMutationOptions = <
  TError = AxiosError<CommitStagedCards500>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof commitStagedCards>>,
    TError,
    void,
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof commitStagedCards>>,
  TError,
  void,
  TContext
> => {
  const mutationKey = ['commitStagedCards'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof commitStagedCards>>,
    void
  > = () => {
    return commitStagedCards(axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type CommitStagedCardsMutationResult = NonNullable<
  Awaited<ReturnType<typeof commitStagedCards>>
>;

export type CommitStagedCardsMutationError = AxiosError<CommitStagedCards500>;

/**
 * @summary Commit Staged Cards
 */
export const useCommitStagedCards = <
  TError = AxiosError<CommitStagedCards500>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof commitStagedCards>>,
    TError,
    void,
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof commitStagedCards>>,
  TError,
  void,
  TContext
> => {
  const mutationOptions = getCommitStagedCardsMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Reset all the cards in the staged area. Does a hard delete of all staged cards
 * @summary Reset Staged Cards
 */
export const resetStagedCards = (
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<void>> => {
  return axios.delete(`/card_users/reset`, options);
};

export const getResetStagedCardsMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof resetStagedCards>>,
    TError,
    void,
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof resetStagedCards>>,
  TError,
  void,
  TContext
> => {
  const mutationKey = ['resetStagedCards'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof resetStagedCards>>,
    void
  > = () => {
    return resetStagedCards(axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type ResetStagedCardsMutationResult = NonNullable<
  Awaited<ReturnType<typeof resetStagedCards>>
>;

export type ResetStagedCardsMutationError = AxiosError<unknown>;

/**
 * @summary Reset Staged Cards
 */
export const useResetStagedCards = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof resetStagedCards>>,
    TError,
    void,
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof resetStagedCards>>,
  TError,
  void,
  TContext
> => {
  const mutationOptions = getResetStagedCardsMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Removes all cards in the users collection for all games. This is a soft delete
 * @summary Clear Collection
 */
export const clearCollection = (
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<void>> => {
  return axios.delete(`/card_users/clear`, options);
};

export const getClearCollectionMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof clearCollection>>,
    TError,
    void,
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof clearCollection>>,
  TError,
  void,
  TContext
> => {
  const mutationKey = ['clearCollection'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof clearCollection>>,
    void
  > = () => {
    return clearCollection(axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type ClearCollectionMutationResult = NonNullable<
  Awaited<ReturnType<typeof clearCollection>>
>;

export type ClearCollectionMutationError = AxiosError<unknown>;

/**
 * @summary Clear Collection
 */
export const useClearCollection = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof clearCollection>>,
    TError,
    void,
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof clearCollection>>,
  TError,
  void,
  TContext
> => {
  const mutationOptions = getClearCollectionMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Gets the linked resources for a card user
 * @summary Get Linked Resources
 */
export const getCardUserResources = (
  getCardUserResourcesBody: GetCardUserResourcesBody,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<GetCardUserResources200>> => {
  return axios.post(`/card_users/resources`, getCardUserResourcesBody, options);
};

export const getGetCardUserResourcesMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof getCardUserResources>>,
    TError,
    { data: GetCardUserResourcesBody },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof getCardUserResources>>,
  TError,
  { data: GetCardUserResourcesBody },
  TContext
> => {
  const mutationKey = ['getCardUserResources'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof getCardUserResources>>,
    { data: GetCardUserResourcesBody }
  > = (props) => {
    const { data } = props ?? {};

    return getCardUserResources(data, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type GetCardUserResourcesMutationResult = NonNullable<
  Awaited<ReturnType<typeof getCardUserResources>>
>;
export type GetCardUserResourcesMutationBody = GetCardUserResourcesBody;
export type GetCardUserResourcesMutationError = AxiosError<unknown>;

/**
 * @summary Get Linked Resources
 */
export const useGetCardUserResources = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof getCardUserResources>>,
    TError,
    { data: GetCardUserResourcesBody },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof getCardUserResources>>,
  TError,
  { data: GetCardUserResourcesBody },
  TContext
> => {
  const mutationOptions = getGetCardUserResourcesMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Update card for card users
 * @summary Update card
 */
export const patchCardUsersCard = (
  params: PatchCardUsersCardParams,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<PatchCardUsersCard200>> => {
  return axios.patch(`/card_users/card`, undefined, {
    ...options,
    params: { ...params, ...options?.params },
  });
};

export const getPatchCardUsersCardMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchCardUsersCard>>,
    TError,
    { params: PatchCardUsersCardParams },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof patchCardUsersCard>>,
  TError,
  { params: PatchCardUsersCardParams },
  TContext
> => {
  const mutationKey = ['patchCardUsersCard'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof patchCardUsersCard>>,
    { params: PatchCardUsersCardParams }
  > = (props) => {
    const { params } = props ?? {};

    return patchCardUsersCard(params, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type PatchCardUsersCardMutationResult = NonNullable<
  Awaited<ReturnType<typeof patchCardUsersCard>>
>;

export type PatchCardUsersCardMutationError = AxiosError<unknown>;

/**
 * @summary Update card
 */
export const usePatchCardUsersCard = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchCardUsersCard>>,
    TError,
    { params: PatchCardUsersCardParams },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof patchCardUsersCard>>,
  TError,
  { params: PatchCardUsersCardParams },
  TContext
> => {
  const mutationOptions = getPatchCardUsersCardMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Uploads a scanned image and attaches it to an existing card user
 * @summary Upload a scanned image
 */
export const postCardUsersScannedImage = (
  id: number,
  postCardUsersScannedImageBody: Blob,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<PostCardUsersScannedImage201>> => {
  return axios.post(
    `/card_users/${id}/scanned_image`,
    postCardUsersScannedImageBody,
    options,
  );
};

export const getPostCardUsersScannedImageMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postCardUsersScannedImage>>,
    TError,
    { id: number; data: Blob },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof postCardUsersScannedImage>>,
  TError,
  { id: number; data: Blob },
  TContext
> => {
  const mutationKey = ['postCardUsersScannedImage'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof postCardUsersScannedImage>>,
    { id: number; data: Blob }
  > = (props) => {
    const { id, data } = props ?? {};

    return postCardUsersScannedImage(id, data, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type PostCardUsersScannedImageMutationResult = NonNullable<
  Awaited<ReturnType<typeof postCardUsersScannedImage>>
>;
export type PostCardUsersScannedImageMutationBody = Blob;
export type PostCardUsersScannedImageMutationError = AxiosError<unknown>;

/**
 * @summary Upload a scanned image
 */
export const usePostCardUsersScannedImage = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postCardUsersScannedImage>>,
    TError,
    { id: number; data: Blob },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof postCardUsersScannedImage>>,
  TError,
  { id: number; data: Blob },
  TContext
> => {
  const mutationOptions = getPostCardUsersScannedImageMutationOptions(options);

  return useMutation(mutationOptions);
};
