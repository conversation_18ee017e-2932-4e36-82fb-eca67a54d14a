/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import { faker } from '@faker-js/faker';

import { HttpResponse, delay, http } from 'msw';

import type {
  AddCardsForUser201,
  CommitStagedCards200,
  CreateCardsForUser201,
  GetCardUserResources200,
  PatchCardUsers201,
  PatchCardUsersCard200,
  PostCardUsersScannedImage201,
} from '.././model';

export const getCreateCardsForUserResponseMock = (
  overrideResponse: Partial<CreateCardsForUser201> = {},
): CreateCardsForUser201 => ({
  card_users: Array.from(
    { length: faker.number.int({ min: 1, max: 10 }) },
    (_, i) => i + 1,
  ).map(() => ({})),
  ...overrideResponse,
});

export const getPatchCardUsersResponseMock = (
  overrideResponse: Partial<PatchCardUsers201> = {},
): PatchCardUsers201 => ({
  card_users: Array.from(
    { length: faker.number.int({ min: 1, max: 10 }) },
    (_, i) => i + 1,
  ).map(() => ({})),
  ...overrideResponse,
});

export const getAddCardsForUserResponseMock = (
  overrideResponse: Partial<AddCardsForUser201> = {},
): AddCardsForUser201 => ({
  card_users: Array.from(
    { length: faker.number.int({ min: 1, max: 10 }) },
    (_, i) => i + 1,
  ).map(() => ({})),
  ...overrideResponse,
});

export const getUpdateFoilOnCardResponseMock = (): UpdateFoilOnCard200 => ({});

export const getUpdateQualityOnCardResponseMock =
  (): UpdateQualityOnCard200 => ({});

export const getUpdateLanguageOnCardResponseMock =
  (): UpdateLanguageOnCard200 => ({});

export const getCommitStagedCardsResponseMock = (
  overrideResponse: Partial<CommitStagedCards200> = {},
): CommitStagedCards200 => ({
  card_ids: Array.from(
    { length: faker.number.int({ min: 1, max: 10 }) },
    (_, i) => i + 1,
  ).map(() => faker.number.int({ min: undefined, max: undefined })),
  ...overrideResponse,
});

export const getGetCardUserResourcesResponseMock = (
  overrideResponse: Partial<GetCardUserResources200> = {},
): GetCardUserResources200 => ({ resources: {}, ...overrideResponse });

export const getPatchCardUsersCardResponseMock = (
  overrideResponse: Partial<PatchCardUsersCard200> = {},
): PatchCardUsersCard200 => ({
  card_users: Array.from(
    { length: faker.number.int({ min: 1, max: 10 }) },
    (_, i) => i + 1,
  ).map(() => ({})),
  ...overrideResponse,
});

export const getPostCardUsersScannedImageResponseMock = (
  overrideResponse: Partial<PostCardUsersScannedImage201> = {},
): PostCardUsersScannedImage201 => ({
  scanned_image: faker.string.alpha(20),
  ...overrideResponse,
});

export const getCreateCardsForUserMockHandler = (
  overrideResponse?:
    | CreateCardsForUser201
    | ((
        info: Parameters<Parameters<typeof http.post>[1]>[0],
      ) => Promise<CreateCardsForUser201> | CreateCardsForUser201),
) => {
  return http.post('*/card_users', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getCreateCardsForUserResponseMock(),
      ),
      { status: 201, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getPatchCardUsersMockHandler = (
  overrideResponse?:
    | PatchCardUsers201
    | ((
        info: Parameters<Parameters<typeof http.patch>[1]>[0],
      ) => Promise<PatchCardUsers201> | PatchCardUsers201),
) => {
  return http.patch('*/card_users', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getPatchCardUsersResponseMock(),
      ),
      { status: 201, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getDeleteCardMockHandler = (
  overrideResponse?:
    | void
    | ((
        info: Parameters<Parameters<typeof http.delete>[1]>[0],
      ) => Promise<void> | void),
) => {
  return http.delete('*/card_users', async (info) => {
    await delay(1000);
    if (typeof overrideResponse === 'function') {
      await overrideResponse(info);
    }
    return new HttpResponse(null, { status: 200 });
  });
};

export const getAddCardsForUserMockHandler = (
  overrideResponse?:
    | AddCardsForUser201
    | ((
        info: Parameters<Parameters<typeof http.post>[1]>[0],
      ) => Promise<AddCardsForUser201> | AddCardsForUser201),
) => {
  return http.post('*/card_users/add', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getAddCardsForUserResponseMock(),
      ),
      { status: 201, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getExportCollectionMockHandler = (
  overrideResponse?:
    | void
    | ((
        info: Parameters<Parameters<typeof http.get>[1]>[0],
      ) => Promise<void> | void),
) => {
  return http.get('*/card_users/export', async (info) => {
    await delay(1000);
    if (typeof overrideResponse === 'function') {
      await overrideResponse(info);
    }
    return new HttpResponse(null, { status: 202 });
  });
};

export const getImportToCollectionMockHandler = (
  overrideResponse?:
    | void
    | ((
        info: Parameters<Parameters<typeof http.post>[1]>[0],
      ) => Promise<void> | void),
) => {
  return http.post('*/card_users/import', async (info) => {
    await delay(1000);
    if (typeof overrideResponse === 'function') {
      await overrideResponse(info);
    }
    return new HttpResponse(null, { status: 202 });
  });
};

export const getUpdateFoilOnCardMockHandler = (
  overrideResponse?:
    | UpdateFoilOnCard200
    | ((
        info: Parameters<Parameters<typeof http.patch>[1]>[0],
      ) => Promise<UpdateFoilOnCard200> | UpdateFoilOnCard200),
) => {
  return http.patch('*/card_users/foil', async (info) => {
    await delay(1000);

    return new HttpResponse(getUpdateFoilOnCardResponseMock(), {
      status: 200,
      headers: { 'Content-Type': 'text/plain' },
    });
  });
};

export const getUpdateQualityOnCardMockHandler = (
  overrideResponse?:
    | UpdateQualityOnCard200
    | ((
        info: Parameters<Parameters<typeof http.patch>[1]>[0],
      ) => Promise<UpdateQualityOnCard200> | UpdateQualityOnCard200),
) => {
  return http.patch('*/card_users/quality', async (info) => {
    await delay(1000);

    return new HttpResponse(getUpdateQualityOnCardResponseMock(), {
      status: 200,
      headers: { 'Content-Type': 'text/plain' },
    });
  });
};

export const getUpdateLanguageOnCardMockHandler = (
  overrideResponse?:
    | UpdateLanguageOnCard200
    | ((
        info: Parameters<Parameters<typeof http.patch>[1]>[0],
      ) => Promise<UpdateLanguageOnCard200> | UpdateLanguageOnCard200),
) => {
  return http.patch('*/card_users/language', async (info) => {
    await delay(1000);

    return new HttpResponse(getUpdateLanguageOnCardResponseMock(), {
      status: 200,
      headers: { 'Content-Type': 'text/plain' },
    });
  });
};

export const getCommitStagedCardsMockHandler = (
  overrideResponse?:
    | CommitStagedCards200
    | ((
        info: Parameters<Parameters<typeof http.post>[1]>[0],
      ) => Promise<CommitStagedCards200> | CommitStagedCards200),
) => {
  return http.post('*/card_users/commit', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getCommitStagedCardsResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getResetStagedCardsMockHandler = (
  overrideResponse?:
    | void
    | ((
        info: Parameters<Parameters<typeof http.delete>[1]>[0],
      ) => Promise<void> | void),
) => {
  return http.delete('*/card_users/reset', async (info) => {
    await delay(1000);
    if (typeof overrideResponse === 'function') {
      await overrideResponse(info);
    }
    return new HttpResponse(null, { status: 200 });
  });
};

export const getClearCollectionMockHandler = (
  overrideResponse?:
    | void
    | ((
        info: Parameters<Parameters<typeof http.delete>[1]>[0],
      ) => Promise<void> | void),
) => {
  return http.delete('*/card_users/clear', async (info) => {
    await delay(1000);
    if (typeof overrideResponse === 'function') {
      await overrideResponse(info);
    }
    return new HttpResponse(null, { status: 204 });
  });
};

export const getGetCardUserResourcesMockHandler = (
  overrideResponse?:
    | GetCardUserResources200
    | ((
        info: Parameters<Parameters<typeof http.post>[1]>[0],
      ) => Promise<GetCardUserResources200> | GetCardUserResources200),
) => {
  return http.post('*/card_users/resources', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getGetCardUserResourcesResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getPatchCardUsersCardMockHandler = (
  overrideResponse?:
    | PatchCardUsersCard200
    | ((
        info: Parameters<Parameters<typeof http.patch>[1]>[0],
      ) => Promise<PatchCardUsersCard200> | PatchCardUsersCard200),
) => {
  return http.patch('*/card_users/card', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getPatchCardUsersCardResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getPostCardUsersScannedImageMockHandler = (
  overrideResponse?:
    | PostCardUsersScannedImage201
    | ((
        info: Parameters<Parameters<typeof http.post>[1]>[0],
      ) =>
        | Promise<PostCardUsersScannedImage201>
        | PostCardUsersScannedImage201),
) => {
  return http.post('*/card_users/:id/scanned_image', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getPostCardUsersScannedImageResponseMock(),
      ),
      { status: 201, headers: { 'Content-Type': 'application/json' } },
    );
  });
};
export const getCardUsersMock = () => [
  getCreateCardsForUserMockHandler(),
  getPatchCardUsersMockHandler(),
  getDeleteCardMockHandler(),
  getAddCardsForUserMockHandler(),
  getExportCollectionMockHandler(),
  getImportToCollectionMockHandler(),
  getUpdateFoilOnCardMockHandler(),
  getUpdateQualityOnCardMockHandler(),
  getUpdateLanguageOnCardMockHandler(),
  getCommitStagedCardsMockHandler(),
  getResetStagedCardsMockHandler(),
  getClearCollectionMockHandler(),
  getGetCardUserResourcesMockHandler(),
  getPatchCardUsersCardMockHandler(),
  getPostCardUsersScannedImageMockHandler(),
];
