/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import { useMutation, useQuery } from '@tanstack/react-query';
import type {
  MutationFunction,
  QueryFunction,
  QueryKey,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult,
} from '@tanstack/react-query';

import axios from 'axios';
import type { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios';

import type {
  AddAllPrintings201,
  AddAllPrintingsBody,
  AddListedCard201,
  AddListedCardBody,
  CardListCommitParams,
  CardListSelectionBody,
  CreateCardList201,
  CreateCardListBody,
  GetCardLists200,
  RemoveListedCardBody,
  ShowCardList200,
  ShowCardListParams,
  UpdateCardList200,
  UpdateCardListBody,
} from '.././model';

type AwaitedInput<T> = PromiseLike<T> | T;

type Awaited<O> = O extends AwaitedInput<infer T> ? T : never;

/**
 * Lists all card lists owned by the current user
 * @summary List Card Lists
 */
export const getCardLists = (
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<GetCardLists200>> => {
  return axios.get(`/card_lists`, options);
};

export const getGetCardListsQueryKey = () => {
  return [`/card_lists`] as const;
};

export const getGetCardListsQueryOptions = <
  TData = Awaited<ReturnType<typeof getCardLists>>,
  TError = AxiosError<unknown>,
>(options?: {
  query?: UseQueryOptions<
    Awaited<ReturnType<typeof getCardLists>>,
    TError,
    TData
  >;
  axios?: AxiosRequestConfig;
}) => {
  const { query: queryOptions, axios: axiosOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetCardListsQueryKey();

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getCardLists>>> = ({
    signal,
  }) => getCardLists({ signal, ...axiosOptions });

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getCardLists>>,
    TError,
    TData
  > & { queryKey: QueryKey };
};

export type GetCardListsQueryResult = NonNullable<
  Awaited<ReturnType<typeof getCardLists>>
>;
export type GetCardListsQueryError = AxiosError<unknown>;

/**
 * @summary List Card Lists
 */

export function useGetCardLists<
  TData = Awaited<ReturnType<typeof getCardLists>>,
  TError = AxiosError<unknown>,
>(options?: {
  query?: UseQueryOptions<
    Awaited<ReturnType<typeof getCardLists>>,
    TError,
    TData
  >;
  axios?: AxiosRequestConfig;
}): UseQueryResult<TData, TError> & { queryKey: QueryKey } {
  const queryOptions = getGetCardListsQueryOptions(options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: QueryKey;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Creates a new Card List
 * @summary Create Card List
 */
export const createCardList = (
  createCardListBody: CreateCardListBody,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<CreateCardList201>> => {
  return axios.post(`/card_lists`, createCardListBody, options);
};

export const getCreateCardListMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof createCardList>>,
    TError,
    { data: CreateCardListBody },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof createCardList>>,
  TError,
  { data: CreateCardListBody },
  TContext
> => {
  const mutationKey = ['createCardList'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof createCardList>>,
    { data: CreateCardListBody }
  > = (props) => {
    const { data } = props ?? {};

    return createCardList(data, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type CreateCardListMutationResult = NonNullable<
  Awaited<ReturnType<typeof createCardList>>
>;
export type CreateCardListMutationBody = CreateCardListBody;
export type CreateCardListMutationError = AxiosError<unknown>;

/**
 * @summary Create Card List
 */
export const useCreateCardList = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof createCardList>>,
    TError,
    { data: CreateCardListBody },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof createCardList>>,
  TError,
  { data: CreateCardListBody },
  TContext
> => {
  const mutationOptions = getCreateCardListMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Gets the details of a Card List.
 * @summary Show Card List
 */
export const showCardList = (
  uuid: string,
  params?: ShowCardListParams,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<ShowCardList200>> => {
  return axios.get(`/card_lists/${uuid}`, {
    ...options,
    params: { ...params, ...options?.params },
  });
};

export const getShowCardListQueryKey = (
  uuid: string,
  params?: ShowCardListParams,
) => {
  return [`/card_lists/${uuid}`, ...(params ? [params] : [])] as const;
};

export const getShowCardListQueryOptions = <
  TData = Awaited<ReturnType<typeof showCardList>>,
  TError = AxiosError<unknown>,
>(
  uuid: string,
  params?: ShowCardListParams,
  options?: {
    query?: UseQueryOptions<
      Awaited<ReturnType<typeof showCardList>>,
      TError,
      TData
    >;
    axios?: AxiosRequestConfig;
  },
) => {
  const { query: queryOptions, axios: axiosOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ?? getShowCardListQueryKey(uuid, params);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof showCardList>>> = ({
    signal,
  }) => showCardList(uuid, params, { signal, ...axiosOptions });

  return {
    queryKey,
    queryFn,
    enabled: !!uuid,
    ...queryOptions,
  } as UseQueryOptions<
    Awaited<ReturnType<typeof showCardList>>,
    TError,
    TData
  > & { queryKey: QueryKey };
};

export type ShowCardListQueryResult = NonNullable<
  Awaited<ReturnType<typeof showCardList>>
>;
export type ShowCardListQueryError = AxiosError<unknown>;

/**
 * @summary Show Card List
 */

export function useShowCardList<
  TData = Awaited<ReturnType<typeof showCardList>>,
  TError = AxiosError<unknown>,
>(
  uuid: string,
  params?: ShowCardListParams,
  options?: {
    query?: UseQueryOptions<
      Awaited<ReturnType<typeof showCardList>>,
      TError,
      TData
    >;
    axios?: AxiosRequestConfig;
  },
): UseQueryResult<TData, TError> & { queryKey: QueryKey } {
  const queryOptions = getShowCardListQueryOptions(uuid, params, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: QueryKey;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Removes a Card List
 * @summary Remove Card List
 */
export const removeCardList = (
  uuid: string,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<void>> => {
  return axios.delete(`/card_lists/${uuid}`, options);
};

export const getRemoveCardListMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof removeCardList>>,
    TError,
    { uuid: string },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof removeCardList>>,
  TError,
  { uuid: string },
  TContext
> => {
  const mutationKey = ['removeCardList'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof removeCardList>>,
    { uuid: string }
  > = (props) => {
    const { uuid } = props ?? {};

    return removeCardList(uuid, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type RemoveCardListMutationResult = NonNullable<
  Awaited<ReturnType<typeof removeCardList>>
>;

export type RemoveCardListMutationError = AxiosError<unknown>;

/**
 * @summary Remove Card List
 */
export const useRemoveCardList = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof removeCardList>>,
    TError,
    { uuid: string },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof removeCardList>>,
  TError,
  { uuid: string },
  TContext
> => {
  const mutationOptions = getRemoveCardListMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Updates a Card List
 * @summary Update Card List
 */
export const updateCardList = (
  uuid: string,
  updateCardListBody: UpdateCardListBody,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<UpdateCardList200>> => {
  return axios.patch(`/card_lists/${uuid}`, updateCardListBody, options);
};

export const getUpdateCardListMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof updateCardList>>,
    TError,
    { uuid: string; data: UpdateCardListBody },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof updateCardList>>,
  TError,
  { uuid: string; data: UpdateCardListBody },
  TContext
> => {
  const mutationKey = ['updateCardList'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof updateCardList>>,
    { uuid: string; data: UpdateCardListBody }
  > = (props) => {
    const { uuid, data } = props ?? {};

    return updateCardList(uuid, data, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type UpdateCardListMutationResult = NonNullable<
  Awaited<ReturnType<typeof updateCardList>>
>;
export type UpdateCardListMutationBody = UpdateCardListBody;
export type UpdateCardListMutationError = AxiosError<unknown>;

/**
 * @summary Update Card List
 */
export const useUpdateCardList = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof updateCardList>>,
    TError,
    { uuid: string; data: UpdateCardListBody },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof updateCardList>>,
  TError,
  { uuid: string; data: UpdateCardListBody },
  TContext
> => {
  const mutationOptions = getUpdateCardListMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Add a Listed Card to a Card List
 * @summary Add Listed Card
 */
export const addListedCard = (
  uuid: string,
  addListedCardBody: AddListedCardBody,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<AddListedCard201>> => {
  return axios.post(`/card_lists/${uuid}/add`, addListedCardBody, options);
};

export const getAddListedCardMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof addListedCard>>,
    TError,
    { uuid: string; data: AddListedCardBody },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof addListedCard>>,
  TError,
  { uuid: string; data: AddListedCardBody },
  TContext
> => {
  const mutationKey = ['addListedCard'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof addListedCard>>,
    { uuid: string; data: AddListedCardBody }
  > = (props) => {
    const { uuid, data } = props ?? {};

    return addListedCard(uuid, data, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type AddListedCardMutationResult = NonNullable<
  Awaited<ReturnType<typeof addListedCard>>
>;
export type AddListedCardMutationBody = AddListedCardBody;
export type AddListedCardMutationError = AxiosError<unknown>;

/**
 * @summary Add Listed Card
 */
export const useAddListedCard = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof addListedCard>>,
    TError,
    { uuid: string; data: AddListedCardBody },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof addListedCard>>,
  TError,
  { uuid: string; data: AddListedCardBody },
  TContext
> => {
  const mutationOptions = getAddListedCardMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Add all printings for cards that match a name
 * @summary Add All Printings
 */
export const addAllPrintings = (
  uuid: string,
  addAllPrintingsBody: AddAllPrintingsBody,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<AddAllPrintings201>> => {
  return axios.post(
    `/card_lists/${uuid}/add_all_printings`,
    addAllPrintingsBody,
    options,
  );
};

export const getAddAllPrintingsMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof addAllPrintings>>,
    TError,
    { uuid: string; data: AddAllPrintingsBody },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof addAllPrintings>>,
  TError,
  { uuid: string; data: AddAllPrintingsBody },
  TContext
> => {
  const mutationKey = ['addAllPrintings'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof addAllPrintings>>,
    { uuid: string; data: AddAllPrintingsBody }
  > = (props) => {
    const { uuid, data } = props ?? {};

    return addAllPrintings(uuid, data, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type AddAllPrintingsMutationResult = NonNullable<
  Awaited<ReturnType<typeof addAllPrintings>>
>;
export type AddAllPrintingsMutationBody = AddAllPrintingsBody;
export type AddAllPrintingsMutationError = AxiosError<unknown>;

/**
 * @summary Add All Printings
 */
export const useAddAllPrintings = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof addAllPrintings>>,
    TError,
    { uuid: string; data: AddAllPrintingsBody },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof addAllPrintings>>,
  TError,
  { uuid: string; data: AddAllPrintingsBody },
  TContext
> => {
  const mutationOptions = getAddAllPrintingsMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Remove a Listed Card from a Card List
 * @summary Remove Listed Card
 */
export const removeListedCard = (
  uuid: string,
  removeListedCardBody: RemoveListedCardBody,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<void>> => {
  return axios.delete(`/card_lists/${uuid}/remove`, {
    data: removeListedCardBody,
    ...options,
  });
};

export const getRemoveListedCardMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof removeListedCard>>,
    TError,
    { uuid: string; data: RemoveListedCardBody },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof removeListedCard>>,
  TError,
  { uuid: string; data: RemoveListedCardBody },
  TContext
> => {
  const mutationKey = ['removeListedCard'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof removeListedCard>>,
    { uuid: string; data: RemoveListedCardBody }
  > = (props) => {
    const { uuid, data } = props ?? {};

    return removeListedCard(uuid, data, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type RemoveListedCardMutationResult = NonNullable<
  Awaited<ReturnType<typeof removeListedCard>>
>;
export type RemoveListedCardMutationBody = RemoveListedCardBody;
export type RemoveListedCardMutationError = AxiosError<unknown>;

/**
 * @summary Remove Listed Card
 */
export const useRemoveListedCard = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof removeListedCard>>,
    TError,
    { uuid: string; data: RemoveListedCardBody },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof removeListedCard>>,
  TError,
  { uuid: string; data: RemoveListedCardBody },
  TContext
> => {
  const mutationOptions = getRemoveListedCardMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Creates Listed Cards based on selection of Card Users
 * @summary Add Card User Selection
 */
export const cardListSelection = (
  uuid: string,
  cardListSelectionBody: CardListSelectionBody,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<void>> => {
  return axios.post(
    `/card_lists/${uuid}/selection`,
    cardListSelectionBody,
    options,
  );
};

export const getCardListSelectionMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof cardListSelection>>,
    TError,
    { uuid: string; data: CardListSelectionBody },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof cardListSelection>>,
  TError,
  { uuid: string; data: CardListSelectionBody },
  TContext
> => {
  const mutationKey = ['cardListSelection'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof cardListSelection>>,
    { uuid: string; data: CardListSelectionBody }
  > = (props) => {
    const { uuid, data } = props ?? {};

    return cardListSelection(uuid, data, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type CardListSelectionMutationResult = NonNullable<
  Awaited<ReturnType<typeof cardListSelection>>
>;
export type CardListSelectionMutationBody = CardListSelectionBody;
export type CardListSelectionMutationError = AxiosError<unknown>;

/**
 * @summary Add Card User Selection
 */
export const useCardListSelection = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof cardListSelection>>,
    TError,
    { uuid: string; data: CardListSelectionBody },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof cardListSelection>>,
  TError,
  { uuid: string; data: CardListSelectionBody },
  TContext
> => {
  const mutationOptions = getCardListSelectionMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Creates ListedCards for all staged Card Users
 * @summary Commit to CardList
 */
export const cardListCommit = (
  uuid: string,
  params: CardListCommitParams,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<void>> => {
  return axios.post(`/card_lists/${uuid}/commit`, undefined, {
    ...options,
    params: { ...params, ...options?.params },
  });
};

export const getCardListCommitMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof cardListCommit>>,
    TError,
    { uuid: string; params: CardListCommitParams },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof cardListCommit>>,
  TError,
  { uuid: string; params: CardListCommitParams },
  TContext
> => {
  const mutationKey = ['cardListCommit'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof cardListCommit>>,
    { uuid: string; params: CardListCommitParams }
  > = (props) => {
    const { uuid, params } = props ?? {};

    return cardListCommit(uuid, params, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type CardListCommitMutationResult = NonNullable<
  Awaited<ReturnType<typeof cardListCommit>>
>;

export type CardListCommitMutationError = AxiosError<unknown>;

/**
 * @summary Commit to CardList
 */
export const useCardListCommit = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof cardListCommit>>,
    TError,
    { uuid: string; params: CardListCommitParams },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof cardListCommit>>,
  TError,
  { uuid: string; params: CardListCommitParams },
  TContext
> => {
  const mutationOptions = getCardListCommitMutationOptions(options);

  return useMutation(mutationOptions);
};
