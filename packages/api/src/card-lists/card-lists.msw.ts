/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import { faker } from '@faker-js/faker';

import { HttpResponse, delay, http } from 'msw';

import type {
  AddAllPrintings201,
  AddListedCard201,
  CreateCardList201,
  GetCardLists200,
  ShowCardList200,
  UpdateCardList200,
} from '.././model';

export const getGetCardListsResponseMock = (
  overrideResponse: Partial<GetCardLists200> = {},
): GetCardLists200 => ({
  card_lists: faker.helpers.arrayElement([
    Array.from(
      { length: faker.number.int({ min: 1, max: 10 }) },
      (_, i) => i + 1,
    ).map(() => ({
      name: faker.helpers.arrayElement([faker.string.alpha(20), undefined]),
      id: faker.helpers.arrayElement([
        faker.number.int({ min: undefined, max: undefined }),
        undefined,
      ]),
      created_at: faker.helpers.arrayElement([
        `${faker.date.past().toISOString().split('.')[0]}Z`,
        undefined,
      ]),
    })),
    undefined,
  ]),
  ...overrideResponse,
});

export const getCreateCardListResponseMock = (
  overrideResponse: Partial<CreateCardList201> = {},
): CreateCardList201 => ({
  card_list: faker.helpers.arrayElement([
    {
      name: faker.helpers.arrayElement([faker.string.alpha(20), undefined]),
      id: faker.helpers.arrayElement([
        faker.number.int({ min: undefined, max: undefined }),
        undefined,
      ]),
      count: faker.helpers.arrayElement([
        faker.number.int({ min: undefined, max: undefined }),
        undefined,
      ]),
      created_at: faker.helpers.arrayElement([
        `${faker.date.past().toISOString().split('.')[0]}Z`,
        undefined,
      ]),
    },
    undefined,
  ]),
  ...overrideResponse,
});

export const getShowCardListResponseMock = (
  overrideResponse: Partial<ShowCardList200> = {},
): ShowCardList200 => ({
  card_list: faker.helpers.arrayElement([
    {
      name: faker.helpers.arrayElement([faker.string.alpha(20), undefined]),
      id: faker.helpers.arrayElement([
        faker.number.int({ min: undefined, max: undefined }),
        undefined,
      ]),
      count: faker.helpers.arrayElement([
        faker.number.int({ min: undefined, max: undefined }),
        undefined,
      ]),
      created_at: faker.helpers.arrayElement([
        `${faker.date.past().toISOString().split('.')[0]}Z`,
        undefined,
      ]),
      listed_cards: faker.helpers.arrayElement([
        Array.from(
          { length: faker.number.int({ min: 1, max: 10 }) },
          (_, i) => i + 1,
        ).map(() => ({
          json_id: faker.helpers.arrayElement([
            faker.string.alpha(20),
            undefined,
          ]),
          foil: faker.helpers.arrayElement([
            faker.datatype.boolean(),
            undefined,
          ]),
          language: faker.helpers.arrayElement([
            faker.string.alpha(20),
            undefined,
          ]),
          created_at: faker.helpers.arrayElement([
            `${faker.date.past().toISOString().split('.')[0]}Z`,
            undefined,
          ]),
        })),
        undefined,
      ]),
      cards: faker.helpers.arrayElement([
        {
          json_id: faker.helpers.arrayElement([
            faker.string.alpha(20),
            undefined,
          ]),
          name: faker.helpers.arrayElement([faker.string.alpha(20), undefined]),
        },
        undefined,
      ]),
    },
    undefined,
  ]),
  ...overrideResponse,
});

export const getUpdateCardListResponseMock = (
  overrideResponse: Partial<UpdateCardList200> = {},
): UpdateCardList200 => ({
  card_list: faker.helpers.arrayElement([
    {
      name: faker.helpers.arrayElement([faker.string.alpha(20), undefined]),
      id: faker.helpers.arrayElement([
        faker.number.int({ min: undefined, max: undefined }),
        undefined,
      ]),
      count: faker.helpers.arrayElement([
        faker.number.int({ min: undefined, max: undefined }),
        undefined,
      ]),
      created_at: faker.helpers.arrayElement([
        `${faker.date.past().toISOString().split('.')[0]}Z`,
        undefined,
      ]),
      listed_cards: faker.helpers.arrayElement([
        Array.from(
          { length: faker.number.int({ min: 1, max: 10 }) },
          (_, i) => i + 1,
        ).map(() => ({
          json_id: faker.helpers.arrayElement([
            faker.string.alpha(20),
            undefined,
          ]),
          foil: faker.helpers.arrayElement([
            faker.datatype.boolean(),
            undefined,
          ]),
          language: faker.helpers.arrayElement([
            faker.string.alpha(20),
            undefined,
          ]),
          created_at: faker.helpers.arrayElement([
            `${faker.date.past().toISOString().split('.')[0]}Z`,
            undefined,
          ]),
        })),
        undefined,
      ]),
      cards: faker.helpers.arrayElement([
        {
          json_id: faker.helpers.arrayElement([
            faker.string.alpha(20),
            undefined,
          ]),
          name: faker.helpers.arrayElement([faker.string.alpha(20), undefined]),
        },
        undefined,
      ]),
    },
    undefined,
  ]),
  ...overrideResponse,
});

export const getAddListedCardResponseMock = (
  overrideResponse: Partial<AddListedCard201> = {},
): AddListedCard201 => ({
  listed_card: faker.helpers.arrayElement([
    {
      json_id: faker.helpers.arrayElement([faker.string.alpha(20), undefined]),
      foil: faker.helpers.arrayElement([faker.datatype.boolean(), undefined]),
      language: faker.helpers.arrayElement([faker.string.alpha(20), undefined]),
      created_at: faker.helpers.arrayElement([
        faker.string.alpha(20),
        undefined,
      ]),
    },
    undefined,
  ]),
  card: faker.helpers.arrayElement([{}, undefined]),
  ...overrideResponse,
});

export const getAddAllPrintingsResponseMock = (
  overrideResponse: Partial<AddAllPrintings201> = {},
): AddAllPrintings201 => ({
  listed_cards: faker.helpers.arrayElement([
    Array.from(
      { length: faker.number.int({ min: 1, max: 10 }) },
      (_, i) => i + 1,
    ).map(() => ({
      json_id: faker.helpers.arrayElement([faker.string.alpha(20), undefined]),
      foil: faker.helpers.arrayElement([faker.datatype.boolean(), undefined]),
      language: faker.helpers.arrayElement([faker.string.alpha(20), undefined]),
      created_at: faker.helpers.arrayElement([
        faker.string.alpha(20),
        undefined,
      ]),
    })),
    undefined,
  ]),
  cards: faker.helpers.arrayElement([{}, undefined]),
  ...overrideResponse,
});

export const getGetCardListsMockHandler = (
  overrideResponse?:
    | GetCardLists200
    | ((
        info: Parameters<Parameters<typeof http.get>[1]>[0],
      ) => Promise<GetCardLists200> | GetCardLists200),
) => {
  return http.get('*/card_lists', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getGetCardListsResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getCreateCardListMockHandler = (
  overrideResponse?:
    | CreateCardList201
    | ((
        info: Parameters<Parameters<typeof http.post>[1]>[0],
      ) => Promise<CreateCardList201> | CreateCardList201),
) => {
  return http.post('*/card_lists', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getCreateCardListResponseMock(),
      ),
      { status: 201, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getShowCardListMockHandler = (
  overrideResponse?:
    | ShowCardList200
    | ((
        info: Parameters<Parameters<typeof http.get>[1]>[0],
      ) => Promise<ShowCardList200> | ShowCardList200),
) => {
  return http.get('*/card_lists/:uuid', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getShowCardListResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getRemoveCardListMockHandler = (
  overrideResponse?:
    | void
    | ((
        info: Parameters<Parameters<typeof http.delete>[1]>[0],
      ) => Promise<void> | void),
) => {
  return http.delete('*/card_lists/:uuid', async (info) => {
    await delay(1000);
    if (typeof overrideResponse === 'function') {
      await overrideResponse(info);
    }
    return new HttpResponse(null, { status: 200 });
  });
};

export const getUpdateCardListMockHandler = (
  overrideResponse?:
    | UpdateCardList200
    | ((
        info: Parameters<Parameters<typeof http.patch>[1]>[0],
      ) => Promise<UpdateCardList200> | UpdateCardList200),
) => {
  return http.patch('*/card_lists/:uuid', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getUpdateCardListResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getAddListedCardMockHandler = (
  overrideResponse?:
    | AddListedCard201
    | ((
        info: Parameters<Parameters<typeof http.post>[1]>[0],
      ) => Promise<AddListedCard201> | AddListedCard201),
) => {
  return http.post('*/card_lists/:uuid/add', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getAddListedCardResponseMock(),
      ),
      { status: 201, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getAddAllPrintingsMockHandler = (
  overrideResponse?:
    | AddAllPrintings201
    | ((
        info: Parameters<Parameters<typeof http.post>[1]>[0],
      ) => Promise<AddAllPrintings201> | AddAllPrintings201),
) => {
  return http.post('*/card_lists/:uuid/add_all_printings', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getAddAllPrintingsResponseMock(),
      ),
      { status: 201, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getRemoveListedCardMockHandler = (
  overrideResponse?:
    | void
    | ((
        info: Parameters<Parameters<typeof http.delete>[1]>[0],
      ) => Promise<void> | void),
) => {
  return http.delete('*/card_lists/:uuid/remove', async (info) => {
    await delay(1000);
    if (typeof overrideResponse === 'function') {
      await overrideResponse(info);
    }
    return new HttpResponse(null, { status: 200 });
  });
};

export const getCardListSelectionMockHandler = (
  overrideResponse?:
    | void
    | ((
        info: Parameters<Parameters<typeof http.post>[1]>[0],
      ) => Promise<void> | void),
) => {
  return http.post('*/card_lists/:uuid/selection', async (info) => {
    await delay(1000);
    if (typeof overrideResponse === 'function') {
      await overrideResponse(info);
    }
    return new HttpResponse(null, { status: 200 });
  });
};

export const getCardListCommitMockHandler = (
  overrideResponse?:
    | void
    | ((
        info: Parameters<Parameters<typeof http.post>[1]>[0],
      ) => Promise<void> | void),
) => {
  return http.post('*/card_lists/:uuid/commit', async (info) => {
    await delay(1000);
    if (typeof overrideResponse === 'function') {
      await overrideResponse(info);
    }
    return new HttpResponse(null, { status: 200 });
  });
};
export const getCardListsMock = () => [
  getGetCardListsMockHandler(),
  getCreateCardListMockHandler(),
  getShowCardListMockHandler(),
  getRemoveCardListMockHandler(),
  getUpdateCardListMockHandler(),
  getAddListedCardMockHandler(),
  getAddAllPrintingsMockHandler(),
  getRemoveListedCardMockHandler(),
  getCardListSelectionMockHandler(),
  getCardListCommitMockHandler(),
];
