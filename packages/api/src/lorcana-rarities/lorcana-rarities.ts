/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import { useQuery } from '@tanstack/react-query';
import type {
  QueryFunction,
  QueryKey,
  UseQueryOptions,
  UseQueryResult,
} from '@tanstack/react-query';

import axios from 'axios';
import type { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios';

import type { GetAllLorcanaRarities200 } from '.././model';

type AwaitedInput<T> = PromiseLike<T> | T;

type Awaited<O> = O extends AwaitedInput<infer T> ? T : never;

/**
 * Get all lorcana rarities
 * @summary Get all lorcana rarities
 */
export const getAllLorcanaRarities = (
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<GetAllLorcanaRarities200>> => {
  return axios.get(`/lorcana_rarities`, options);
};

export const getGetAllLorcanaRaritiesQueryKey = () => {
  return [`/lorcana_rarities`] as const;
};

export const getGetAllLorcanaRaritiesQueryOptions = <
  TData = Awaited<ReturnType<typeof getAllLorcanaRarities>>,
  TError = AxiosError<unknown>,
>(options?: {
  query?: UseQueryOptions<
    Awaited<ReturnType<typeof getAllLorcanaRarities>>,
    TError,
    TData
  >;
  axios?: AxiosRequestConfig;
}) => {
  const { query: queryOptions, axios: axiosOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetAllLorcanaRaritiesQueryKey();

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof getAllLorcanaRarities>>
  > = ({ signal }) => getAllLorcanaRarities({ signal, ...axiosOptions });

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getAllLorcanaRarities>>,
    TError,
    TData
  > & { queryKey: QueryKey };
};

export type GetAllLorcanaRaritiesQueryResult = NonNullable<
  Awaited<ReturnType<typeof getAllLorcanaRarities>>
>;
export type GetAllLorcanaRaritiesQueryError = AxiosError<unknown>;

/**
 * @summary Get all lorcana rarities
 */

export function useGetAllLorcanaRarities<
  TData = Awaited<ReturnType<typeof getAllLorcanaRarities>>,
  TError = AxiosError<unknown>,
>(options?: {
  query?: UseQueryOptions<
    Awaited<ReturnType<typeof getAllLorcanaRarities>>,
    TError,
    TData
  >;
  axios?: AxiosRequestConfig;
}): UseQueryResult<TData, TError> & { queryKey: QueryKey } {
  const queryOptions = getGetAllLorcanaRaritiesQueryOptions(options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: QueryKey;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}
