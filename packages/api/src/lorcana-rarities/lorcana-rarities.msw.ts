/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import { faker } from '@faker-js/faker';

import { HttpResponse, delay, http } from 'msw';

import type { GetAllLorcanaRarities200 } from '.././model';

export const getGetAllLorcanaRaritiesResponseMock = (
  overrideResponse: Partial<GetAllLorcanaRarities200> = {},
): GetAllLorcanaRarities200 => ({
  rarities: faker.helpers.arrayElement([
    Array.from(
      { length: faker.number.int({ min: 1, max: 10 }) },
      (_, i) => i + 1,
    ).map(() => ({})),
    undefined,
  ]),
  ...overrideResponse,
});

export const getGetAllLorcanaRaritiesMockHandler = (
  overrideResponse?:
    | GetAllLorcanaRarities200
    | ((
        info: Parameters<Parameters<typeof http.get>[1]>[0],
      ) => Promise<GetAllLorcanaRarities200> | GetAllLorcanaRarities200),
) => {
  return http.get('*/lorcana_rarities', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getGetAllLorcanaRaritiesResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};
export const getLorcanaRaritiesMock = () => [
  getGetAllLorcanaRaritiesMockHandler(),
];
