/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import { faker } from '@faker-js/faker';

import { HttpResponse, delay, http } from 'msw';

import type { GetHashVersionForMcr200 } from '.././model';

export const getGetHashVersionForMcrResponseMock = (
  overrideResponse: Partial<GetHashVersionForMcr200> = {},
): GetHashVersionForMcr200 => ({
  version: faker.string.alpha(20),
  deprecated: faker.datatype.boolean(),
  ...overrideResponse,
});

export const getGetHashVersionForMcrMockHandler = (
  overrideResponse?:
    | GetHashVersionForMcr200
    | ((
        info: Parameters<Parameters<typeof http.get>[1]>[0],
      ) => Promise<GetHashVersionForMcr200> | GetHashVersionForMcr200),
) => {
  return http.get('*/mcr/hashes', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getGetHashVersionForMcrResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getUploadMetricsForMcrMockHandler = (
  overrideResponse?:
    | void
    | ((
        info: Parameters<Parameters<typeof http.post>[1]>[0],
      ) => Promise<void> | void),
) => {
  return http.post('*/mcr/metrics', async (info) => {
    await delay(1000);
    if (typeof overrideResponse === 'function') {
      await overrideResponse(info);
    }
    return new HttpResponse(null, { status: 204 });
  });
};
export const getMcrMock = () => [
  getGetHashVersionForMcrMockHandler(),
  getUploadMetricsForMcrMockHandler(),
];
