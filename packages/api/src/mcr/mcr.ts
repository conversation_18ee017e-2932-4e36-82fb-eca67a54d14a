/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import { useMutation, useQuery } from '@tanstack/react-query';
import type {
  MutationFunction,
  QueryFunction,
  QueryKey,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult,
} from '@tanstack/react-query';

import axios from 'axios';
import type { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios';

import type {
  GetHashVersionForMcr200,
  GetHashVersionForMcr400,
  GetHashVersionForMcr404,
  UploadMetricsForMcrBody,
} from '.././model';

type AwaitedInput<T> = PromiseLike<T> | T;

type Awaited<O> = O extends AwaitedInput<infer T> ? T : never;

/**
 * Get the MCR hash version for a corresponding MCR version.
+ version (string) - Specify the MCR version to get the corresponding hash. This is required.
 * @summary Get Hash Version for MCR >= v3
 */
export const getHashVersionForMcr = (
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<GetHashVersionForMcr200>> => {
  return axios.get(`/mcr/hashes`, options);
};

export const getGetHashVersionForMcrQueryKey = () => {
  return [`/mcr/hashes`] as const;
};

export const getGetHashVersionForMcrQueryOptions = <
  TData = Awaited<ReturnType<typeof getHashVersionForMcr>>,
  TError = AxiosError<GetHashVersionForMcr400 | GetHashVersionForMcr404>,
>(options?: {
  query?: UseQueryOptions<
    Awaited<ReturnType<typeof getHashVersionForMcr>>,
    TError,
    TData
  >;
  axios?: AxiosRequestConfig;
}) => {
  const { query: queryOptions, axios: axiosOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetHashVersionForMcrQueryKey();

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof getHashVersionForMcr>>
  > = ({ signal }) => getHashVersionForMcr({ signal, ...axiosOptions });

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getHashVersionForMcr>>,
    TError,
    TData
  > & { queryKey: QueryKey };
};

export type GetHashVersionForMcrQueryResult = NonNullable<
  Awaited<ReturnType<typeof getHashVersionForMcr>>
>;
export type GetHashVersionForMcrQueryError = AxiosError<
  GetHashVersionForMcr400 | GetHashVersionForMcr404
>;

/**
 * @summary Get Hash Version for MCR >= v3
 */

export function useGetHashVersionForMcr<
  TData = Awaited<ReturnType<typeof getHashVersionForMcr>>,
  TError = AxiosError<GetHashVersionForMcr400 | GetHashVersionForMcr404>,
>(options?: {
  query?: UseQueryOptions<
    Awaited<ReturnType<typeof getHashVersionForMcr>>,
    TError,
    TData
  >;
  axios?: AxiosRequestConfig;
}): UseQueryResult<TData, TError> & { queryKey: QueryKey } {
  const queryOptions = getGetHashVersionForMcrQueryOptions(options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: QueryKey;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Upload metrics about the MCR as a binary blob. Send as a form data object and multipart.
+ metrics (object) - The blog of binary data to upload
 * @summary Upload Metrics for MCR
 */
export const uploadMetricsForMcr = (
  uploadMetricsForMcrBody: UploadMetricsForMcrBody,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<void>> => {
  return axios.post(`/mcr/metrics`, uploadMetricsForMcrBody, options);
};

export const getUploadMetricsForMcrMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof uploadMetricsForMcr>>,
    TError,
    { data: UploadMetricsForMcrBody },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof uploadMetricsForMcr>>,
  TError,
  { data: UploadMetricsForMcrBody },
  TContext
> => {
  const mutationKey = ['uploadMetricsForMcr'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof uploadMetricsForMcr>>,
    { data: UploadMetricsForMcrBody }
  > = (props) => {
    const { data } = props ?? {};

    return uploadMetricsForMcr(data, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type UploadMetricsForMcrMutationResult = NonNullable<
  Awaited<ReturnType<typeof uploadMetricsForMcr>>
>;
export type UploadMetricsForMcrMutationBody = UploadMetricsForMcrBody;
export type UploadMetricsForMcrMutationError = AxiosError<unknown>;

/**
 * @summary Upload Metrics for MCR
 */
export const useUploadMetricsForMcr = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof uploadMetricsForMcr>>,
    TError,
    { data: UploadMetricsForMcrBody },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof uploadMetricsForMcr>>,
  TError,
  { data: UploadMetricsForMcrBody },
  TContext
> => {
  const mutationOptions = getUploadMetricsForMcrMutationOptions(options);

  return useMutation(mutationOptions);
};
