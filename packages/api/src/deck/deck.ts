/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import { useMutation, useQuery } from '@tanstack/react-query';
import type {
  MutationFunction,
  QueryFunction,
  QueryKey,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult,
} from '@tanstack/react-query';

import axios from 'axios';
import type { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios';

import type {
  CloneADeck201,
  CreateADeck201,
  CreateADeckParams,
  DeckStatistics200,
  DeckStatisticsParams,
  DeckToPdf200,
  DeckTurnProbabilities200,
  DeckTurnProbabilitiesParams,
  GetADeck200,
  GetADeckParams,
  GetAllDecks200,
  SearchForDecks200,
  SearchForDecksParams,
  UpdateADeck200,
  UpdateADeckParams,
  ValidateDeck200,
  ValidateDeckParams,
} from '.././model';

type AwaitedInput<T> = PromiseLike<T> | T;

type Awaited<O> = O extends AwaitedInput<infer T> ? T : never;

/**
 * Get all the decks for a user.
 * @summary Get all Decks
 */
export const getAllDecks = (
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<GetAllDecks200>> => {
  return axios.get(`/decks`, options);
};

export const getGetAllDecksQueryKey = () => {
  return [`/decks`] as const;
};

export const getGetAllDecksQueryOptions = <
  TData = Awaited<ReturnType<typeof getAllDecks>>,
  TError = AxiosError<unknown>,
>(options?: {
  query?: UseQueryOptions<
    Awaited<ReturnType<typeof getAllDecks>>,
    TError,
    TData
  >;
  axios?: AxiosRequestConfig;
}) => {
  const { query: queryOptions, axios: axiosOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetAllDecksQueryKey();

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getAllDecks>>> = ({
    signal,
  }) => getAllDecks({ signal, ...axiosOptions });

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getAllDecks>>,
    TError,
    TData
  > & { queryKey: QueryKey };
};

export type GetAllDecksQueryResult = NonNullable<
  Awaited<ReturnType<typeof getAllDecks>>
>;
export type GetAllDecksQueryError = AxiosError<unknown>;

/**
 * @summary Get all Decks
 */

export function useGetAllDecks<
  TData = Awaited<ReturnType<typeof getAllDecks>>,
  TError = AxiosError<unknown>,
>(options?: {
  query?: UseQueryOptions<
    Awaited<ReturnType<typeof getAllDecks>>,
    TError,
    TData
  >;
  axios?: AxiosRequestConfig;
}): UseQueryResult<TData, TError> & { queryKey: QueryKey } {
  const queryOptions = getGetAllDecksQueryOptions(options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: QueryKey;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Create a deck for the current user. Price reflects currency and pricing source preferences of the deck owner.
 * @summary Create a Deck
 */
export const createADeck = (
  params?: CreateADeckParams,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<CreateADeck201>> => {
  return axios.post(`/decks`, undefined, {
    ...options,
    params: { ...params, ...options?.params },
  });
};

export const getCreateADeckMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof createADeck>>,
    TError,
    { params?: CreateADeckParams },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof createADeck>>,
  TError,
  { params?: CreateADeckParams },
  TContext
> => {
  const mutationKey = ['createADeck'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof createADeck>>,
    { params?: CreateADeckParams }
  > = (props) => {
    const { params } = props ?? {};

    return createADeck(params, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type CreateADeckMutationResult = NonNullable<
  Awaited<ReturnType<typeof createADeck>>
>;

export type CreateADeckMutationError = AxiosError<unknown>;

/**
 * @summary Create a Deck
 */
export const useCreateADeck = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof createADeck>>,
    TError,
    { params?: CreateADeckParams },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof createADeck>>,
  TError,
  { params?: CreateADeckParams },
  TContext
> => {
  const mutationOptions = getCreateADeckMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Search for decks owned by the currently logged in user.
 * @summary Search for Decks
 */
export const searchForDecks = (
  params?: SearchForDecksParams,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<SearchForDecks200>> => {
  return axios.post(`/decks/search`, undefined, {
    ...options,
    params: { ...params, ...options?.params },
  });
};

export const getSearchForDecksMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof searchForDecks>>,
    TError,
    { params?: SearchForDecksParams },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof searchForDecks>>,
  TError,
  { params?: SearchForDecksParams },
  TContext
> => {
  const mutationKey = ['searchForDecks'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof searchForDecks>>,
    { params?: SearchForDecksParams }
  > = (props) => {
    const { params } = props ?? {};

    return searchForDecks(params, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type SearchForDecksMutationResult = NonNullable<
  Awaited<ReturnType<typeof searchForDecks>>
>;

export type SearchForDecksMutationError = AxiosError<unknown>;

/**
 * @summary Search for Decks
 */
export const useSearchForDecks = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof searchForDecks>>,
    TError,
    { params?: SearchForDecksParams },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof searchForDecks>>,
  TError,
  { params?: SearchForDecksParams },
  TContext
> => {
  const mutationOptions = getSearchForDecksMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Delete all decks owned by the user.
 * @summary Delete all Decks
 */
export const deleteAllDecks = (
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<void>> => {
  return axios.delete(`/decks/clear`, options);
};

export const getDeleteAllDecksMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteAllDecks>>,
    TError,
    void,
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof deleteAllDecks>>,
  TError,
  void,
  TContext
> => {
  const mutationKey = ['deleteAllDecks'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof deleteAllDecks>>,
    void
  > = () => {
    return deleteAllDecks(axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type DeleteAllDecksMutationResult = NonNullable<
  Awaited<ReturnType<typeof deleteAllDecks>>
>;

export type DeleteAllDecksMutationError = AxiosError<unknown>;

/**
 * @summary Delete all Decks
 */
export const useDeleteAllDecks = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteAllDecks>>,
    TError,
    void,
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof deleteAllDecks>>,
  TError,
  void,
  TContext
> => {
  const mutationOptions = getDeleteAllDecksMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * @summary Import a Deck
 */
export const importADeck = (
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<void>> => {
  return axios.get(`/decks/import`, options);
};

export const getImportADeckQueryKey = () => {
  return [`/decks/import`] as const;
};

export const getImportADeckQueryOptions = <
  TData = Awaited<ReturnType<typeof importADeck>>,
  TError = AxiosError<unknown>,
>(options?: {
  query?: UseQueryOptions<
    Awaited<ReturnType<typeof importADeck>>,
    TError,
    TData
  >;
  axios?: AxiosRequestConfig;
}) => {
  const { query: queryOptions, axios: axiosOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getImportADeckQueryKey();

  const queryFn: QueryFunction<Awaited<ReturnType<typeof importADeck>>> = ({
    signal,
  }) => importADeck({ signal, ...axiosOptions });

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof importADeck>>,
    TError,
    TData
  > & { queryKey: QueryKey };
};

export type ImportADeckQueryResult = NonNullable<
  Awaited<ReturnType<typeof importADeck>>
>;
export type ImportADeckQueryError = AxiosError<unknown>;

/**
 * @summary Import a Deck
 */

export function useImportADeck<
  TData = Awaited<ReturnType<typeof importADeck>>,
  TError = AxiosError<unknown>,
>(options?: {
  query?: UseQueryOptions<
    Awaited<ReturnType<typeof importADeck>>,
    TError,
    TData
  >;
  axios?: AxiosRequestConfig;
}): UseQueryResult<TData, TError> & { queryKey: QueryKey } {
  const queryOptions = getImportADeckQueryOptions(options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: QueryKey;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Get a particular deck. If user is not logged in, it will try to get a public deck. Price reflects currency and pricing source preferences of the deck owner.
 * @summary Get a Deck
 */
export const getADeck = (
  uuid: string,
  params?: GetADeckParams,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<GetADeck200>> => {
  return axios.get(`/decks/${uuid}`, {
    ...options,
    params: { ...params, ...options?.params },
  });
};

export const getGetADeckQueryKey = (uuid: string, params?: GetADeckParams) => {
  return [`/decks/${uuid}`, ...(params ? [params] : [])] as const;
};

export const getGetADeckQueryOptions = <
  TData = Awaited<ReturnType<typeof getADeck>>,
  TError = AxiosError<unknown>,
>(
  uuid: string,
  params?: GetADeckParams,
  options?: {
    query?: UseQueryOptions<
      Awaited<ReturnType<typeof getADeck>>,
      TError,
      TData
    >;
    axios?: AxiosRequestConfig;
  },
) => {
  const { query: queryOptions, axios: axiosOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetADeckQueryKey(uuid, params);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getADeck>>> = ({
    signal,
  }) => getADeck(uuid, params, { signal, ...axiosOptions });

  return {
    queryKey,
    queryFn,
    enabled: !!uuid,
    ...queryOptions,
  } as UseQueryOptions<Awaited<ReturnType<typeof getADeck>>, TError, TData> & {
    queryKey: QueryKey;
  };
};

export type GetADeckQueryResult = NonNullable<
  Awaited<ReturnType<typeof getADeck>>
>;
export type GetADeckQueryError = AxiosError<unknown>;

/**
 * @summary Get a Deck
 */

export function useGetADeck<
  TData = Awaited<ReturnType<typeof getADeck>>,
  TError = AxiosError<unknown>,
>(
  uuid: string,
  params?: GetADeckParams,
  options?: {
    query?: UseQueryOptions<
      Awaited<ReturnType<typeof getADeck>>,
      TError,
      TData
    >;
    axios?: AxiosRequestConfig;
  },
): UseQueryResult<TData, TError> & { queryKey: QueryKey } {
  const queryOptions = getGetADeckQueryOptions(uuid, params, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: QueryKey;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Delete a Deck
 * @summary Delete a Deck
 */
export const deleteADeck = (
  uuid: string,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<void>> => {
  return axios.delete(`/decks/${uuid}`, options);
};

export const getDeleteADeckMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteADeck>>,
    TError,
    { uuid: string },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof deleteADeck>>,
  TError,
  { uuid: string },
  TContext
> => {
  const mutationKey = ['deleteADeck'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof deleteADeck>>,
    { uuid: string }
  > = (props) => {
    const { uuid } = props ?? {};

    return deleteADeck(uuid, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type DeleteADeckMutationResult = NonNullable<
  Awaited<ReturnType<typeof deleteADeck>>
>;

export type DeleteADeckMutationError = AxiosError<unknown>;

/**
 * @summary Delete a Deck
 */
export const useDeleteADeck = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteADeck>>,
    TError,
    { uuid: string },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof deleteADeck>>,
  TError,
  { uuid: string },
  TContext
> => {
  const mutationOptions = getDeleteADeckMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Updates the attributes of the deck.
 * @summary Update a Deck
 */
export const updateADeck = (
  uuid: string,
  params?: UpdateADeckParams,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<UpdateADeck200>> => {
  return axios.patch(`/decks/${uuid}`, undefined, {
    ...options,
    params: { ...params, ...options?.params },
  });
};

export const getUpdateADeckMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof updateADeck>>,
    TError,
    { uuid: string; params?: UpdateADeckParams },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof updateADeck>>,
  TError,
  { uuid: string; params?: UpdateADeckParams },
  TContext
> => {
  const mutationKey = ['updateADeck'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof updateADeck>>,
    { uuid: string; params?: UpdateADeckParams }
  > = (props) => {
    const { uuid, params } = props ?? {};

    return updateADeck(uuid, params, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type UpdateADeckMutationResult = NonNullable<
  Awaited<ReturnType<typeof updateADeck>>
>;

export type UpdateADeckMutationError = AxiosError<unknown>;

/**
 * @summary Update a Deck
 */
export const useUpdateADeck = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof updateADeck>>,
    TError,
    { uuid: string; params?: UpdateADeckParams },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof updateADeck>>,
  TError,
  { uuid: string; params?: UpdateADeckParams },
  TContext
> => {
  const mutationOptions = getUpdateADeckMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Validate a deck's card are legal for the specified format
 * @summary Validate a Deck
 */
export const validateDeck = (
  uuid: string,
  params?: ValidateDeckParams,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<ValidateDeck200>> => {
  return axios.post(`/decks/${uuid}/validate`, undefined, {
    ...options,
    params: { ...params, ...options?.params },
  });
};

export const getValidateDeckMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof validateDeck>>,
    TError,
    { uuid: string; params?: ValidateDeckParams },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof validateDeck>>,
  TError,
  { uuid: string; params?: ValidateDeckParams },
  TContext
> => {
  const mutationKey = ['validateDeck'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof validateDeck>>,
    { uuid: string; params?: ValidateDeckParams }
  > = (props) => {
    const { uuid, params } = props ?? {};

    return validateDeck(uuid, params, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type ValidateDeckMutationResult = NonNullable<
  Awaited<ReturnType<typeof validateDeck>>
>;

export type ValidateDeckMutationError = AxiosError<unknown>;

/**
 * @summary Validate a Deck
 */
export const useValidateDeck = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof validateDeck>>,
    TError,
    { uuid: string; params?: ValidateDeckParams },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof validateDeck>>,
  TError,
  { uuid: string; params?: ValidateDeckParams },
  TContext
> => {
  const mutationOptions = getValidateDeckMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Get statistics for a specific deck
 * @summary Get Deck Statistics
 */
export const deckStatistics = (
  uuid: string,
  params?: DeckStatisticsParams,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<DeckStatistics200>> => {
  return axios.post(`/decks/${uuid}/statistics`, undefined, {
    ...options,
    params: { ...params, ...options?.params },
  });
};

export const getDeckStatisticsMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deckStatistics>>,
    TError,
    { uuid: string; params?: DeckStatisticsParams },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof deckStatistics>>,
  TError,
  { uuid: string; params?: DeckStatisticsParams },
  TContext
> => {
  const mutationKey = ['deckStatistics'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof deckStatistics>>,
    { uuid: string; params?: DeckStatisticsParams }
  > = (props) => {
    const { uuid, params } = props ?? {};

    return deckStatistics(uuid, params, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type DeckStatisticsMutationResult = NonNullable<
  Awaited<ReturnType<typeof deckStatistics>>
>;

export type DeckStatisticsMutationError = AxiosError<unknown>;

/**
 * @summary Get Deck Statistics
 */
export const useDeckStatistics = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deckStatistics>>,
    TError,
    { uuid: string; params?: DeckStatisticsParams },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof deckStatistics>>,
  TError,
  { uuid: string; params?: DeckStatisticsParams },
  TContext
> => {
  const mutationOptions = getDeckStatisticsMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Get the probability of drawing a card in the first 3 turns based on it's type, mana cost, and/or name
 * @summary Get Deck Turn Probabilities
 */
export const deckTurnProbabilities = (
  uuid: string,
  params?: DeckTurnProbabilitiesParams,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<DeckTurnProbabilities200>> => {
  return axios.post(`/decks/${uuid}/turn_probabilities`, undefined, {
    ...options,
    params: { ...params, ...options?.params },
  });
};

export const getDeckTurnProbabilitiesMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deckTurnProbabilities>>,
    TError,
    { uuid: string; params?: DeckTurnProbabilitiesParams },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof deckTurnProbabilities>>,
  TError,
  { uuid: string; params?: DeckTurnProbabilitiesParams },
  TContext
> => {
  const mutationKey = ['deckTurnProbabilities'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof deckTurnProbabilities>>,
    { uuid: string; params?: DeckTurnProbabilitiesParams }
  > = (props) => {
    const { uuid, params } = props ?? {};

    return deckTurnProbabilities(uuid, params, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type DeckTurnProbabilitiesMutationResult = NonNullable<
  Awaited<ReturnType<typeof deckTurnProbabilities>>
>;

export type DeckTurnProbabilitiesMutationError = AxiosError<unknown>;

/**
 * @summary Get Deck Turn Probabilities
 */
export const useDeckTurnProbabilities = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deckTurnProbabilities>>,
    TError,
    { uuid: string; params?: DeckTurnProbabilitiesParams },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof deckTurnProbabilities>>,
  TError,
  { uuid: string; params?: DeckTurnProbabilitiesParams },
  TContext
> => {
  const mutationOptions = getDeckTurnProbabilitiesMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Creates a PDF and returns the raw data to the client
 * @summary Export deck as PDF
 */
export const deckToPdf = (
  uuid: string,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<DeckToPdf200>> => {
  return axios.post(`/decks/${uuid}/to_pdf`, undefined, options);
};

export const getDeckToPdfMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deckToPdf>>,
    TError,
    { uuid: string },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof deckToPdf>>,
  TError,
  { uuid: string },
  TContext
> => {
  const mutationKey = ['deckToPdf'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof deckToPdf>>,
    { uuid: string }
  > = (props) => {
    const { uuid } = props ?? {};

    return deckToPdf(uuid, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type DeckToPdfMutationResult = NonNullable<
  Awaited<ReturnType<typeof deckToPdf>>
>;

export type DeckToPdfMutationError = AxiosError<unknown>;

/**
 * @summary Export deck as PDF
 */
export const useDeckToPdf = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deckToPdf>>,
    TError,
    { uuid: string },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof deckToPdf>>,
  TError,
  { uuid: string },
  TContext
> => {
  const mutationOptions = getDeckToPdfMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Clones the specified deck one to one, except for any tags. It can only clone public decks or those owned by the user. Price reflects currency and pricing source preferences of the deck owner.
 * @summary Clone a Deck
 */
export const cloneADeck = (
  uuid: string,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<CloneADeck201>> => {
  return axios.post(`/decks/${uuid}/clone`, undefined, options);
};

export const getCloneADeckMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof cloneADeck>>,
    TError,
    { uuid: string },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof cloneADeck>>,
  TError,
  { uuid: string },
  TContext
> => {
  const mutationKey = ['cloneADeck'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof cloneADeck>>,
    { uuid: string }
  > = (props) => {
    const { uuid } = props ?? {};

    return cloneADeck(uuid, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type CloneADeckMutationResult = NonNullable<
  Awaited<ReturnType<typeof cloneADeck>>
>;

export type CloneADeckMutationError = AxiosError<unknown>;

/**
 * @summary Clone a Deck
 */
export const useCloneADeck = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof cloneADeck>>,
    TError,
    { uuid: string },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof cloneADeck>>,
  TError,
  { uuid: string },
  TContext
> => {
  const mutationOptions = getCloneADeckMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * @summary Export a Deck
 */
export const exportADeck = (
  uuid: string,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<void>> => {
  return axios.get(`/decks/${uuid}/export`, options);
};

export const getExportADeckQueryKey = (uuid: string) => {
  return [`/decks/${uuid}/export`] as const;
};

export const getExportADeckQueryOptions = <
  TData = Awaited<ReturnType<typeof exportADeck>>,
  TError = AxiosError<unknown>,
>(
  uuid: string,
  options?: {
    query?: UseQueryOptions<
      Awaited<ReturnType<typeof exportADeck>>,
      TError,
      TData
    >;
    axios?: AxiosRequestConfig;
  },
) => {
  const { query: queryOptions, axios: axiosOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getExportADeckQueryKey(uuid);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof exportADeck>>> = ({
    signal,
  }) => exportADeck(uuid, { signal, ...axiosOptions });

  return {
    queryKey,
    queryFn,
    enabled: !!uuid,
    ...queryOptions,
  } as UseQueryOptions<
    Awaited<ReturnType<typeof exportADeck>>,
    TError,
    TData
  > & { queryKey: QueryKey };
};

export type ExportADeckQueryResult = NonNullable<
  Awaited<ReturnType<typeof exportADeck>>
>;
export type ExportADeckQueryError = AxiosError<unknown>;

/**
 * @summary Export a Deck
 */

export function useExportADeck<
  TData = Awaited<ReturnType<typeof exportADeck>>,
  TError = AxiosError<unknown>,
>(
  uuid: string,
  options?: {
    query?: UseQueryOptions<
      Awaited<ReturnType<typeof exportADeck>>,
      TError,
      TData
    >;
    axios?: AxiosRequestConfig;
  },
): UseQueryResult<TData, TError> & { queryKey: QueryKey } {
  const queryOptions = getExportADeckQueryOptions(uuid, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: QueryKey;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}
