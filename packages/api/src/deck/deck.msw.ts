/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import { faker } from '@faker-js/faker';

import { HttpResponse, delay, http } from 'msw';

import type {
  CloneADeck201,
  CreateADeck201,
  DeckStatistics200,
  DeckToPdf200,
  DeckTurnProbabilities200,
  GetADeck200,
  GetAllDecks200,
  SearchForDecks200,
  UpdateADeck200,
  ValidateDeck200,
} from '.././model';

export const getGetAllDecksResponseMock = (
  overrideResponse: Partial<GetAllDecks200> = {},
): GetAllDecks200 => ({
  decks: Array.from(
    { length: faker.number.int({ min: 1, max: 10 }) },
    (_, i) => i + 1,
  ).map(() => ({})),
  ...overrideResponse,
});

export const getCreateADeckResponseMock = (
  overrideResponse: Partial<CreateADeck201> = {},
): CreateADeck201 => ({ deck: {}, ...overrideResponse });

export const getSearchForDecksResponseMock = (
  overrideResponse: Partial<SearchForDecks200> = {},
): SearchForDecks200 => ({
  decks: Array.from(
    { length: faker.number.int({ min: 1, max: 10 }) },
    (_, i) => i + 1,
  ).map(() => ({})),
  ...overrideResponse,
});

export const getGetADeckResponseMock = (
  overrideResponse: Partial<GetADeck200> = {},
): GetADeck200 => ({ deck: {}, ...overrideResponse });

export const getUpdateADeckResponseMock = (
  overrideResponse: Partial<UpdateADeck200> = {},
): UpdateADeck200 => ({ deck: {}, ...overrideResponse });

export const getValidateDeckResponseMock = (
  overrideResponse: Partial<ValidateDeck200> = {},
): ValidateDeck200 => ({ deck_legalities: {}, ...overrideResponse });

export const getDeckStatisticsResponseMock = (
  overrideResponse: Partial<DeckStatistics200> = {},
): DeckStatistics200 => ({ deck_statistics: {}, ...overrideResponse });

export const getDeckTurnProbabilitiesResponseMock = (
  overrideResponse: Partial<DeckTurnProbabilities200> = {},
): DeckTurnProbabilities200 => ({
  turn_probabilities: {},
  ...overrideResponse,
});

export const getDeckToPdfResponseMock = (
  overrideResponse: Partial<DeckToPdf200> = {},
): DeckToPdf200 => ({ pdf: faker.string.alpha(20), ...overrideResponse });

export const getCloneADeckResponseMock = (
  overrideResponse: Partial<CloneADeck201> = {},
): CloneADeck201 => ({ deck: {}, ...overrideResponse });

export const getGetAllDecksMockHandler = (
  overrideResponse?:
    | GetAllDecks200
    | ((
        info: Parameters<Parameters<typeof http.get>[1]>[0],
      ) => Promise<GetAllDecks200> | GetAllDecks200),
) => {
  return http.get('*/decks', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getGetAllDecksResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getCreateADeckMockHandler = (
  overrideResponse?:
    | CreateADeck201
    | ((
        info: Parameters<Parameters<typeof http.post>[1]>[0],
      ) => Promise<CreateADeck201> | CreateADeck201),
) => {
  return http.post('*/decks', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getCreateADeckResponseMock(),
      ),
      { status: 201, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getSearchForDecksMockHandler = (
  overrideResponse?:
    | SearchForDecks200
    | ((
        info: Parameters<Parameters<typeof http.post>[1]>[0],
      ) => Promise<SearchForDecks200> | SearchForDecks200),
) => {
  return http.post('*/decks/search', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getSearchForDecksResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getDeleteAllDecksMockHandler = (
  overrideResponse?:
    | void
    | ((
        info: Parameters<Parameters<typeof http.delete>[1]>[0],
      ) => Promise<void> | void),
) => {
  return http.delete('*/decks/clear', async (info) => {
    await delay(1000);
    if (typeof overrideResponse === 'function') {
      await overrideResponse(info);
    }
    return new HttpResponse(null, { status: 204 });
  });
};

export const getImportADeckMockHandler = (
  overrideResponse?:
    | void
    | ((
        info: Parameters<Parameters<typeof http.get>[1]>[0],
      ) => Promise<void> | void),
) => {
  return http.get('*/decks/import', async (info) => {
    await delay(1000);
    if (typeof overrideResponse === 'function') {
      await overrideResponse(info);
    }
    return new HttpResponse(null, { status: 200 });
  });
};

export const getGetADeckMockHandler = (
  overrideResponse?:
    | GetADeck200
    | ((
        info: Parameters<Parameters<typeof http.get>[1]>[0],
      ) => Promise<GetADeck200> | GetADeck200),
) => {
  return http.get('*/decks/:uuid', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getGetADeckResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getDeleteADeckMockHandler = (
  overrideResponse?:
    | void
    | ((
        info: Parameters<Parameters<typeof http.delete>[1]>[0],
      ) => Promise<void> | void),
) => {
  return http.delete('*/decks/:uuid', async (info) => {
    await delay(1000);
    if (typeof overrideResponse === 'function') {
      await overrideResponse(info);
    }
    return new HttpResponse(null, { status: 204 });
  });
};

export const getUpdateADeckMockHandler = (
  overrideResponse?:
    | UpdateADeck200
    | ((
        info: Parameters<Parameters<typeof http.patch>[1]>[0],
      ) => Promise<UpdateADeck200> | UpdateADeck200),
) => {
  return http.patch('*/decks/:uuid', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getUpdateADeckResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getValidateDeckMockHandler = (
  overrideResponse?:
    | ValidateDeck200
    | ((
        info: Parameters<Parameters<typeof http.post>[1]>[0],
      ) => Promise<ValidateDeck200> | ValidateDeck200),
) => {
  return http.post('*/decks/:uuid/validate', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getValidateDeckResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getDeckStatisticsMockHandler = (
  overrideResponse?:
    | DeckStatistics200
    | ((
        info: Parameters<Parameters<typeof http.post>[1]>[0],
      ) => Promise<DeckStatistics200> | DeckStatistics200),
) => {
  return http.post('*/decks/:uuid/statistics', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getDeckStatisticsResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getDeckTurnProbabilitiesMockHandler = (
  overrideResponse?:
    | DeckTurnProbabilities200
    | ((
        info: Parameters<Parameters<typeof http.post>[1]>[0],
      ) => Promise<DeckTurnProbabilities200> | DeckTurnProbabilities200),
) => {
  return http.post('*/decks/:uuid/turn_probabilities', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getDeckTurnProbabilitiesResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getDeckToPdfMockHandler = (
  overrideResponse?:
    | DeckToPdf200
    | ((
        info: Parameters<Parameters<typeof http.post>[1]>[0],
      ) => Promise<DeckToPdf200> | DeckToPdf200),
) => {
  return http.post('*/decks/:uuid/to_pdf', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getDeckToPdfResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getCloneADeckMockHandler = (
  overrideResponse?:
    | CloneADeck201
    | ((
        info: Parameters<Parameters<typeof http.post>[1]>[0],
      ) => Promise<CloneADeck201> | CloneADeck201),
) => {
  return http.post('*/decks/:uuid/clone', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getCloneADeckResponseMock(),
      ),
      { status: 201, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getExportADeckMockHandler = (
  overrideResponse?:
    | void
    | ((
        info: Parameters<Parameters<typeof http.get>[1]>[0],
      ) => Promise<void> | void),
) => {
  return http.get('*/decks/:uuid/export', async (info) => {
    await delay(1000);
    if (typeof overrideResponse === 'function') {
      await overrideResponse(info);
    }
    return new HttpResponse(null, { status: 200 });
  });
};
export const getDeckMock = () => [
  getGetAllDecksMockHandler(),
  getCreateADeckMockHandler(),
  getSearchForDecksMockHandler(),
  getDeleteAllDecksMockHandler(),
  getImportADeckMockHandler(),
  getGetADeckMockHandler(),
  getDeleteADeckMockHandler(),
  getUpdateADeckMockHandler(),
  getValidateDeckMockHandler(),
  getDeckStatisticsMockHandler(),
  getDeckTurnProbabilitiesMockHandler(),
  getDeckToPdfMockHandler(),
  getCloneADeckMockHandler(),
  getExportADeckMockHandler(),
];
