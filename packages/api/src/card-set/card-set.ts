/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import { useQuery } from '@tanstack/react-query';
import type {
  QueryFunction,
  QueryKey,
  UseQueryOptions,
  UseQueryResult,
} from '@tanstack/react-query';

import axios from 'axios';
import type { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios';

import type {
  GetAllCardSetTypes200,
  GetAllCardSets200,
  GetAllCardSetsParams,
} from '.././model';

type AwaitedInput<T> = PromiseLike<T> | T;

type Awaited<O> = O extends AwaitedInput<infer T> ? T : never;

/**
 * Get all sets
 * @summary Get All Card Sets
 */
export const getAllCardSets = (
  params?: GetAllCardSetsParams,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<GetAllCardSets200>> => {
  return axios.get(`/card_sets`, {
    ...options,
    params: { ...params, ...options?.params },
  });
};

export const getGetAllCardSetsQueryKey = (params?: GetAllCardSetsParams) => {
  return [`/card_sets`, ...(params ? [params] : [])] as const;
};

export const getGetAllCardSetsQueryOptions = <
  TData = Awaited<ReturnType<typeof getAllCardSets>>,
  TError = AxiosError<unknown>,
>(
  params?: GetAllCardSetsParams,
  options?: {
    query?: UseQueryOptions<
      Awaited<ReturnType<typeof getAllCardSets>>,
      TError,
      TData
    >;
    axios?: AxiosRequestConfig;
  },
) => {
  const { query: queryOptions, axios: axiosOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetAllCardSetsQueryKey(params);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getAllCardSets>>> = ({
    signal,
  }) => getAllCardSets(params, { signal, ...axiosOptions });

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getAllCardSets>>,
    TError,
    TData
  > & { queryKey: QueryKey };
};

export type GetAllCardSetsQueryResult = NonNullable<
  Awaited<ReturnType<typeof getAllCardSets>>
>;
export type GetAllCardSetsQueryError = AxiosError<unknown>;

/**
 * @summary Get All Card Sets
 */

export function useGetAllCardSets<
  TData = Awaited<ReturnType<typeof getAllCardSets>>,
  TError = AxiosError<unknown>,
>(
  params?: GetAllCardSetsParams,
  options?: {
    query?: UseQueryOptions<
      Awaited<ReturnType<typeof getAllCardSets>>,
      TError,
      TData
    >;
    axios?: AxiosRequestConfig;
  },
): UseQueryResult<TData, TError> & { queryKey: QueryKey } {
  const queryOptions = getGetAllCardSetsQueryOptions(params, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: QueryKey;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Get all set types
 * @summary Get All Card Set Types
 */
export const getAllCardSetTypes = (
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<GetAllCardSetTypes200>> => {
  return axios.get(`/card_sets/type`, options);
};

export const getGetAllCardSetTypesQueryKey = () => {
  return [`/card_sets/type`] as const;
};

export const getGetAllCardSetTypesQueryOptions = <
  TData = Awaited<ReturnType<typeof getAllCardSetTypes>>,
  TError = AxiosError<unknown>,
>(options?: {
  query?: UseQueryOptions<
    Awaited<ReturnType<typeof getAllCardSetTypes>>,
    TError,
    TData
  >;
  axios?: AxiosRequestConfig;
}) => {
  const { query: queryOptions, axios: axiosOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetAllCardSetTypesQueryKey();

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof getAllCardSetTypes>>
  > = ({ signal }) => getAllCardSetTypes({ signal, ...axiosOptions });

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getAllCardSetTypes>>,
    TError,
    TData
  > & { queryKey: QueryKey };
};

export type GetAllCardSetTypesQueryResult = NonNullable<
  Awaited<ReturnType<typeof getAllCardSetTypes>>
>;
export type GetAllCardSetTypesQueryError = AxiosError<unknown>;

/**
 * @summary Get All Card Set Types
 */

export function useGetAllCardSetTypes<
  TData = Awaited<ReturnType<typeof getAllCardSetTypes>>,
  TError = AxiosError<unknown>,
>(options?: {
  query?: UseQueryOptions<
    Awaited<ReturnType<typeof getAllCardSetTypes>>,
    TError,
    TData
  >;
  axios?: AxiosRequestConfig;
}): UseQueryResult<TData, TError> & { queryKey: QueryKey } {
  const queryOptions = getGetAllCardSetTypesQueryOptions(options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: QueryKey;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}
