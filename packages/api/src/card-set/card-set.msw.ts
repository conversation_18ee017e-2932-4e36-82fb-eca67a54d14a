/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import { faker } from '@faker-js/faker';

import { HttpResponse, delay, http } from 'msw';

import type { GetAllCardSetTypes200, GetAllCardSets200 } from '.././model';

export const getGetAllCardSetsResponseMock = (
  overrideResponse: Partial<GetAllCardSets200> = {},
): GetAllCardSets200 => ({
  card_sets: Array.from(
    { length: faker.number.int({ min: 1, max: 10 }) },
    (_, i) => i + 1,
  ).map(() => ({})),
  ...overrideResponse,
});

export const getGetAllCardSetTypesResponseMock = (
  overrideResponse: Partial<GetAllCardSetTypes200> = {},
): GetAllCardSetTypes200 => ({
  types: Array.from(
    { length: faker.number.int({ min: 1, max: 10 }) },
    (_, i) => i + 1,
  ).map(() => faker.string.alpha(20)),
  ...overrideResponse,
});

export const getGetAllCardSetsMockHandler = (
  overrideResponse?:
    | GetAllCardSets200
    | ((
        info: Parameters<Parameters<typeof http.get>[1]>[0],
      ) => Promise<GetAllCardSets200> | GetAllCardSets200),
) => {
  return http.get('*/card_sets', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getGetAllCardSetsResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getGetAllCardSetTypesMockHandler = (
  overrideResponse?:
    | GetAllCardSetTypes200
    | ((
        info: Parameters<Parameters<typeof http.get>[1]>[0],
      ) => Promise<GetAllCardSetTypes200> | GetAllCardSetTypes200),
) => {
  return http.get('*/card_sets/type', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getGetAllCardSetTypesResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};
export const getCardSetMock = () => [
  getGetAllCardSetsMockHandler(),
  getGetAllCardSetTypesMockHandler(),
];
