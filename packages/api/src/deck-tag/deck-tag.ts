/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import { useMutation, useQuery } from '@tanstack/react-query';
import type {
  MutationFunction,
  QueryFunction,
  QueryKey,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult,
} from '@tanstack/react-query';

import axios from 'axios';
import type { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios';

import type {
  AddTagToDeck200,
  AddTagToDeckBody,
  GetAllTags200,
  RemoveTagFromDeckBody,
} from '.././model';

type AwaitedInput<T> = PromiseLike<T> | T;

type Awaited<O> = O extends AwaitedInput<infer T> ? T : never;

/**
 * Adds a tag to a specified deck, If the tag does not exist, it is created.
 * @summary Add tag to deck
 */
export const addTagToDeck = (
  uuid: string,
  addTagToDeckBody: AddTagToDeckBody,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<AddTagToDeck200>> => {
  return axios.post(`/decks/${uuid}/deck_tags`, addTagToDeckBody, options);
};

export const getAddTagToDeckMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof addTagToDeck>>,
    TError,
    { uuid: string; data: AddTagToDeckBody },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof addTagToDeck>>,
  TError,
  { uuid: string; data: AddTagToDeckBody },
  TContext
> => {
  const mutationKey = ['addTagToDeck'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof addTagToDeck>>,
    { uuid: string; data: AddTagToDeckBody }
  > = (props) => {
    const { uuid, data } = props ?? {};

    return addTagToDeck(uuid, data, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type AddTagToDeckMutationResult = NonNullable<
  Awaited<ReturnType<typeof addTagToDeck>>
>;
export type AddTagToDeckMutationBody = AddTagToDeckBody;
export type AddTagToDeckMutationError = AxiosError<unknown>;

/**
 * @summary Add tag to deck
 */
export const useAddTagToDeck = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof addTagToDeck>>,
    TError,
    { uuid: string; data: AddTagToDeckBody },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof addTagToDeck>>,
  TError,
  { uuid: string; data: AddTagToDeckBody },
  TContext
> => {
  const mutationOptions = getAddTagToDeckMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * @summary Remove tag from deck
 */
export const removeTagFromDeck = (
  uuid: string,
  removeTagFromDeckBody: RemoveTagFromDeckBody,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<void>> => {
  return axios.delete(`/decks/${uuid}/deck_tags`, {
    data: removeTagFromDeckBody,
    ...options,
  });
};

export const getRemoveTagFromDeckMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof removeTagFromDeck>>,
    TError,
    { uuid: string; data: RemoveTagFromDeckBody },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof removeTagFromDeck>>,
  TError,
  { uuid: string; data: RemoveTagFromDeckBody },
  TContext
> => {
  const mutationKey = ['removeTagFromDeck'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof removeTagFromDeck>>,
    { uuid: string; data: RemoveTagFromDeckBody }
  > = (props) => {
    const { uuid, data } = props ?? {};

    return removeTagFromDeck(uuid, data, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type RemoveTagFromDeckMutationResult = NonNullable<
  Awaited<ReturnType<typeof removeTagFromDeck>>
>;
export type RemoveTagFromDeckMutationBody = RemoveTagFromDeckBody;
export type RemoveTagFromDeckMutationError = AxiosError<unknown>;

/**
 * @summary Remove tag from deck
 */
export const useRemoveTagFromDeck = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof removeTagFromDeck>>,
    TError,
    { uuid: string; data: RemoveTagFromDeckBody },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof removeTagFromDeck>>,
  TError,
  { uuid: string; data: RemoveTagFromDeckBody },
  TContext
> => {
  const mutationOptions = getRemoveTagFromDeckMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Get the 10 tags used by all decks
+ query (string, optional) - partial query string to match against all tags
 * @summary Get All Tags
 */
export const getAllTags = (
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<GetAllTags200>> => {
  return axios.get(`/deck_tags`, options);
};

export const getGetAllTagsQueryKey = () => {
  return [`/deck_tags`] as const;
};

export const getGetAllTagsQueryOptions = <
  TData = Awaited<ReturnType<typeof getAllTags>>,
  TError = AxiosError<unknown>,
>(options?: {
  query?: UseQueryOptions<
    Awaited<ReturnType<typeof getAllTags>>,
    TError,
    TData
  >;
  axios?: AxiosRequestConfig;
}) => {
  const { query: queryOptions, axios: axiosOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetAllTagsQueryKey();

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getAllTags>>> = ({
    signal,
  }) => getAllTags({ signal, ...axiosOptions });

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getAllTags>>,
    TError,
    TData
  > & { queryKey: QueryKey };
};

export type GetAllTagsQueryResult = NonNullable<
  Awaited<ReturnType<typeof getAllTags>>
>;
export type GetAllTagsQueryError = AxiosError<unknown>;

/**
 * @summary Get All Tags
 */

export function useGetAllTags<
  TData = Awaited<ReturnType<typeof getAllTags>>,
  TError = AxiosError<unknown>,
>(options?: {
  query?: UseQueryOptions<
    Awaited<ReturnType<typeof getAllTags>>,
    TError,
    TData
  >;
  axios?: AxiosRequestConfig;
}): UseQueryResult<TData, TError> & { queryKey: QueryKey } {
  const queryOptions = getGetAllTagsQueryOptions(options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: QueryKey;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}
