/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import { faker } from '@faker-js/faker';

import { HttpResponse, delay, http } from 'msw';

import type { AddTagToDeck200, GetAllTags200 } from '.././model';

export const getAddTagToDeckResponseMock = (
  overrideResponse: Partial<AddTagToDeck200> = {},
): AddTagToDeck200 => ({ name: faker.string.alpha(20), ...overrideResponse });

export const getGetAllTagsResponseMock = (
  overrideResponse: Partial<GetAllTags200> = {},
): GetAllTags200 => ({
  deck_tags: Array.from(
    { length: faker.number.int({ min: 1, max: 10 }) },
    (_, i) => i + 1,
  ).map(() => ({})),
  ...overrideResponse,
});

export const getAddTagToDeckMockHandler = (
  overrideResponse?:
    | AddTagToDeck200
    | ((
        info: Parameters<Parameters<typeof http.post>[1]>[0],
      ) => Promise<AddTagToDeck200> | AddTagToDeck200),
) => {
  return http.post('*/decks/:uuid/deck_tags', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getAddTagToDeckResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getRemoveTagFromDeckMockHandler = (
  overrideResponse?:
    | void
    | ((
        info: Parameters<Parameters<typeof http.delete>[1]>[0],
      ) => Promise<void> | void),
) => {
  return http.delete('*/decks/:uuid/deck_tags', async (info) => {
    await delay(1000);
    if (typeof overrideResponse === 'function') {
      await overrideResponse(info);
    }
    return new HttpResponse(null, { status: 204 });
  });
};

export const getGetAllTagsMockHandler = (
  overrideResponse?:
    | GetAllTags200
    | ((
        info: Parameters<Parameters<typeof http.get>[1]>[0],
      ) => Promise<GetAllTags200> | GetAllTags200),
) => {
  return http.get('*/deck_tags', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getGetAllTagsResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};
export const getDeckTagMock = () => [
  getAddTagToDeckMockHandler(),
  getRemoveTagFromDeckMockHandler(),
  getGetAllTagsMockHandler(),
];
