/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import { useMutation } from '@tanstack/react-query';
import type {
  MutationFunction,
  UseMutationOptions,
  UseMutationResult,
} from '@tanstack/react-query';

import axios from 'axios';
import type { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios';

import type {
  AddMultipleCardsToDeck200,
  AddMultipleCardsToDeckParams,
  CreateDeckBoard200,
  CreateDeckBoardBody,
  UpdateDeckBoard200,
  UpdateDeckBoardBody,
} from '.././model';

type AwaitedInput<T> = PromiseLike<T> | T;

type Awaited<O> = O extends AwaitedInput<infer T> ? T : never;

/**
 * Adds a board to the specified deck. Name of the board must be unique per deck
 * @summary Create Deck Board
 */
export const createDeckBoard = (
  uuid: string,
  createDeckBoardBody: CreateDeckBoardBody,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<CreateDeckBoard200>> => {
  return axios.post(`/decks/${uuid}/deck_boards`, createDeckBoardBody, options);
};

export const getCreateDeckBoardMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof createDeckBoard>>,
    TError,
    { uuid: string; data: CreateDeckBoardBody },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof createDeckBoard>>,
  TError,
  { uuid: string; data: CreateDeckBoardBody },
  TContext
> => {
  const mutationKey = ['createDeckBoard'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof createDeckBoard>>,
    { uuid: string; data: CreateDeckBoardBody }
  > = (props) => {
    const { uuid, data } = props ?? {};

    return createDeckBoard(uuid, data, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type CreateDeckBoardMutationResult = NonNullable<
  Awaited<ReturnType<typeof createDeckBoard>>
>;
export type CreateDeckBoardMutationBody = CreateDeckBoardBody;
export type CreateDeckBoardMutationError = AxiosError<unknown>;

/**
 * @summary Create Deck Board
 */
export const useCreateDeckBoard = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof createDeckBoard>>,
    TError,
    { uuid: string; data: CreateDeckBoardBody },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof createDeckBoard>>,
  TError,
  { uuid: string; data: CreateDeckBoardBody },
  TContext
> => {
  const mutationOptions = getCreateDeckBoardMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Deletes the deck board and any cards which are on it.
 * @summary Delete Deck Board
 */
export const deleteDeckBoard = (
  uuid: string,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<void>> => {
  return axios.delete(`/decks/${uuid}/deck_boards/:id`, options);
};

export const getDeleteDeckBoardMutationOptions = <
  TError = AxiosError<void>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteDeckBoard>>,
    TError,
    { uuid: string },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof deleteDeckBoard>>,
  TError,
  { uuid: string },
  TContext
> => {
  const mutationKey = ['deleteDeckBoard'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof deleteDeckBoard>>,
    { uuid: string }
  > = (props) => {
    const { uuid } = props ?? {};

    return deleteDeckBoard(uuid, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type DeleteDeckBoardMutationResult = NonNullable<
  Awaited<ReturnType<typeof deleteDeckBoard>>
>;

export type DeleteDeckBoardMutationError = AxiosError<void>;

/**
 * @summary Delete Deck Board
 */
export const useDeleteDeckBoard = <
  TError = AxiosError<void>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteDeckBoard>>,
    TError,
    { uuid: string },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof deleteDeckBoard>>,
  TError,
  { uuid: string },
  TContext
> => {
  const mutationOptions = getDeleteDeckBoardMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Updates the attributes of the deck
 * @summary Update Deck Board
 */
export const updateDeckBoard = (
  uuid: string,
  updateDeckBoardBody: UpdateDeckBoardBody,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<UpdateDeckBoard200>> => {
  return axios.patch(
    `/decks/${uuid}/deck_boards/:id`,
    updateDeckBoardBody,
    options,
  );
};

export const getUpdateDeckBoardMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof updateDeckBoard>>,
    TError,
    { uuid: string; data: UpdateDeckBoardBody },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof updateDeckBoard>>,
  TError,
  { uuid: string; data: UpdateDeckBoardBody },
  TContext
> => {
  const mutationKey = ['updateDeckBoard'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof updateDeckBoard>>,
    { uuid: string; data: UpdateDeckBoardBody }
  > = (props) => {
    const { uuid, data } = props ?? {};

    return updateDeckBoard(uuid, data, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type UpdateDeckBoardMutationResult = NonNullable<
  Awaited<ReturnType<typeof updateDeckBoard>>
>;
export type UpdateDeckBoardMutationBody = UpdateDeckBoardBody;
export type UpdateDeckBoardMutationError = AxiosError<unknown>;

/**
 * @summary Update Deck Board
 */
export const useUpdateDeckBoard = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof updateDeckBoard>>,
    TError,
    { uuid: string; data: UpdateDeckBoardBody },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof updateDeckBoard>>,
  TError,
  { uuid: string; data: UpdateDeckBoardBody },
  TContext
> => {
  const mutationOptions = getUpdateDeckBoardMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Add multiple cards to a deck board
 * @summary Add Multiple Cards To A Deck Board
 */
export const addMultipleCardsToDeck = (
  uuid: string,
  params: AddMultipleCardsToDeckParams,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<AddMultipleCardsToDeck200>> => {
  return axios.post(`/decks/${uuid}/deck_boards/:id/add_cards`, undefined, {
    ...options,
    params: { ...params, ...options?.params },
  });
};

export const getAddMultipleCardsToDeckMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof addMultipleCardsToDeck>>,
    TError,
    { uuid: string; params: AddMultipleCardsToDeckParams },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof addMultipleCardsToDeck>>,
  TError,
  { uuid: string; params: AddMultipleCardsToDeckParams },
  TContext
> => {
  const mutationKey = ['addMultipleCardsToDeck'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof addMultipleCardsToDeck>>,
    { uuid: string; params: AddMultipleCardsToDeckParams }
  > = (props) => {
    const { uuid, params } = props ?? {};

    return addMultipleCardsToDeck(uuid, params, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type AddMultipleCardsToDeckMutationResult = NonNullable<
  Awaited<ReturnType<typeof addMultipleCardsToDeck>>
>;

export type AddMultipleCardsToDeckMutationError = AxiosError<unknown>;

/**
 * @summary Add Multiple Cards To A Deck Board
 */
export const useAddMultipleCardsToDeck = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof addMultipleCardsToDeck>>,
    TError,
    { uuid: string; params: AddMultipleCardsToDeckParams },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof addMultipleCardsToDeck>>,
  TError,
  { uuid: string; params: AddMultipleCardsToDeckParams },
  TContext
> => {
  const mutationOptions = getAddMultipleCardsToDeckMutationOptions(options);

  return useMutation(mutationOptions);
};
