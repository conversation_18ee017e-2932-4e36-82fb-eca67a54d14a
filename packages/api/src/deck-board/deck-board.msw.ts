/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import { HttpResponse, delay, http } from 'msw';

import type {
  AddMultipleCardsToDeck200,
  CreateDeckBoard200,
  UpdateDeckBoard200,
} from '.././model';

export const getCreateDeckBoardResponseMock = (
  overrideResponse: Partial<CreateDeckBoard200> = {},
): CreateDeckBoard200 => ({ deck_board: {}, ...overrideResponse });

export const getUpdateDeckBoardResponseMock = (
  overrideResponse: Partial<UpdateDeckBoard200> = {},
): UpdateDeckBoard200 => ({ deck_board: {}, ...overrideResponse });

export const getAddMultipleCardsToDeckResponseMock = (
  overrideResponse: Partial<AddMultipleCardsToDeck200> = {},
): AddMultipleCardsToDeck200 => ({ cards: {}, ...overrideResponse });

export const getCreateDeckBoardMockHandler = (
  overrideResponse?:
    | CreateDeckBoard200
    | ((
        info: Parameters<Parameters<typeof http.post>[1]>[0],
      ) => Promise<CreateDeckBoard200> | CreateDeckBoard200),
) => {
  return http.post('*/decks/:uuid/deck_boards', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getCreateDeckBoardResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getDeleteDeckBoardMockHandler = (
  overrideResponse?:
    | void
    | ((
        info: Parameters<Parameters<typeof http.delete>[1]>[0],
      ) => Promise<void> | void),
) => {
  return http.delete('*/decks/:uuid/deck_boards/:id', async (info) => {
    await delay(1000);
    if (typeof overrideResponse === 'function') {
      await overrideResponse(info);
    }
    return new HttpResponse(null, { status: 204 });
  });
};

export const getUpdateDeckBoardMockHandler = (
  overrideResponse?:
    | UpdateDeckBoard200
    | ((
        info: Parameters<Parameters<typeof http.patch>[1]>[0],
      ) => Promise<UpdateDeckBoard200> | UpdateDeckBoard200),
) => {
  return http.patch('*/decks/:uuid/deck_boards/:id', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getUpdateDeckBoardResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getAddMultipleCardsToDeckMockHandler = (
  overrideResponse?:
    | AddMultipleCardsToDeck200
    | ((
        info: Parameters<Parameters<typeof http.post>[1]>[0],
      ) => Promise<AddMultipleCardsToDeck200> | AddMultipleCardsToDeck200),
) => {
  return http.post('*/decks/:uuid/deck_boards/:id/add_cards', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getAddMultipleCardsToDeckResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};
export const getDeckBoardMock = () => [
  getCreateDeckBoardMockHandler(),
  getDeleteDeckBoardMockHandler(),
  getUpdateDeckBoardMockHandler(),
  getAddMultipleCardsToDeckMockHandler(),
];
