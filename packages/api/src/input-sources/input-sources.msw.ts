/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import { faker } from '@faker-js/faker';

import { HttpResponse, delay, http } from 'msw';

import type {
  InputSourcesCreate200,
  InputSourcesIndex200,
  InputSourcesShow200,
  InputSourcesUpdate200,
} from '.././model';

export const getInputSourcesIndexResponseMock = (
  overrideResponse: Partial<InputSourcesIndex200> = {},
): InputSourcesIndex200 => ({
  input_sources: Array.from(
    { length: faker.number.int({ min: 1, max: 10 }) },
    (_, i) => i + 1,
  ).map(() => ({})),
  ...overrideResponse,
});

export const getInputSourcesCreateResponseMock = (
  overrideResponse: Partial<InputSourcesCreate200> = {},
): InputSourcesCreate200 => ({ input_source: {}, ...overrideResponse });

export const getInputSourcesShowResponseMock = (
  overrideResponse: Partial<InputSourcesShow200> = {},
): InputSourcesShow200 => ({ input_source: {}, ...overrideResponse });

export const getInputSourcesUpdateResponseMock = (
  overrideResponse: Partial<InputSourcesUpdate200> = {},
): InputSourcesUpdate200 => ({ input_source: {}, ...overrideResponse });

export const getInputSourcesIndexMockHandler = (
  overrideResponse?:
    | InputSourcesIndex200
    | ((
        info: Parameters<Parameters<typeof http.get>[1]>[0],
      ) => Promise<InputSourcesIndex200> | InputSourcesIndex200),
) => {
  return http.get('*/input_sources', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getInputSourcesIndexResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getInputSourcesCreateMockHandler = (
  overrideResponse?:
    | InputSourcesCreate200
    | ((
        info: Parameters<Parameters<typeof http.post>[1]>[0],
      ) => Promise<InputSourcesCreate200> | InputSourcesCreate200),
) => {
  return http.post('*/input_sources', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getInputSourcesCreateResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getInputSourcesShowMockHandler = (
  overrideResponse?:
    | InputSourcesShow200
    | ((
        info: Parameters<Parameters<typeof http.get>[1]>[0],
      ) => Promise<InputSourcesShow200> | InputSourcesShow200),
) => {
  return http.get('*/input_sources/:uuid', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getInputSourcesShowResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};

export const getInputSourcesUpdateMockHandler = (
  overrideResponse?:
    | InputSourcesUpdate200
    | ((
        info: Parameters<Parameters<typeof http.patch>[1]>[0],
      ) => Promise<InputSourcesUpdate200> | InputSourcesUpdate200),
) => {
  return http.patch('*/input_sources/:uuid', async (info) => {
    await delay(1000);

    return new HttpResponse(
      JSON.stringify(
        overrideResponse !== undefined
          ? typeof overrideResponse === 'function'
            ? await overrideResponse(info)
            : overrideResponse
          : getInputSourcesUpdateResponseMock(),
      ),
      { status: 200, headers: { 'Content-Type': 'application/json' } },
    );
  });
};
export const getInputSourcesMock = () => [
  getInputSourcesIndexMockHandler(),
  getInputSourcesCreateMockHandler(),
  getInputSourcesShowMockHandler(),
  getInputSourcesUpdateMockHandler(),
];
