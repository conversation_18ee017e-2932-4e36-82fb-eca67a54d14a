/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * CardCastle API v3
 * API to interact with the CardCastle Platform
 * OpenAPI spec version: 1.0
 */
import { useMutation, useQuery } from '@tanstack/react-query';
import type {
  MutationFunction,
  QueryFunction,
  QueryKey,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult,
} from '@tanstack/react-query';

import axios from 'axios';
import type { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios';

import type {
  InputSourcesCreate200,
  InputSourcesCreateParams,
  InputSourcesIndex200,
  InputSourcesIndexParams,
  InputSourcesShow200,
  InputSourcesUpdate200,
  InputSourcesUpdateParams,
} from '.././model';

type AwaitedInput<T> = PromiseLike<T> | T;

type Awaited<O> = O extends AwaitedInput<infer T> ? T : never;

/**
 * Get list of input sources for authenticated user
 * @summary Get list of input sources
 */
export const inputSourcesIndex = (
  params?: InputSourcesIndexParams,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<InputSourcesIndex200>> => {
  return axios.get(`/input_sources`, {
    ...options,
    params: { ...params, ...options?.params },
  });
};

export const getInputSourcesIndexQueryKey = (
  params?: InputSourcesIndexParams,
) => {
  return [`/input_sources`, ...(params ? [params] : [])] as const;
};

export const getInputSourcesIndexQueryOptions = <
  TData = Awaited<ReturnType<typeof inputSourcesIndex>>,
  TError = AxiosError<unknown>,
>(
  params?: InputSourcesIndexParams,
  options?: {
    query?: UseQueryOptions<
      Awaited<ReturnType<typeof inputSourcesIndex>>,
      TError,
      TData
    >;
    axios?: AxiosRequestConfig;
  },
) => {
  const { query: queryOptions, axios: axiosOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ?? getInputSourcesIndexQueryKey(params);

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof inputSourcesIndex>>
  > = ({ signal }) => inputSourcesIndex(params, { signal, ...axiosOptions });

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof inputSourcesIndex>>,
    TError,
    TData
  > & { queryKey: QueryKey };
};

export type InputSourcesIndexQueryResult = NonNullable<
  Awaited<ReturnType<typeof inputSourcesIndex>>
>;
export type InputSourcesIndexQueryError = AxiosError<unknown>;

/**
 * @summary Get list of input sources
 */

export function useInputSourcesIndex<
  TData = Awaited<ReturnType<typeof inputSourcesIndex>>,
  TError = AxiosError<unknown>,
>(
  params?: InputSourcesIndexParams,
  options?: {
    query?: UseQueryOptions<
      Awaited<ReturnType<typeof inputSourcesIndex>>,
      TError,
      TData
    >;
    axios?: AxiosRequestConfig;
  },
): UseQueryResult<TData, TError> & { queryKey: QueryKey } {
  const queryOptions = getInputSourcesIndexQueryOptions(params, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: QueryKey;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Create new input source for authenticated user
 * @summary Create new input source
 */
export const inputSourcesCreate = (
  params: InputSourcesCreateParams,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<InputSourcesCreate200>> => {
  return axios.post(`/input_sources`, undefined, {
    ...options,
    params: { ...params, ...options?.params },
  });
};

export const getInputSourcesCreateMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof inputSourcesCreate>>,
    TError,
    { params: InputSourcesCreateParams },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof inputSourcesCreate>>,
  TError,
  { params: InputSourcesCreateParams },
  TContext
> => {
  const mutationKey = ['inputSourcesCreate'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof inputSourcesCreate>>,
    { params: InputSourcesCreateParams }
  > = (props) => {
    const { params } = props ?? {};

    return inputSourcesCreate(params, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type InputSourcesCreateMutationResult = NonNullable<
  Awaited<ReturnType<typeof inputSourcesCreate>>
>;

export type InputSourcesCreateMutationError = AxiosError<unknown>;

/**
 * @summary Create new input source
 */
export const useInputSourcesCreate = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof inputSourcesCreate>>,
    TError,
    { params: InputSourcesCreateParams },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof inputSourcesCreate>>,
  TError,
  { params: InputSourcesCreateParams },
  TContext
> => {
  const mutationOptions = getInputSourcesCreateMutationOptions(options);

  return useMutation(mutationOptions);
};
/**
 * Get specific input source for authenticated user
 * @summary Get input source
 */
export const inputSourcesShow = (
  uuid: string,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<InputSourcesShow200>> => {
  return axios.get(`/input_sources/${uuid}`, options);
};

export const getInputSourcesShowQueryKey = (uuid: string) => {
  return [`/input_sources/${uuid}`] as const;
};

export const getInputSourcesShowQueryOptions = <
  TData = Awaited<ReturnType<typeof inputSourcesShow>>,
  TError = AxiosError<unknown>,
>(
  uuid: string,
  options?: {
    query?: UseQueryOptions<
      Awaited<ReturnType<typeof inputSourcesShow>>,
      TError,
      TData
    >;
    axios?: AxiosRequestConfig;
  },
) => {
  const { query: queryOptions, axios: axiosOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getInputSourcesShowQueryKey(uuid);

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof inputSourcesShow>>
  > = ({ signal }) => inputSourcesShow(uuid, { signal, ...axiosOptions });

  return {
    queryKey,
    queryFn,
    enabled: !!uuid,
    ...queryOptions,
  } as UseQueryOptions<
    Awaited<ReturnType<typeof inputSourcesShow>>,
    TError,
    TData
  > & { queryKey: QueryKey };
};

export type InputSourcesShowQueryResult = NonNullable<
  Awaited<ReturnType<typeof inputSourcesShow>>
>;
export type InputSourcesShowQueryError = AxiosError<unknown>;

/**
 * @summary Get input source
 */

export function useInputSourcesShow<
  TData = Awaited<ReturnType<typeof inputSourcesShow>>,
  TError = AxiosError<unknown>,
>(
  uuid: string,
  options?: {
    query?: UseQueryOptions<
      Awaited<ReturnType<typeof inputSourcesShow>>,
      TError,
      TData
    >;
    axios?: AxiosRequestConfig;
  },
): UseQueryResult<TData, TError> & { queryKey: QueryKey } {
  const queryOptions = getInputSourcesShowQueryOptions(uuid, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & {
    queryKey: QueryKey;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Update name of specific input source owned by authenticated user
 * @summary Update input source name
 */
export const inputSourcesUpdate = (
  uuid: string,
  params: InputSourcesUpdateParams,
  options?: AxiosRequestConfig,
): Promise<AxiosResponse<InputSourcesUpdate200>> => {
  return axios.patch(`/input_sources/${uuid}`, undefined, {
    ...options,
    params: { ...params, ...options?.params },
  });
};

export const getInputSourcesUpdateMutationOptions = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof inputSourcesUpdate>>,
    TError,
    { uuid: string; params: InputSourcesUpdateParams },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationOptions<
  Awaited<ReturnType<typeof inputSourcesUpdate>>,
  TError,
  { uuid: string; params: InputSourcesUpdateParams },
  TContext
> => {
  const mutationKey = ['inputSourcesUpdate'];
  const { mutation: mutationOptions, axios: axiosOptions } = options
    ? options.mutation &&
      'mutationKey' in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, axios: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof inputSourcesUpdate>>,
    { uuid: string; params: InputSourcesUpdateParams }
  > = (props) => {
    const { uuid, params } = props ?? {};

    return inputSourcesUpdate(uuid, params, axiosOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type InputSourcesUpdateMutationResult = NonNullable<
  Awaited<ReturnType<typeof inputSourcesUpdate>>
>;

export type InputSourcesUpdateMutationError = AxiosError<unknown>;

/**
 * @summary Update input source name
 */
export const useInputSourcesUpdate = <
  TError = AxiosError<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof inputSourcesUpdate>>,
    TError,
    { uuid: string; params: InputSourcesUpdateParams },
    TContext
  >;
  axios?: AxiosRequestConfig;
}): UseMutationResult<
  Awaited<ReturnType<typeof inputSourcesUpdate>>,
  TError,
  { uuid: string; params: InputSourcesUpdateParams },
  TContext
> => {
  const mutationOptions = getInputSourcesUpdateMutationOptions(options);

  return useMutation(mutationOptions);
};
